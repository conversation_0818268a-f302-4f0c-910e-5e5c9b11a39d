// JavaScript para el formulario de prospectos InteletGroup

// Variables globales
let currentUserName = '';
let currentUserId = 0;

// Inicialización cuando el DOM está listo
document.addEventListener('DOMContentLoaded', function() {
    initializeInteletGroupProspectForm();
});

// Función principal de inicialización
function initializeInteletGroupProspectForm() {
    // Obtener información del usuario actual
    if (typeof window.currentUserName !== 'undefined') {
        currentUserName = window.currentUserName;
    }
    if (typeof window.currentUserId !== 'undefined') {
        currentUserId = window.currentUserId;
    }

    // Configurar eventos
    setupFormEvents();
    setupValidation();
    setupFileUpload();
}

// Configurar eventos del formulario
function setupFormEvents() {
    // Botón de guardar
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    if (saveBtn) {
        saveBtn.addEventListener('click', handleSaveProspect);
    }

    // Botón de llenar datos de prueba
    const fillTestBtn = document.getElementById('fillTestDataBtn');
    if (fillTestBtn) {
        fillTestBtn.addEventListener('click', fillTestData);
    }

    // Evento cuando se abre el modal
    const modal = document.getElementById('inteletGroupProspectModal');
    if (modal) {
        modal.addEventListener('show.bs.modal', function() {
            resetForm();
            populateExecutiveName();
        });
    }
}

// Configurar validación en tiempo real
function setupValidation() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return;

    // RUT validation
    const rutField = document.getElementById('rut_cliente');
    if (rutField) {
        rutField.addEventListener('input', function() {
            validateRUT(this);
        });
    }

    // Razón Social validation
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField) {
        razonSocialField.addEventListener('input', function() {
            validateRazonSocial(this);
        });
    }

    // Teléfono validation
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField) {
        telefonoField.addEventListener('input', function() {
            validateTelefono(this);
        });
    }

    // Email validation
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('input', function() {
            validateEmail(this);
        });
    }

    // Validación general para campos requeridos
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateRequired(this);
        });
    });
}

// Configurar subida de archivos
function setupFileUpload() {
    const fileInput = document.getElementById('documentos');
    if (!fileInput) return;

    fileInput.addEventListener('change', function() {
        validateFiles(this);
    });
}

// Validar RUT
function validateRUT(field) {
    const value = field.value.trim();
    const rutPattern = /^\d{7,8}-[\dkK]$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!rutPattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato inválido. Use: ********-9');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar Razón Social
function validateRazonSocial(field) {
    const value = field.value.trim();
    const pattern = /^[A-Z\s]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo letras mayúsculas y espacios');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar teléfono
function validateTelefono(field) {
    const value = field.value.trim();
    const pattern = /^\d{9,15}$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo números, mínimo 9 dígitos');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar email
function validateEmail(field) {
    const value = field.value.trim();
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato de email inválido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar campos requeridos
function validateRequired(field) {
    const value = field.value.trim();
    
    if (value === '') {
        setFieldState(field, 'invalid', 'Este campo es requerido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar archivos
function validateFiles(fileInput) {
    const files = fileInput.files;
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    let isValid = true;
    let errorMessage = '';
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        if (file.size > maxSize) {
            isValid = false;
            errorMessage = `El archivo ${file.name} excede el tamaño máximo de 5MB`;
            break;
        }
        
        if (!allowedTypes.includes(file.type)) {
            isValid = false;
            errorMessage = `El archivo ${file.name} no tiene un formato permitido`;
            break;
        }
    }
    
    if (!isValid) {
        setFieldState(fileInput, 'invalid', errorMessage);
        fileInput.value = '';
    } else {
        setFieldState(fileInput, 'valid');
    }
    
    return isValid;
}

// Establecer estado del campo
function setFieldState(field, state, message = '') {
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    // Limpiar clases anteriores
    field.classList.remove('is-valid', 'is-invalid');
    
    switch (state) {
        case 'valid':
            field.classList.add('is-valid');
            if (feedback) feedback.textContent = '';
            break;
        case 'invalid':
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = message;
            break;
        case 'neutral':
            if (feedback) feedback.textContent = '';
            break;
    }
}

// Poblar nombre del ejecutivo
function populateExecutiveName() {
    const nameField = document.getElementById('nombre_ejecutivo');
    if (nameField && currentUserName) {
        nameField.value = currentUserName;
    }
}

// Función para generar un RUT único para pruebas
function generateUniqueTestRut() {
    // Generar un número aleatorio entre 10000000 y 99999999
    const randomNumber = Math.floor(Math.random() * (99999999 - 10000000 + 1)) + 10000000;

    // Calcular dígito verificador
    let sum = 0;
    let multiplier = 2;
    const rutString = randomNumber.toString();

    for (let i = rutString.length - 1; i >= 0; i--) {
        sum += parseInt(rutString[i]) * multiplier;
        multiplier = multiplier === 7 ? 2 : multiplier + 1;
    }

    const remainder = sum % 11;
    const dv = remainder < 2 ? remainder.toString() : (11 - remainder === 10 ? 'K' : (11 - remainder).toString());

    return `${randomNumber}-${dv}`;
}

// Llenar datos de prueba
function fillTestData() {
    const uniqueRut = generateUniqueTestRut();
    const testData = {
        rut_cliente: uniqueRut,
        razon_social: 'EMPRESA EJEMPLO LTDA',
        rubro: 'COMERCIO AL POR MENOR',
        direccion_comercial: 'AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO',
        telefono_celular: '*********',
        email: '<EMAIL>',
        numero_pos: 'POS123456',
        tipo_cuenta: 'Cuenta Corriente',
        numero_cuenta_bancaria: '********',
        dias_atencion: 'LUNES A VIERNES',
        horario_atencion: '09:00 - 18:00',
        contrata_boleta: 'Si',
        competencia_actual: 'Transbank'
    };
    
    Object.keys(testData).forEach(key => {
        const field = document.getElementById(key);
        if (field) {
            field.value = testData[key];
            // Trigger validation
            field.dispatchEvent(new Event('input'));
        }
    });
    
    showMessage('info', 'Datos de prueba cargados correctamente');
}

// Validar todo el formulario
function validateForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return false;
    
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!validateRequired(field)) {
            isValid = false;
        }
    });
    
    // Validaciones específicas
    const rutField = document.getElementById('rut_cliente');
    if (rutField && !validateRUT(rutField)) {
        isValid = false;
    }
    
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField && !validateRazonSocial(razonSocialField)) {
        isValid = false;
    }
    
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField && !validateTelefono(telefonoField)) {
        isValid = false;
    }
    
    const emailField = document.getElementById('email');
    if (emailField && !validateEmail(emailField)) {
        isValid = false;
    }
    
    return isValid;
}

// Variable global para controlar envíos múltiples
let isSubmitting = false;

// Manejar guardado del prospecto usando método directo sin service worker
function handleSaveProspect() {
    // Prevenir múltiples envíos
    if (isSubmitting) {
        console.log('Ya hay un envío en progreso, ignorando...');
        return;
    }

    if (!validateForm()) {
        showMessage('error', 'Por favor, corrija los errores en el formulario antes de continuar');
        return;
    }

    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    const form = document.getElementById('inteletGroupProspectForm');

    // Marcar como enviando y mostrar estado de carga
    isSubmitting = true;
    saveBtn.classList.add('loading');
    saveBtn.disabled = true;
    
    // Cambiar el texto del botón para mostrar estado de carga
    const btnText = saveBtn.querySelector('.btn-text');
    const btnLoading = saveBtn.querySelector('.btn-loading');
    if (btnText) btnText.style.display = 'none';
    if (btnLoading) btnLoading.style.display = 'inline-block';
    
    // Mostrar mensaje de carga
    showMessage('info', 'Guardando prospecto InteletGroup...');

    // Crear un timestamp único para evitar cache
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);

    // Obtener todos los datos del formulario
    const formData = new FormData(form);
    formData.append('usuario_id', currentUserId);
    formData.append('timestamp', timestamp);
    formData.append('bypass_sw', '1'); // Flag para bypass service worker

    console.log('Submitting form to:', `guardar_inteletgroup_prospecto.php?t=${timestamp}&r=${randomId}`);

    // Usar fetch en lugar de formulario con iframe para mantener las cookies de sesión
    fetch(`guardar_inteletgroup_prospecto.php?t=${timestamp}&r=${randomId}`, {
        method: 'POST',
        body: formData,
        credentials: 'same-origin', // Importante: incluir cookies de sesión
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        // Capturar la respuesta incluso si hay error para ver el mensaje específico
        return response.text().then(text => {
            console.log('Response text:', text);
            if (!response.ok) {
                console.log('Error response text:', text);
                // Mostrar el error específico del servidor
                showMessage('error', `Error del servidor (${response.status}): ${text.substring(0, 300)}`);
                return null; // Retornar null para indicar error
            }
            return text;
        });
    })
    .then(responseText => {
        // Si responseText es null, significa que hubo un error y ya se mostró el mensaje
        if (responseText === null) {
            return;
        }

        console.log('Response from server:', responseText);

        // Verificar si hay respuesta
        if (responseText && responseText.trim()) {
            try {
                const result = JSON.parse(responseText);

                if (result.success) {
                    // Mostrar mensaje de éxito más visible
                    showMessage('success', result.message || 'Prospecto InteletGroup guardado exitosamente');
                    
                    // Crear notificación en la página principal
                    crearNotificacionPrincipal('success', 'Prospecto guardado con éxito', 
                                             'El prospecto ha sido registrado correctamente en el sistema.');
                    
                    // Esperar antes de cerrar el modal para que el usuario pueda ver el mensaje
                    setTimeout(() => {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('inteletGroupProspectModal'));
                        modal.hide();
                        // Recargar la página para mostrar el nuevo prospecto
                        location.reload();
                    }, 2000);
                } else {
                    showMessage('error', result.message || 'Error al guardar el prospecto');
                    console.error('Error del servidor:', result);
                }
            } catch (jsonError) {
                console.error('Error al parsear JSON:', jsonError);
                // Mostrar el texto de respuesta en lugar de error genérico
                if (responseText.includes("Fatal error") || responseText.includes("Parse error")) {
                    showMessage('error', 'Error en el servidor: ' + responseText.substring(0, 100) + '...');
                } else {
                    showMessage('error', 'Respuesta no válida del servidor: ' + responseText.substring(0, 200));
                }
            }
        } else {
            console.error('Respuesta vacía del servidor');
            showMessage('error', 'No se recibió respuesta del servidor. Verifique la conexión a la base de datos.');
        }
    })
    .catch(error => {
        console.error('Error en fetch:', error);
        showMessage('error', 'Error de conexión: ' + error.message);
    })
    .finally(() => {
        // Restaurar botón y resetear flag
        isSubmitting = false;
        saveBtn.classList.remove('loading');
        saveBtn.disabled = false;
        
        // Restaurar el texto original del botón
        if (btnText) btnText.style.display = 'inline-block';
        if (btnLoading) btnLoading.style.display = 'none';
    });
}

// Mostrar mensajes en el formulario modal
function showMessage(type, message) {
    const container = document.getElementById('inteletgroup-message-container');
    const successMsg = document.getElementById('inteletgroup-success-message');
    const errorMsg = document.getElementById('inteletgroup-error-message');
    const loadingMsg = document.getElementById('inteletgroup-loading-message');
    
    if (!container || !successMsg || !errorMsg || !loadingMsg) {
        console.error('Elementos de mensaje no encontrados');
        return;
    }
    
    // Ocultar todos los mensajes
    successMsg.style.display = 'none';
    errorMsg.style.display = 'none';
    loadingMsg.style.display = 'none';
    
    // Mostrar el mensaje apropiado
    let targetMsg;
    switch (type) {
        case 'success':
            targetMsg = successMsg;
            break;
        case 'error':
            targetMsg = errorMsg;
            break;
        case 'info':
            targetMsg = loadingMsg;
            break;
    }
    
    if (targetMsg) {
        const messageText = targetMsg.querySelector('.message-text');
        if (messageText) {
            messageText.textContent = message;
        }
        targetMsg.style.display = 'block';
        container.style.display = 'block';
        
        // Hacer scroll al mensaje para asegurarnos que sea visible
        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        // Auto-hide después de 5 segundos para mensajes de éxito
        if (type === 'success') {
            setTimeout(() => {
                container.style.display = 'none';
            }, 5000);
        }
    }
}

// Crear notificación en la página principal (fuera del modal)
function crearNotificacionPrincipal(type, title, message) {
    // Buscar si ya existe el contenedor de notificaciones o crearlo
    let notifContainer = document.getElementById('inteletgroup-notifications-container');
    
    if (!notifContainer) {
        notifContainer = document.createElement('div');
        notifContainer.id = 'inteletgroup-notifications-container';
        notifContainer.style.position = 'fixed';
        notifContainer.style.top = '80px';
        notifContainer.style.right = '20px';
        notifContainer.style.zIndex = '9999';
        notifContainer.style.maxWidth = '350px';
        document.body.appendChild(notifContainer);
    }
    
    // Crear la notificación
    const notif = document.createElement('div');
    notif.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} shadow-sm`;
    notif.style.marginBottom = '10px';
    notif.style.position = 'relative';
    notif.style.animation = 'fadeInRight 0.5s';
    
    // Agregar estilos de animación si no existen
    if (!document.getElementById('notif-animations')) {
        const style = document.createElement('style');
        style.id = 'notif-animations';
        style.textContent = `
            @keyframes fadeInRight {
                from { opacity: 0; transform: translateX(50px); }
                to { opacity: 1; transform: translateX(0); }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    // Contenido de la notificación
    notif.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="bi ${type === 'success' ? 'bi-check-circle-fill' : 
                          type === 'error' ? 'bi-exclamation-triangle-fill' : 
                          'bi-info-circle-fill'}" style="font-size: 1.5rem;"></i>
            </div>
            <div>
                <h6 class="alert-heading mb-1">${title}</h6>
                <p class="mb-0 small">${message}</p>
            </div>
        </div>
        <button type="button" class="btn-close btn-sm position-absolute" 
                style="top: 10px; right: 10px;" aria-label="Close"></button>
    `;
    
    // Agregar la notificación al contenedor
    notifContainer.appendChild(notif);
    
    // Agregar evento para cerrar la notificación
    const closeBtn = notif.querySelector('.btn-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            notif.style.animation = 'fadeOut 0.3s';
            setTimeout(() => {
                notifContainer.removeChild(notif);
                // Si no quedan notificaciones, remover el contenedor
                if (notifContainer.children.length === 0) {
                    document.body.removeChild(notifContainer);
                }
            }, 300);
        });
    }
    
    // Auto-ocultar después de 8 segundos
    setTimeout(() => {
        if (notif.parentNode) {
            notif.style.animation = 'fadeOut 0.3s';
            setTimeout(() => {
                if (notif.parentNode) {
                    notifContainer.removeChild(notif);
                    // Si no quedan notificaciones, remover el contenedor
                    if (notifContainer.children.length === 0 && notifContainer.parentNode) {
                        document.body.removeChild(notifContainer);
                    }
                }
            }, 300);
        }
    }, 8000);
    
    return notif;
}

// Resetear formulario
function resetForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (form) {
        form.reset();
        
        // Limpiar estados de validación
        const fields = form.querySelectorAll('.form-control, .form-select');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });
        
        // Limpiar mensajes
        const feedbacks = form.querySelectorAll('.invalid-feedback');
        feedbacks.forEach(feedback => {
            feedback.textContent = '';
        });
        
        // Resetear el botón de guardar
        const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.classList.remove('loading');
            
            const btnText = saveBtn.querySelector('.btn-text');
            const btnLoading = saveBtn.querySelector('.btn-loading');
            if (btnText) btnText.style.display = 'inline-block';
            if (btnLoading) btnLoading.style.display = 'none';
        }
    }
    
    // Ocultar mensajes
    const container = document.getElementById('inteletgroup-message-container');
    if (container) {
        container.style.display = 'none';
    }
}

// Función para abrir el modal (llamada desde el botón)
function abrirModalInteletGroupProspecto() {
    const modal = new bootstrap.Modal(document.getElementById('inteletGroupProspectModal'));
    modal.show();
}
