<?php
// Configurar encabezados para permitir CORS y especificar el tipo de contenido
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Max-Age: 3600');

// Incluir el archivo de conexión a la base de datos
// Usar dirname para obtener la ruta absoluta al directorio padre
require_once dirname(__FILE__) . '/../con_db.php';

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar si el usuario está autenticado
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'Usuario no autenticado']);
    exit;
}

try {
    // Establecer conexión a la base de datos usando PDO
    try {
        // Configuración de la conexión a la base de datos
        $host = 'localhost';
        $port = 3306;
        $dbname = 'gestarse_experian';
        $username = 'gestarse_ncornejo7_experian';
        $password = 'N1c0l7as17';

        $connection = new PDO(
            "mysql:host=$host;dbname=$dbname;charset=utf8",
            $username,
            $password,
            array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
        );
    } catch (PDOException $e) {
        throw new Exception("No se pudo establecer conexión con la base de datos: " . $e->getMessage());
    }

    // Consulta SQL para obtener todos los prospectos con la última bitácora
    $query = "SELECT
                p.nombre_ejecutivo,
                p.rut_ejecutivo,
                p.razon_social,
                p.rubro,
                p.contacto,
                p.telefono,
                p.fecha,
                COALESCE(b.estado, p.estado) AS estado,
                COALESCE(b.observaciones, p.observaciones) AS observaciones,
                COALESCE(b.fecha_registro, p.fecha_registro) AS fecha_gestion,
                u.nombre_usuario AS nombre_usuario_gestion
              FROM tb_experian_prospecto p
              LEFT JOIN (
                  SELECT
                      pb.rut_ejecutivo,
                      pb.estado,
                      pb.observaciones,
                      pb.fecha_registro,
                      pb.usuario_id,
                      ROW_NUMBER() OVER (PARTITION BY pb.rut_ejecutivo ORDER BY pb.fecha_registro DESC) as rn
                  FROM tb_experian_prospecto_bitacora pb
              ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
              LEFT JOIN tb_experian_usuarios u ON b.usuario_id = u.id
              ORDER BY COALESCE(b.fecha_registro, p.fecha_registro) DESC";

    $stmt = $connection->prepare($query);
    $stmt->execute();
    $prospectos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Devolver los datos en formato JSON
    echo json_encode([
        'success' => true,
        'data' => $prospectos
    ]);
    exit;

} catch (Exception $e) {
    // En caso de error, devolver un mensaje de error
    echo json_encode([
        'success' => false,
        'message' => 'Error al exportar los prospectos: ' . $e->getMessage()
    ]);
    exit;
}
?>
