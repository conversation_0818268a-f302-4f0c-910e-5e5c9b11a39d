/* :: Image Gallery */

.masonry-content-wrapper {
    overflow: hidden;

    >* {
        width: calc(50% - 1rem);
        margin: .5rem;
    }
}

.single-gallery-item {
    position: relative;
    z-index: 1;
    border-radius: .5rem;
    transition-duration: 100ms;

    img {
        border-radius: .5rem;
    }

    .fav-icon {
        text-align: center;
        transition-duration: 500ms;
        background-color: $white;
        color: $danger;
        position: absolute;
        width: 1.75rem;
        height: 1.75rem;
        top: .75rem;
        right: .75rem;
        z-index: 10;
        border-radius: 50%;
        visibility: hidden;
        opacity: 0;

        i {
            line-height: 32px;
        }

        &.active {
            background-color: $danger;
            color: $white;
        }
    }

    &:hover,
    &:focus {
        .fav-icon {
            visibility: visible;
            opacity: 1;
        }
    }
}

.vbox-container {
    overflow-y: hidden !important;
}

.vbox-child {
    border-radius: 6px;

    img {
        border-radius: 6px;
        max-height: 90vh;
    }
}

.vbox-bottom {
    bottom: 8px !important;
}

.vbox-share {
    font-size: 18px;
    border-radius: 8px;
}

.vbox-title {
    color: $white;
}

.vbox-close {
    font-size: 20px;
}