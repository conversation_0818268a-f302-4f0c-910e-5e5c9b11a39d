<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || empty($_SESSION['usuario_id'])) {
    // Responder con error en formato JSON
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Usuario no autenticado']);
    exit();
}

// Incluir archivo de conexión a la base de datos y verificar conexión
require_once 'con_db.php';
if (!isset($conn) || $conn === null) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']);
    exit();
}

// Obtener el ID del usuario de la sesión
$usuario_id = $_SESSION['usuario_id'];

// Verificar si el usuario tiene rol de administrador
$query_rol = "SELECT id, correo, nombre_usuario, rol FROM tb_experian_usuarios WHERE id = ?";
$stmt_rol = $conn->prepare($query_rol);
$stmt_rol->bind_param("i", $usuario_id);
$stmt_rol->execute();
$result_rol = $stmt_rol->get_result();
$usuario = $result_rol->fetch_assoc();
$es_admin = false;

// Registrar información del usuario para depuración
error_log("cargar_prospecto.php - Usuario ID: " . $usuario_id . " - Datos: " . json_encode($usuario));

// Forzar a que todos los usuarios que no sean explícitamente administradores vean solo sus prospectos
if ($usuario && isset($usuario['rol']) && ($usuario['rol'] == 'admin' || $usuario['rol'] == 'administrador')) {
    $es_admin = true;
    error_log("cargar_prospecto.php - Usuario " . $usuario['nombre_usuario'] . " (ID: " . $usuario_id . ") es administrador");
} else {
    $es_admin = false;
    error_log("cargar_prospecto.php - Usuario " . ($usuario ? $usuario['nombre_usuario'] : 'desconocido') . " (ID: " . $usuario_id . ") NO es administrador");
}

// Consulta SQL para obtener los prospectos con la última bitácora
// Si es admin, mostrar todos; si no, filtrar por usuario_id
if ($es_admin) {
    $sql = "SELECT p.*,
            b.estado AS ultimo_estado,
            b.observaciones AS ultima_observacion,
            b.fecha_registro AS ultima_fecha_gestion,
            u.nombre_usuario AS nombre_usuario_creador
          FROM tb_experian_prospecto p
          LEFT JOIN (
              SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                     ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
              FROM tb_experian_prospecto_bitacora
          ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
          LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
          ORDER BY p.fecha_registro DESC";

    error_log("cargar_prospecto.php - Consulta SQL para administrador: " . $sql);
    $result = mysqli_query($conn, $sql);
} else {
    // FORZAR FILTRADO POR USUARIO_ID
    $sql = "SELECT p.*,
            b.estado AS ultimo_estado,
            b.observaciones AS ultima_observacion,
            b.fecha_registro AS ultima_fecha_gestion,
            u.nombre_usuario AS nombre_usuario_creador
          FROM tb_experian_prospecto p
          LEFT JOIN (
              SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                     ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
              FROM tb_experian_prospecto_bitacora
          ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
          LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
          WHERE p.usuario_id = ?
          ORDER BY p.fecha_registro DESC";

    error_log("cargar_prospecto.php - Consulta SQL para usuario regular: " . $sql . " (usuario_id = " . $usuario_id . ")");
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
}

// Registrar en el log qué tipo de consulta se está ejecutando
error_log("Ejecutando consulta de prospectos en cargar_prospecto.php como " . ($es_admin ? "administrador" : "usuario regular") . ". Usuario ID: " . $usuario_id);

if ($result) {
    $prospectos = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $prospectos[] = $row;
    }

    // Responder con éxito en formato JSON
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'data' => $prospectos]);
} else {
    // Responder con error en formato JSON
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Error al consultar prospectos: ' . mysqli_error($conn)]);
}

// Cerrar conexión
mysqli_close($conn);
?>