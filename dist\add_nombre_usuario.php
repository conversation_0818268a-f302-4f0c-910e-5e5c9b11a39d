<?php
// Incluir archivo de conexión a la base de datos
require_once 'con_db.php';

try {
    // Verificar si la columna ya existe
    $result = $mysqli->query("SHOW COLUMNS FROM tb_experian_usuarios LIKE 'nombre_usuario'");
    
    if ($result->num_rows === 0) {
        // La columna no existe, agregarla
        $sql = "ALTER TABLE tb_experian_usuarios ADD COLUMN nombre_usuario VARCHAR(255) AFTER correo";
        
        if (!$mysqli->query($sql)) {
            throw new Exception("Error al agregar la columna nombre_usuario: " . $mysqli->error);
        }
        
        // Actualizar los registros existentes para establecer nombre_usuario igual a correo
        $update_sql = "UPDATE tb_experian_usuarios SET nombre_usuario = SUBSTRING_INDEX(correo, '@', 1)";
        
        if (!$mysqli->query($update_sql)) {
            throw new Exception("Error al actualizar los valores de nombre_usuario: " . $mysqli->error);
        }
        
        echo "Columna nombre_usuario agregada y actualizada correctamente";
    } else {
        echo "La columna nombre_usuario ya existe en la tabla";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
