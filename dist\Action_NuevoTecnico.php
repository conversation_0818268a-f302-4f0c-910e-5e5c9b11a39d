<?php
// Conexión a la base de datos


$inc = include("con_db.php");
session_start();

// Obtener los datos del formulario
$correo = $_POST['correo'];
$nombre = $_POST['nombre'];
$RUT = $_POST['RUT'];
$area = $_POST['area'];
$super = $_POST['super'];




// Realizar la consulta SQL para insertar los datos en la tabla
$sql = "INSERT INTO tb_user_tqw (email  , pass  , reg_date  , nombre  , area  , supervisor  ,  rut  ,  vigente )
values ('$correo'  , substr('$RUT',1,LENGTH('$RUT')-2) , now() , '$nombre'  , '$area'  , '$super'  ,  '$RUT'  ,  'Si' )
";


$sql2 = "INSERT INTO TB_CLAVES_USUARIOS 
VALUES (NOW(),substr('$RUT',1,LENGTH('$RUT')-2),'$correo',substr('$RUT',1,LENGTH('$RUT')-2),1,null,null)";

// Ejecutar la consulta
if (mysqli_query($conex, $sql) && mysqli_query($conex, $sql2)) {
echo "<script>alert('Datos guardados correctamente');</script>";
?>

<script type="text/javascript">
window.location = 'Home_Admin.php'
</script>

<?php    

} else 
{
echo "Error al guardar los datos: " . mysqli_error($conex);
}
 





// Cerrar la conexión
mysqli_close($conex);
?>