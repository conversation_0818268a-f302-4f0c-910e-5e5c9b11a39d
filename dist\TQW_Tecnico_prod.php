
<?php 

    header('Content-Type: text/html; charset=UTF-8');
    //Iniciar una nueva sesión o reanudar la existente.
    session_start();

    $inc = include("con_db.php");





    
    $KPI_2023 = $conex->query("
    select 
    cast(`Fecha fin#` as date) as fecha
    , SUM(Ptos_referencial)  Ptos
    from  tb_paso_pyNdc tppn      
    where NombreTecnico = '".$_SESSION['RUT']."'
    AND  cast(`Fecha fin#` as date)  > '2023-03-25'  
    group by cast(`Fecha fin#` as date)  
    limit 7
    ");

      while ($row = mysqli_fetch_array($KPI_2023)) {

          $data1 = $data1 . '"'. $row['Ptos'].'",';          
          
        }
      $data1 = trim($data1,",");
      
      


    ?>



        

        <!DOCTYPE html>
        <html lang="en">

        <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="Affan - PWA Mobile HTML Template">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

        <meta name="theme-color" content="#0134d4">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">

        <!-- Title -->
        <title>APP TQW </title>

        <!-- Favicon -->
        <link rel="icon" href="img/core-img/favicon.ico">
        <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
        <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
        <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
        <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

        <!-- Style CSS -->
        <link rel="stylesheet" href="style.css">

        <!-- Web App Manifest -->
        <link rel="manifest" href="manifest.json">
        </head>

        <body>
        <!-- Preloader -->
        <div id="preloader">
            <div class="spinner-grow text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
            </div>
        </div>

        <!-- Internet Connection Status -->
        <div class="internet-connection-status" id="internetStatus"></div>

        <!-- Dark mode switching -->
        <div class="dark-mode-switching">
            <div class="d-flex w-100 h-100 align-items-center justify-content-center">
            <div class="dark-mode-text text-center">
                <i class="bi bi-moon"></i>
                <p class="mb-0">Switching to dark mode</p>
            </div>
            <div class="light-mode-text text-center">
                <i class="bi bi-brightness-high"></i>
                <p class="mb-0">Switching to light mode</p>
            </div>
            </div>
        </div>

        <!-- RTL mode switching -->
        <div class="rtl-mode-switching">
            <div class="d-flex w-100 h-100 align-items-center justify-content-center">
            <div class="rtl-mode-text text-center">
                <i class="bi bi-text-right"></i>
                <p class="mb-0">Switching to RTL mode</p>
            </div>
            <div class="ltr-mode-text text-center">
                <i class="bi bi-text-left"></i>
                <p class="mb-0">Switching to default mode</p>
            </div>
            </div>
        </div>

        <!-- Setting Popup Overlay -->
        <div id="setting-popup-overlay"></div>

        <!-- Setting Popup Card -->
        <div class="card setting-popup-card shadow-lg" id="settingCard">
            <div class="card-body">
            <div class="container">
                <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
                <p class="mb-0">Settings</p>
                <div class="btn-close" id="settingCardClose"></div>
                </div>

                <div class="single-setting-panel">
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
                    <label class="form-check-label" for="availabilityStatus">Availability status</label>
                </div>
                </div>

                <div class="single-setting-panel">
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
                    <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
                </div>
                </div>

                <div class="single-setting-panel">
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="darkSwitch">
                    <label class="form-check-label" for="darkSwitch">Dark mode</label>
                </div>
                </div>

                <div class="single-setting-panel">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="rtlSwitch">
                    <label class="form-check-label" for="rtlSwitch">RTL mode</label>
                </div>
                </div>
            </div>
            </div>
        </div>

        <!-- Header Area -->
        <div class="header-area" id="headerArea">
            <div class="container">
            <!-- Header Content -->
            <div class="header-content position-relative d-flex align-items-center justify-content-between">
                <!-- Back Button -->
                <div class="back-button">
                <a href="elements.html">
                    <i class="bi bi-arrow-left-short"></i>
                </a>
                </div>

                <!-- Page Title -->
                <div class="page-heading">
                <h6 class="mb-0">Area Chart</h6>
                </div>

                <!-- Settings -->
                <div class="setting-wrapper">
                <div class="setting-trigger-btn" id="settingTriggerBtn">
                    <i class="bi bi-gear"></i>
                    <span></span>
                </div>
                </div>
            </div>
            </div>
        </div>

        <div class="page-content-wrapper py-3">

            <!-- Element Heading -->
            <div class="container">
            <div class="element-heading">
                <h6>Area One</h6>
            </div>
            </div>

            <div class="container">
            <div class="card shadow-sm">
                <div class="card-body pb-2">
                <div class="chart-wrapper">
                <div id="areaChart17"></div>
            </div>
                </div>
            </div>
            </div>

            <!-- Element Heading -->
            <div class="container">
            <div class="element-heading mt-3">
                <h6>Area Two</h6>
            </div>
            </div>

            <div class="container">
            <div class="card shadow-sm">
                <div class="card-body pb-2">
                <div class="chart-wrapper">
                    <div id="areaChart2"></div>
                </div>
                </div>
            </div>
            </div>
        </div>

        <!-- Footer Nav -->
        <div class="footer-nav-area" id="footerNav">
            <div class="container px-0">
            <!-- Footer Content -->
            <div class="footer-nav position-relative">
                <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
                <li class="active">
                    <a href="home.html">
                    <i class="bi bi-house"></i>
                    <span>Home</span>
                    </a>
                </li>

                <li>
                    <a href="pages.html">
                    <i class="bi bi-collection"></i>
                    <span>Pages</span>
                    </a>
                </li>

                <li>
                    <a href="elements.html">
                    <i class="bi bi-folder2-open"></i>
                    <span>Elements</span>
                    </a>
                </li>

                <li>
                    <a href="chat-users.html">
                    <i class="bi bi-chat-dots"></i>
                    <span>Chat</span>
                    </a>
                </li>

                <li>
                    <a href="settings.html">
                    <i class="bi bi-gear"></i>
                    <span>Settings</span>
                    </a>
                </li>
                </ul>
            </div>
            </div>
        </div>


        <script>
               
                var areaChart17 = {
                    chart: {
                        height: 240,
                        type: 'area',
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 1000
                        },
                        dropShadow: {
                            enabled: true,
                            opacity: 0.1,
                            blur: 1,
                            left: -5,
                            top: 18
                        },
                        zoom: {
                            enabled: false
                        },
                        toolbar: {
                            show: false
                        },
                    },
                    colors: ['#0134d4', '#ea4c62'],
                    dataLabels: {
                        enabled: false
                    },
                    fill: {
                        type: "gradient",
                        gradient: {
                            type: "vertical",
                            shadeIntensity: 1,
                            inverseColors: true,
                            opacityFrom: 0.15,
                            opacityTo: 0.02,
                            stops: [40, 100],
                        }
                    },
                    grid: {
                        borderColor: '#dbeaea',
                        strokeDashArray: 4,
                        xaxis: {
                            lines: {
                                show: true
                            }
                        },
                        yaxis: {
                            lines: {
                                show: false,
                            }
                        },
                        padding: {
                            top: 0,
                            right: 0,
                            bottom: 0,
                            left: 0
                        },
                    },
                    legend: {
                        position: 'bottom',
                        horizontalAlign: 'center',
                        offsetY: 4,
                        fontSize: '14px',
                        markers: {
                            width: 9,
                            height: 9,
                            strokeWidth: 0,
                            radius: 20
                        },
                        itemMargin: {
                            horizontal: 5,
                            vertical: 0
                        }
                    },
                    title: {
                        text: '$5,391',
                        align: 'left',
                        margin: 0,
                        offsetX: 0,
                        offsetY: 20,
                        floating: false,
                        style: {
                            fontSize: '16px',
                            color: '#8480ae'
                        }
                    },
                    tooltip: {
                        theme: 'dark',
                        marker: {
                            show: true,
                        },
                        x: {
                            show: false,
                        }
                    },
                    subtitle: {
                        text: 'This week sales',
                        align: 'left',
                        margin: 0,
                        offsetX: 0,
                        offsetY: 0,
                        floating: false,
                        style: {
                            fontSize: '14px',
                            color: '#8480ae'
                        }
                    },
                    stroke: {
                        show: true,
                        curve: 'smooth',
                        width: 3
                    },
                    labels: ['S', 'S', 'M', 'T', 'W', 'T', 'F'],
                    series: [{
                        name: 'Affan',
                        data: [data1]
                    }, {
                        name: 'Suha',
                        data: [177, 395, 350, 395, 100, 385, 374]
                    }],
                    xaxis: {
                        crosshairs: {
                            show: true
                        },
                        labels: {
                            offsetX: 0,
                            offsetY: 0,
                            style: {
                                colors: '#8480ae',
                                fontSize: '12px',
                            },
                        },
                        tooltip: {
                            enabled: false,
                        },
                    },
                    yaxis: {
                        labels: {
                            offsetX: -10,
                            offsetY: 0,
                            style: {
                                colors: '#8480ae',
                                fontSize: '12px',
                            },
                        }
                    },
                }


               </script>

        

        <!-- All JavaScript Files -->
        <script src="js/bootstrap.bundle.min.js"></script>
        <script src="js/internet-status.js"></script>
        <script src="js/dark-rtl.js"></script>
        <script src="js/active.js"></script>
        <!-- Apex Chart -->
        <script src="js/apexcharts.min.js"></script>
        <script src="js/chart-active.js"></script>
        <!-- PWA -->
        <script src="js/pwa.js"></script>
        </body>

        </html>


