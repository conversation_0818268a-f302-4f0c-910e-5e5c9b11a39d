<?php
require_once("con_db.php");

try {
    // Crear la tabla si no existe
    $sql = "CREATE TABLE IF NOT EXISTS usuarios (
        id INT AUTO_INCREMENT PRIMARY KEY,
        correo VARCHAR(255) NOT NULL,
        clave VARCHAR(255) NOT NULL,
        rol VARCHAR(50) DEFAULT 'ejecutivos'
    )";

    if (!$mysqli->query($sql)) {
        throw new Exception("Error creando la tabla: " . $mysqli->error);
    }

    // Insertar usuarios predefinidos solo si la tabla está vacía
    $check = $mysqli->query("SELECT id FROM usuarios LIMIT 1");
    if ($check->num_rows === 0) {
        $usuarios = [
            ['<EMAIL>', 'Marcel.2024B'],
            ['<EMAIL>', 'Marcela.2024B'],
            ['<PERSON><PERSON>@gestarservicios.cl', 'Jose.2024G'],
            ['<PERSON><PERSON>.<EMAIL>', 'Oriana.2024C'],
            ['<PERSON><PERSON>@gestarservic<PERSON>.cl', '<PERSON>.2024R'],
            ['<PERSON>herina.<PERSON>@gestarservicios.cl', 'Katherina.2024W']
        ];

        $stmt = $mysqli->prepare("INSERT INTO usuarios (correo, clave, rol) VALUES (?, ?, 'ejecutivos')");
        foreach ($usuarios as $usuario) {
            $stmt->bind_param("ss", $usuario[0], $usuario[1]);
            if (!$stmt->execute()) {
                throw new Exception("Error insertando usuario {$usuario[0]}: " . $stmt->error);
            }
        }
        $stmt->close();
        echo "Usuarios creados exitosamente";
    } else {
        echo "La tabla usuarios ya existe y contiene datos";
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>