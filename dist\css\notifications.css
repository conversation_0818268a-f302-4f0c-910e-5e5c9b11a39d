/* Estilos para notificaciones */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    max-width: 350px;
    min-width: 250px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    animation: slide-in 0.4s ease-out;
    display: flex;
    align-items: center;
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.3s, transform 0.3s;
}

.notification.visible {
    opacity: 1;
    transform: translateX(0);
}

.notification.success {
    background-color: #3cb371; /* Experian verde */
    border-left: 5px solid #2e8b57;
}

.notification.error {
    background-color: #e74c3c;
    border-left: 5px solid #c0392b;
}

.notification.info {
    background-color: #3498db;
    border-left: 5px solid #2980b9;
}

.notification-icon {
    margin-right: 10px;
    font-size: 20px;
}

.notification-close {
    margin-left: 10px;
    cursor: pointer;
    opacity: 0.7;
    font-size: 18px;
    transition: opacity 0.2s;
}

.notification-close:hover {
    opacity: 1;
}

.notification-message {
    flex-grow: 1;
    word-break: break-word;
}

/* Animación de entrada */
@keyframes slide-in {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Animación de salida */
@keyframes slide-out {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(30px);
    }
}

.notification.removing {
    animation: slide-out 0.3s forwards;
}

/* Ajustes para móviles */
@media (max-width: 576px) {
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: calc(100% - 20px);
    }
}