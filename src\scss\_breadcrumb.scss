/* :: Breadcrumb */

.breadcrumb-wrapper {
    background-color: $white;
}

.breadcrumb {
    background-color: $white;
}

.breadcrumb-item {
    font-weight: 500;
    font-size: 14px;
}

.breadcrumb-one {
    position: relative;
    z-index: 1;

    .breadcrumb-item+.breadcrumb-item::before {
        color: $text;
        content: ">";
    }

    .breadcrumb-item {
        font-size: 14px;
        color: $heading;
    }

    .breadcrumb-item a {
        color: $text;

        &:hover,
        &:focus {
            color: $primary;
        }
    }
}

.breadcrumb-two {
    position: relative;
    z-index: 1;

    .breadcrumb-item+.breadcrumb-item::before {
        color: $text;
        content: "~";
    }

    .breadcrumb-item {
        font-size: 14px;
        color: $heading;
    }

    .breadcrumb-item a {
        color: $text;

        &:hover,
        &:focus {
            color: $primary;
        }
    }
}

.breadcrumb-three {
    position: relative;
    z-index: 1;

    .breadcrumb-item+.breadcrumb-item::before {
        color: $text;
        content: ">";
    }

    .breadcrumb-item {
        color: $heading;
    }

    .breadcrumb-item a {
        color: $text;

        i {
            margin-right: .25rem;
            color: $heading;
        }

        &:hover,
        &:focus {
            color: $primary;
        }
    }
}

.breadcrumb-four {
    position: relative;
    z-index: 1;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;

    .breadcrumb-item+.breadcrumb-item::before {
        color: $white;
        content: ">";
    }

    .breadcrumb-item {
        color: $white;
    }

    .breadcrumb-item a {
        color: $white;

        &:hover,
        &:focus {
            color: $primary;
        }
    }
}

.breadcrumb-colorful {
    position: relative;
    z-index: 1;

    .breadcrumb-item+.breadcrumb-item::before {
        color: rgba(255, 255, 255, 0.6);
        content: ">";
    }

    .breadcrumb-item {
        font-size: 14px;
        color: $white;
    }

    .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.6);
        border-bottom: 2px solid rgba(255, 255, 255, 0.6);

        &:hover,
        &:focus {
            color: $white;
        }
    }
}