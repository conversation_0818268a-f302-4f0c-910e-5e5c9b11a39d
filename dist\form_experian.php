<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Aplicar headers anti-caché
no_cache_headers();

// Verificación mejorada de autenticación
function checkAuth() {
    if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
        // Registrar el intento fallido
        error_log("[" . date('Y-m-d H:i:s') . "] Error de autenticación - Usuario no identificado - IP: " . $_SERVER['REMOTE_ADDR']);

        // Redireccionar a login con mensaje de error
        $errorMsg = urlencode("Usuario no autenticado. Inicie sesión para continuar.");
        header('Location: ' . version_url('login.php?error=' . $errorMsg));
        exit;
    }

    // Renovar la sesión para evitar su caducidad prematura
    $_SESSION['last_activity'] = time();
    return true;
}

// Verificar si el usuario está autenticado
checkAuth();

// Incluir el archivo de conexión
require_once 'con_db.php';

// Asegurar que tenemos una conexión a la base de datos disponible globalmente
// Crear una función que devuelve la conexión PDO, para asegurar que está disponible cuando se necesite
function getDBConnection() {
    global $mysqli, $conn;

    // Si ya existe una conexión mysqli, crear y devolver una conexión PDO equivalente
    if (isset($mysqli) && $mysqli instanceof mysqli && !$mysqli->connect_error) {
        try {
            return new PDO(
                "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                'gestarse_ncornejo7_experian',
                'N1c0l7as17',
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch (PDOException $e) {
            error_log("Error creando conexión PDO desde mysqli existente: " . $e->getMessage());
            return null;
        }
    }

    // Si no hay conexión existente, crear una nueva
    try {
        $pdo = new PDO(
            "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
            'gestarse_ncornejo7_experian',
            'N1c0l7as17',
            array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("Error creando nueva conexión a la base de datos: " . $e->getMessage());
        return null;
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulario de Cliente</title>
    <?php echo no_cache_meta(); ?>
    <link rel="stylesheet" href="<?php echo version_url('css/form_experian.css'); ?>">
    <!-- Estilos para tablas ordenables -->
    <link rel="stylesheet" href="<?php echo version_url('css/table-sort.css'); ?>">
    <!-- Agregar Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Cargar jQuery primero -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Agregar SheetJS (versión completa y estable) -->
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script>
        // Verificar que jQuery se cargó correctamente
        window.onload = function() {
            if (typeof jQuery === "undefined") {
                alert("jQuery no se cargó correctamente");
            } else {
                console.log("jQuery cargado correctamente: " + jQuery.fn.jquery);
            }
            // Verificar la carga de SheetJS
            if (typeof XLSX === "undefined") {
                console.error("Error: SheetJS no se cargó correctamente");
            } else {
                console.log("SheetJS cargado correctamente");
            }
        };
    </script>
    <!-- Se eliminaron las referencias a DataTables -->
</head>
<body>
<div class="site-header">
    <div class="header-container">
        <div class="logo-container">
            <img src="<?php echo version_url('img/img_experian/logo.jpg'); ?>" alt="Logo Gestar" class="header-logo">
        </div>
        <div class="welcome-message" style="text-align:center; margin: 10px 0; padding: 15px; background: linear-gradient(45deg, #1E3C72, #2A5298); color: #fff; font-size: 1.2em; font-weight: bold; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            Bienvenido(a): <?php echo htmlspecialchars($_SESSION['usuario']); ?>
        </div>
        <nav class="header-nav">
            <ul>
                <li><a href="logout.php">Cerrar Sesión</a></li>
            </ul>
        </nav>
    </div>
</div>

<div class="tabs-container">
    <!-- Tabs Header -->
    <div class="tabs-header">
        <button class="tab-button active" data-tab="new-tab">Formulario Prospecto</button>
        <button class="tab-button" data-tab="form-tab">Formulario venta</button>
        <?php if ($_SESSION['usuario_id'] == 4): ?>
        <button class="tab-button" data-tab="table-tab">Registros</button>
        <?php endif; ?>
    </div>

    <!-- Tab Contenido: Formulario -->
    <div id="form-tab" class="tab-content">
        <form id="formExperian" method="POST" action="guardar_formulario.php" enctype="multipart/form-data">            <div class="container">
                <header>
                    <img src="img/img_experian/logo.jpg" alt="Logo Corporativo" class="logo" style="max-height: 100px;">
                    <h1>Formulario de Registro de Cliente</h1>
                </header>

                <!-- Indicador de pasos -->
                <div class="steps-container">
                    <div class="progress-line">
                        <div class="fill"></div>
                    </div>
                    <div class="step-indicator active" data-step="1">1. Identificación del Cliente</div>
                    <div class="step-indicator" data-step="2">2. Datos de Contactos</div>
                    <div class="step-indicator" data-step="3">3. Servicios y Transacciones</div>
                </div>

                <!-- Sección 1: Identificación del cliente -->
                <div class="section-container active" id="section1">
                    <div class="section-header">1. IDENTIFICACIÓN DEL CLIENTE</div>

                    <table>
                        <tr>
                            <td class="label">Tipo de Cliente</td>
                            <td class="input-cell">
                                <select name="tipo_cliente" title="Tipo de Cliente" required>
                                    <option value="">Seleccione...</option>
                                    <option>Cliente Vigente</option>
                                    <option>Cliente No vigente</option>
                                </select>
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12"
                                    required>
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Razón Social</td>
                            <td class="input-cell">
                                <input type="text" name="razon_social" title="Razón Social" required>
                            </td>
                            <td class="label">Nombre Representante Legal 1</td>
                            <td class="input-cell">
                                <input type="text" name="nombre_representante1" title="Nombre Representante Legal 1" required>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 1</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante1"
                                    title="Rut Representante 1"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Nombre Representante Legal 2</td>
                            <td class="input-cell"><input type="text" name="nombre_representante2" title="Nombre Representante Legal 2"></td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 2</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante2"
                                    title="Rut Representante 2"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Nombre Representante Legal 3</td>
                            <td class="input-cell"><input type="text" name="nombre_representante3" title="Nombre Representante Legal 3"></td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 3</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante3"
                                    title="Rut Representante 3"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Sistema Creación de Empresa</td>
                            <td class="input-cell">
                                <select name="sistema_creacion" title="Sistema Creación de Empresa">
                                    <option>Tradicional</option>
                                    <option>Empresa por un día</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Fecha de creación</td>
                            <td class="input-cell">
                                <input type="date" name="fecha_creacion" title="Fecha de creación" required>
                            </td>
                            <td class="label">Notaría</td>
                            <td class="input-cell"><input type="text" name="notaria" title="Notaría"></td>
                        </tr>
                        <tr>
                            <td class="label">Actividad Económica SII</td>
                            <td class="input-cell"><input type="number" name="actividad_economica" title="Actividad Económica SII"></td>
                            <td class="label">Fecha de Constitución</td>
                            <td class="input-cell">
                                <input type="date" name="fecha_constitucion" title="Fecha de Constitución" required>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Dirección</td>
                            <td class="input-cell"><input type="text" name="direccion" title="Dirección"></td>
                            <td class="label">Comuna</td>
                            <td class="input-cell"><input type="text" name="comuna" title="Comuna"></td>
                        </tr>
                        <tr>
                            <td class="label">Página Web</td>
                            <td class="input-cell"><input type="text" name="pagina_web" title="Página Web"></td>
                            <td class="label">Correo Electrónico contacto</td>
                            <td class="input-cell">
                                <input type="email" name="email" title="Correo Electrónico contacto"
                                    pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                                    required>
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="tel" name="telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678"
                                    required>
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Clasificación de Cliente SII</td>
                            <td class="input-cell">
                                <select name="clasificacion_sii" title="Clasificación de Cliente SII">
                                    <option>Nuevo</option>
                                    <option>Antiguo</option>
                                </select>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <div></div> <!-- Espacio vacío para alinear a la derecha -->
                        <button type="button" class="btn-next" data-next="2">Siguiente</button>
                    </div>
                </div>

                <!-- Sección 2: Datos de Contactos -->
                <div class="section-container" id="section2">
                    <div class="section-header">2. DATOS DE CONTACTOS</div>

                    <table>
                        <tr>
                            <td class="label">Contacto Nombre</td>
                            <td class="input-cell"><input type="text" name="contacto_nombre" title="Contacto Nombre"></td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="contacto_rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="contacto_telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Correo Electrónico</td>
                            <td class="input-cell">
                                <input type="email" name="contacto_email" title="Correo Electrónico"
                                    pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                        <td class="label">2.1 DATOS DE BACKUP</td>
                        <tr>
                            <td class="label">Contacto Backup Nombre</td>
                            <td class="input-cell"><input type="text" name="contacto_backup_nombre" title="Contacto Backup Nombre"></td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="contacto_backup_rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="contacto_backup_telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Correo Electrónico</td>
                            <td class="input-cell">
                                <input type="email" name="contacto_backup_email" title="Correo Electrónico"
                                    pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-prev" data-prev="1">Anterior</button>
                        <button type="button" class="btn-next" data-next="3">Siguiente</button>
                    </div>
                </div>

                <!-- Sección 3: Servicios y Nivel de Transacciones -->
                <div class="section-container" id="section3">
                    <div class="section-header">3. SERVICIOS Y NIVEL DE TRANSACCIONES / PUNTOS</div>

                    <!-- Subsección 3.1: Publicación de Morosos -->
                    <div class="subsection-header">3.1 PUBLICACIÓN DE MOROSOS</div>
                    <table>
                        <tr>
                            <td class="label">Plan</td>
                            <td class="input-cell">
                                <select name="morosos_plan" id="morosos_plan" title="Plan Publicación de Morosos">
                                    <option value="">Seleccione...</option>
                                    <option>XS</option>
                                    <option>S</option>
                                    <option>M</option>
                                    <option>L</option>
                                    <option>XL</option>
                                </select>
                            </td>
                            <td class="label">Número de Consultas</td>
                            <td class="input-cell">
                                <input type="text" name="morosos_consultas" id="morosos_consultas" title="Número de Consultas"  readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">UF Mensual</td>
                            <td class="input-cell">
                                <input type="number" name="morosos_uf" id="morosos_uf" step="0.01" title="UF Mensual" placeholder="0.00" readonly>
                            </td>
                            <td class="label">% de descuento</td>
                            <td class="input-cell">
                                <select name="morosos_descuento" title="% de descuento">
                                    <option value="0">0%</option>
                                    <option value="10">10%</option>
                                    <option value="20">20%</option>
                                    <option value="30">30%</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Nuevo Valor</td>
                            <td class="input-cell">
                                <input type="number" name="morosos_nuevo_valor" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    </table>

                    <!-- Subsección 3.2: Informe Advanced SME -->
                    <div class="subsection-header">3.2 INFORME ADVANCED SME</div>
                    <table>
                        <tr>
                            <td class="label">Plan</td>
                            <td class="input-cell">
                                <select name="advanced_plan" title="Plan Advanced SME">
                                    <option value="">Seleccione...</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                    <option value="300">300</option>
                                    <option value="400">400</option>
                                    <option value="500">500</option>
                                    <option value="1000">1.000</option>
                                    <option value="2000">2.000</option>
                                    <option value="3000">3.000</option>
                                    <option value="4000">4.000</option>
                                    <option value="5000">5.000</option>
                                </select>
                            </td>
                            <td class="label"> UF / transacción</td>
                            <td class="input-cell">
                                <input type="text" name="advanced_consultas" title="Número de Consultas"  readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">UF Mensual</td>
                            <td class="input-cell">
                                <input type="number" name="advanced_uf" step="0.01" title="UF Mensual" placeholder="0.00" readonly>
                            </td>
                            <td class="label">% de descuento</td>
                            <td class="input-cell">
                                <select name="advanced_descuento" title="% de descuento">
                                    <option value="0">0%</option>
                                    <option value="5">5%</option>
                                    <option value="10">10%</option>
                                    <option value="15%">15%</option>
                                    <option value="20">20%</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Nuevo Valor</td>
                            <td class="input-cell">
                                <input type="number" name="advanced_nuevo_valor" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    </table>

                    <!-- Subsección 3.3: Uso de Claves -->
                    <div class="subsection-header">3.3 PARA USO DE CLAVES</div>
                    <table>
                        <tr>
                            <td class="label">Nombre</td>
                            <td class="input-cell">
                                <input type="text" name="clave_nombre" title="Nombre para uso de claves">
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="clave_rut"
                                    title="Rut para uso de claves"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Correo</td>
                            <td class="input-cell">
                                <input type="email" name="clave_email" title="Correo para uso de claves"
                                    pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}$">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="clave_telefono" title="Teléfono para uso de claves"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                        </tr>
                    </table>

                    <!-- Subsección 3.4: Backup de Claves -->
                    <div class="subsection-header">3.4 BACKUP DE CLAVES</div>
                    <table>
                        <tr>
                            <td class="label">Nombre</td>
                            <td class="input-cell">
                                <input type="text" name="backup_clave_nombre" title="Nombre backup de claves">
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="backup_clave_rut"
                                    title="Rut backup de claves"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Correo</td>
                            <td class="input-cell">
                                <input type="email" name="backup_clave_email" title="Correo backup de claves"
                                    pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}$">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="backup_clave_telefono" title="Teléfono backup de claves"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                        </tr>
                    </table>

                    <!-- Nueva Subsección 3.5: Documentos -->
                    <div class="subsection-header">3.5 DOCUMENTOS</div>
                    <table>
                        <tr>
                            <td class="label">Carnet de Identidad (CI)</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci" id="archivo_ci" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Erut</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_erut" id="archivo_erut" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Extracto</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_extracto" id="archivo_extracto" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">CI Frente</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci_frente" id="archivo_ci_frente" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">CI Detrás</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci_detras" id="archivo_ci_detras" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Carpeta Tributaria</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_carpeta_tributaria" id="archivo_carpeta_tributaria" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Consulta de Terceros</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_consulta_terceros" id="archivo_consulta_terceros" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-prev" data-prev="2">Anterior</button>
                        <button type="submit" class="btn-submit">Guardar Formulario</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Tab Contenido: Tabla -->
    <?php if ($_SESSION['usuario_id'] == 4): ?>
    <div id="table-tab" class="tab-content">
        <div id="table-container">
            <h2>Registros de Clientes</h2>
            <button id="exportClients" class="export-button">
                <i class="fa fa-download"></i> Descargar Registros
            </button>
            <div class="table-controls">
                <input type="text" class="table-search" id="tableSearch" placeholder="Buscar...">
            </div>
            <div class="table-wrapper" style="overflow-x: auto; width: 100%; display: block;">
                <table id="user-table" style="min-width: 100%; width: auto; border-collapse: separate; border-spacing: 0;">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tipo Cliente</th>
                            <th>RUT</th>
                            <th>Razón Social</th>
                            <th>Nombre Representante 1</th>
                            <th>RUT Representante 1</th>
                            <th>Nombre Representante 2</th>
                            <th>RUT Representante 2</th>
                            <th>Nombre Representante 3</th>
                            <th>RUT Representante 3</th>
                            <th>Sistema Creación</th>
                            <th>Fecha Creación</th>
                            <th>Notaría</th>
                            <th>Actividad Económica</th>
                            <th>Fecha Constitución</th>
                            <th>Dirección</th>
                            <th>Comuna</th>
                            <th>Página Web</th>
                            <th>Email</th>
                            <th>Teléfono</th>
                            <th>Clasificación SII</th>
                            <th>Contacto Nombre</th>
                            <th>Contacto RUT</th>
                            <th>Contacto Teléfono</th>
                            <th>Contacto Email</th>
                            <th>Contacto Backup Nombre</th>
                            <th>Contacto Backup RUT</th>
                            <th>Contacto Backup Teléfono</th>
                            <th>Contacto Backup Email</th>
                            <th>Morosos Plan</th>
                            <th>Morosos Consultas</th>
                            <th>Morosos UF</th>
                            <th>Morosos Descuento</th>
                            <th>Morosos Nuevo Valor</th>
                            <th>Advanced Plan</th>
                            <th>Advanced Consultas</th>
                            <th>Advanced UF</th>
                            <th>Advanced Descuento</th>
                            <th>Advanced Nuevo Valor</th>
                            <!-- Columnas para claves de usuario -->
                            <th>Nombre Usuario Clave</th>
                            <th>RUT Usuario Clave</th>
                            <th>Email Usuario Clave</th>
                            <th>Teléfono Usuario Clave</th>
                            <th>Nombre Backup Clave</th>
                            <th>RUT Backup Clave</th>
                            <th>Email Backup Clave</th>
                            <th>Teléfono Backup Clave</th>
                            <!-- Nuevas columnas para documentos -->
                            <th>CI</th>
                            <th>ERUT</th>
                            <th>Extracto</th>
                            <th>CI Frente</th>
                            <th>CI Detrás</th>
                            <th>Carpeta Tributaria</th>
                            <th>Consulta Terceros</th>
                            <th>Fecha Creación Registro</th>
                            <th>ID Usuario</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        try {
                            $connection = getDBConnection();
                            if ($connection) {
                                $query = "SELECT * FROM form_experian ORDER BY id DESC";
                                $stmt = $connection->prepare($query);
                                $stmt->execute();
                                $clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                if (empty($clientes)) {
                                    echo '<tr><td colspan="54" class="no-data">No hay registros disponibles</td></tr>';
                                } else {
                                    foreach ($clientes as $cliente) {
                                        echo '<tr>';
                                        foreach ($cliente as $valor) {
                                            echo '<td>' . htmlspecialchars($valor ?? '') . '</td>';
                                        }
                                        echo '</tr>';
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            echo '<tr><td colspan="54" class="error-data">Error al cargar los datos: ' . htmlspecialchars($e->getMessage()) . '</td></tr>';
                            error_log("Error en la consulta de clientes: " . $e->getMessage());
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Tab Contenido: Nueva Opción -->
<div id="new-tab" class="tab-content active">
    <div class="container">
        <h2>Registro de prospecto</h2>

        <!-- Botón para abrir el modal -->
        <button id="openProspectoModal" class="btn-modal-open">
            <i class="fa fa-plus-circle"></i> Nuevo Prospecto
        </button>

        <!-- Modal para el formulario de prospecto -->
        <div id="prospectoModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h2>Registro de prospecto</h2>

                <form id="formEjecutivos" method="POST" action="guardar_prospecto.php">
                    <div class="section-header">DATOS DEL PROSPECTO</div>

                    <table>
                        <tr>
                            <td class="label">Nombre Ejecutivo</td>
                            <td class="input-cell">
                                <input type="text" name="nombre_prospecto" title="Nombre Prospecto" required>
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_ejecutivo"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12"
                                    required>
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Razón Social</td>
                            <td class="input-cell">
                                <input type="text" name="razon_social" title="Razón Social" required>
                            </td>
                            <td class="label">Rubro</td>
                            <td class="input-cell">
                                <input type="text" name="rubro" title="Rubro" required>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Contacto</td>
                            <td class="input-cell">
                                <input type="text" name="contacto" title="Contacto" required>
                            </td>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="tel" name="telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678"
                                    required>
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Fecha</td>
                            <td class="input-cell">
                                <input type="date" name="fecha" title="Fecha" value="<?php echo date('Y-m-d'); ?>" required>
                            </td>
                            <td class="label">Estado</td>
                            <td class="input-cell">
                                <select name="estado" title="Estado" required>
                                    <option value="">Seleccione...</option>
                                    <!-- <option value="Interesado">Interesado</option>
                                    <option value="No interesado">No interesado</option> -->
                                    <option value="Envio información">Envio información</option>
                                    <option value="Negociación">Negociación</option>
                                    <option value="Cerrado">Cerrado</option>
                                    <option value="B.O. Experian">B.O. Experian</option>
                                    <option value="Proceso de Firma">Proceso de Firma</option>
                                    <option value="Firmado">Firmado</option>
                                    <option value="Habilitado">Habilitado</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Observaciones</td>
                            <td class="input-cell">
                                <textarea name="observaciones" title="Observaciones" rows="3" style="width: 100%; resize: vertical;"></textarea>
                            </td>
                            <td class="label"></td>
                            <td class="input-cell"></td>
                        </tr>
                    </table>

                    <div class="form-actions">
                        <button type="submit" class="btn-submit" style="background-color: green;">
                            <i class="fa fa-save"></i> Guardar prospecto
                        </button>
                        <button type="reset" class="btn-reset">
                            <i class="fa fa-eraser"></i> Limpiar Formulario
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <?php if ($_SESSION['usuario_id'] == 4): ?>
        <div class="ejecutivos-table-container" style="margin-top: 30px;">
            <div class="section-header">Registros de prospectos</div>
            <button id="exportEjecutivos" class="export-button">
                <i class="fa fa-download"></i> Descargar Prospectos
            </button>
            <div class="table-controls" style="margin-bottom: 15px;">
                <input type="text" id="ejecutivos-search" class="table-search" placeholder="Buscar...">
            </div>
            <div class="table-wrapper">
                <table id="ejecutivos-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nombre Ejecutivo</th>
                            <th>RUT</th>
                            <th>Razón Social</th>
                            <th>Rubro</th>
                            <th>Contacto</th>
                            <th>Teléfono</th>
                            <th>Fecha</th>
                            <th>Estado</th>
                            <th>Observaciones</th>
                            <th>Fecha Registro</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        try {
                            $connection = getDBConnection();
                            if (!$connection) {
                                throw new Exception("No se pudo establecer conexión con la base de datos");
                            }

                            $query = "SELECT * FROM tb_experian_prospecto ORDER BY id DESC";
                            $stmt = $connection->prepare($query);
                            $stmt->execute();
                            $prospectos = $stmt->fetchAll(PDO::FETCH_ASSOC);

                            if (empty($prospectos)) {
                                echo '<tr><td colspan="11" class="no-data">No hay registros disponibles</td></tr>';
                            } else {
                                foreach ($prospectos as $prospecto) {
                                    echo '<tr>';
                                    echo '<td>' . htmlspecialchars($prospecto['id']) . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['nombre_ejecutivo']) . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['rut_ejecutivo']) . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['razon_social']) . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['rubro']) . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['contacto']) . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['telefono']) . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['fecha']) . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['estado']) . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['observaciones'] ?? '') . '</td>';
                                    echo '<td>' . htmlspecialchars($prospecto['fecha_registro']) . '</td>';
                                    echo '</tr>';
                                }
                            }
                        } catch (Exception $e) {
                            echo '<tr><td colspan="11" class="error-data">Error al cargar los datos: ' . $e->getMessage() . '</td></tr>';
                            error_log("Error en la consulta de prospectos: " . $e->getMessage());
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Scripts -->
<script>
    // Definir variables globales
    window.userIsAdmin = <?php echo json_encode(isset($_SESSION['usuario_id']) && $_SESSION['usuario_id'] == 4); ?>;
</script>
<script src="<?php echo version_url('js/form_experian.js'); ?>?v=<?php echo filemtime(__DIR__ . '/js/form_experian.js'); ?>"></script>
<script>
// Define user permissions for JavaScript
window.userIsAdmin = <?php echo $_SESSION['usuario_id'] == 4 ? 'true' : 'false'; ?>;

// Pre-load table data to avoid additional requests
<?php
// Only load data if user is admin
if ($_SESSION['usuario_id'] == 4):

    // Get client records data
    try {
        $connection = getDBConnection();
        if ($connection) {
            // Fetch client records
            $clientsQuery = "SELECT * FROM clientes ORDER BY id DESC";
            $clientsStmt = $connection->prepare($clientsQuery);
            $clientsStmt->execute();
            $clientData = $clientsStmt->fetchAll(PDO::FETCH_ASSOC);

            // Convert to JSON for JavaScript
            echo "window.preloadedClientData = " . json_encode(['success' => true, 'data' => $clientData]) . ";\n";
        }
    } catch (Exception $e) {
        error_log("Error loading client data: " . $e->getMessage());
        echo "window.preloadedClientData = {success: false, message: 'Error al cargar los datos iniciales: " . addslashes($e->getMessage()) . "'};\n";
    }

    // Get prospects data
    try {
        $connection = getDBConnection();
        if ($connection) {
            // Fetch prospects
            $prospectsQuery = "SELECT * FROM tb_experian_prospecto ORDER BY id DESC";
            $prospectsStmt = $connection->prepare($prospectsQuery);
            $prospectsStmt->execute();
            $prospectsData = $prospectsStmt->fetchAll(PDO::FETCH_ASSOC);

            // Convert to JSON for JavaScript
            echo "window.preloadedProspectsData = " . json_encode(['success' => true, 'data' => $prospectsData]) . ";\n";
        }
    } catch (Exception $e) {
        error_log("Error loading prospects data: " . $e->getMessage());
        echo "window.preloadedProspectsData = {success: false, message: 'Error al cargar los datos iniciales: " . addslashes($e->getMessage()) . "'};\n";
    }
endif;
?>

$(document).ready(function() {
    // Código existente para manejo de planes
    $('#morosos_plan').change(function() {
        var selectedPlan = $(this).val();
        if(selectedPlan) {
            console.log('Plan seleccionado:', selectedPlan); // Debug log
            $.ajax({
                url: 'get_plan_details.php',
                type: 'POST',
                data: { plan: selectedPlan },
                dataType: 'json',
                success: function(response) {
                    console.log('Respuesta del servidor:', response); // Debug log
                    if(response.success) {
                        $('#morosos_consultas').val(response.data.plan_consumo);
                        $('#morosos_uf').val(response.data.uf_mensual);
                    } else {
                        console.error('Error del servidor:', response.message);
                        alert('Error al obtener los datos del plan: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error AJAX:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                    alert('Error al obtener los datos del plan. Por favor, intente nuevamente.');
                }
            });
        } else {
            $('#morosos_consultas').val('');
            $('#morosos_uf').val('');
        }
    });

    // Código existente para plan Advanced SME
    $('select[name="advanced_plan"]').change(function() {
        var selectedPlan = $(this).val();
        if(selectedPlan) {
            console.log('Plan Advanced seleccionado:', selectedPlan); // Debug log
            $.ajax({
                url: 'get_advanced_plan_details.php',
                type: 'POST',
                data: { plan: selectedPlan },
                dataType: 'json',
                success: function(response) {
                    console.log('Respuesta del servidor (Advanced):', response); // Debug log
                    if(response.success) {
                        $('input[name="advanced_consultas"]').val(response.data.uf_transaccion);
                        $('input[name="advanced_uf"]').val(response.data.uf_mensual);
                    } else {
                        console.error('Error del servidor (Advanced):', response.message);
                        alert('Error al obtener los datos del plan Advanced: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error AJAX (Advanced):', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                    alert('Error al obtener los datos del plan Advanced. Por favor, intente nuevamente.');
                }
            });
        } else {
            $('input[name="advanced_consultas"]').val('');
            $('input[name="advanced_uf"]').val('');
        }
    });

    // Código existente para exportar tabla de clientes
    $('#exportClients').on('click', function(e) {
        e.preventDefault();
        console.log("Botón de exportación clickeado");

        try {
            // Verificar que la tabla existe
            const table = document.getElementById('user-table');
            if (!table) {
                console.error("No se encontró la tabla 'user-table'");
                alert("Error: No se encontró la tabla para exportar");
                return;
            }

            console.log("Tabla encontrada, procediendo con la exportación");

            // Crear una copia profunda de la tabla para modificar
            const tableClone = table.cloneNode(true);

            // Definir las columnas que contienen URLs
            const urlColumns = {
                'CI': true,
                'ERUT': true,
                'Extracto': true,
                'CI Frente': true,
                'CI Detrás': true,
                'Carpeta Tributaria': true,
                'Consulta Terceros': true
            };

            // Obtener índices de las columnas con URLs
            const headerCells = tableClone.querySelectorAll('th');
            const urlColumnIndexes = {};
            headerCells.forEach((cell, index) => {
                if (urlColumns[cell.textContent.trim()]) {
                    urlColumnIndexes[index] = true;
                }
            });

            // Modificar las celdas que contienen URLs para incluir fórmulas de hipervínculo
            const rows = tableClone.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                cells.forEach((cell, index) => {
                    if (urlColumnIndexes[index] && cell.textContent.trim()) {
                        const url = cell.textContent.trim();
                        cell.textContent = url;
                        cell._hyperlink = url;
                    }
                });
            });

            // Convertir la tabla a hoja de cálculo con hipervínculos
            const ws = XLSX.utils.table_to_sheet(tableClone, { raw: true });

            // Procesar las celdas para agregar hipervínculos
            for (let cellRef in ws) {
                if (ws[cellRef].v && ws[cellRef]._hyperlink) {
                    ws[cellRef].l = { Target: ws[cellRef].v };
                    ws[cellRef].f = `=HYPERLINK("${ws[cellRef].v}","${ws[cellRef].v}")`;
                }
            }

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "Clientes");

            // Generar nombre de archivo con fecEstadoha
            const date = new Date();
            const timestamp = date.getFullYear() + '-' +
                           ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
                           ('0' + date.getDate()).slice(-2);
            const filename = "Registros_Clientes_" + timestamp + ".xlsx";

            console.log("Intentando guardar archivo:", filename);

            // Escribir archivo y descargarlo
            XLSX.writeFile(wb, filename);

            console.log("Exportación completada con hipervínculos");
        } catch (error) {
            console.error("Error durante la exportación:", error);
            alert("Error al exportar: " + error.message);
        }
    });

    // Exportar tabla de ejecutivos
    $('#exportEjecutivos').on('click', function(e) {
        e.preventDefault();
        console.log("Botón de exportación de ejecutivos clickeado");

        try {
            const table = document.getElementById('ejecutivos-table');
            if (!table) {
                console.error("No se encontró la tabla 'ejecutivos-table'");
                alert("Error: No se encontró la tabla para exportar");
                return;
            }

            console.log("Tabla encontrada, procediendo con la exportación");

            const ws = XLSX.utils.table_to_sheet(table);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "Prospectos");

            const date = new Date();
            const timestamp = date.getFullYear() + '-' +
                           ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
                           ('0' + date.getDate()).slice(-2);
            const filename = "Registros_Prospectos_" + timestamp + ".xlsx";

            console.log("Intentando guardar archivo:", filename);
            XLSX.writeFile(wb, filename);

            console.log("Exportación completada");
        } catch (error) {
            console.error("Error durante la exportación:", error);
            alert("Error al exportar: " + error.message);
        }
    });

    // El manejo de la pestaña de ejecutivos ahora se realiza en el script de inicialización simple

    // Configurar el botón de exportación
    $('#exportEjecutivos').on('click', function(e) {
        e.preventDefault();
        console.log("Botón de exportación de ejecutivos clickeado");

        try {
            const table = document.getElementById('ejecutivos-table');
            if (!table) {
                console.error("No se encontró la tabla 'ejecutivos-table'");
                alert("Error: No se encontró la tabla para exportar");
                return;
            }

            console.log("Tabla encontrada, procediendo con la exportación");

            const ws = XLSX.utils.table_to_sheet(table);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "Prospectos");

            const date = new Date();
            const timestamp = date.getFullYear() + '-' +
                           ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
                           ('0' + date.getDate()).slice(-2);
            const filename = "Registros_Prospectos_" + timestamp + ".xlsx";

            console.log("Intentando guardar archivo:", filename);
            XLSX.writeFile(wb, filename);

            console.log("Exportación completada");
        } catch (error) {
            console.error("Error durante la exportación:", error);
            alert("Error al exportar: " + error.message);
        }
    });

    // Función para cargar ejecutivos si el usuario tiene permisos
    function cargarEjecutivos() {
        if (window.preloadedProspectsData && window.preloadedProspectsData.success) {
            console.log('Using preloaded prospects data');
            renderProspectsData(window.preloadedProspectsData);
        } else {
            console.log('Preloaded prospects data not available, fetching from server');
            // Mostrar indicador de carga en la tabla
            $('#ejecutivos-table tbody').html('<tr><td colspan="10" class="loading-data">Cargando datos...</td></tr>');

            $.ajax({
                url: 'cargar_prospecto.php',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    renderProspectsData(response);
                },
                error: function(xhr, status, error) {
                    console.error('Error AJAX al cargar ejecutivos:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                    $('#ejecutivos-table tbody').html('<tr><td colspan="10" class="error-data">Error de conexión. Intente nuevamente más tarde.</td></tr>');
                    mostrarMensaje('Error de conexión al cargar la tabla de prospectos.', 'error');
                }
            });
        }
    }

    // Helper function to render prospects data - Versión simple sin DataTables
    function renderProspectsData(response) {
        console.log('Iniciando renderProspectsData - versión simple');

        try {
            // Asegurarse de que la tabla tenga la estructura correcta
            if ($('#ejecutivos-table tbody').length === 0) {
                console.log('Agregando tbody a la tabla');
                $('#ejecutivos-table').append('<tbody></tbody>');
            }

            // Limpia la tabla
            $('#ejecutivos-table tbody').empty();

            if (!response.success) {
                console.error('Error al cargar ejecutivos:', response.message);
                $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="error-data">Error al cargar los datos: ' + response.message + '</td></tr>');
                mostrarMensaje('Error al cargar la tabla de prospectos: ' + response.message, 'error');
                return;
            }

            // Si no hay datos
            if (response.data.length === 0) {
                console.log('No hay datos para mostrar');
                $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="no-data">No hay registros disponibles</td></tr>');
                return;
            }

            console.log('Agregando ' + response.data.length + ' filas a la tabla');

            // Agregar datos a la tabla
            $.each(response.data, function(index, ejecutivo) {
                var row = '<tr>' +
                    '<td>' + ejecutivo.id + '</td>' +
                    '<td>' + ejecutivo.nombre_ejecutivo + '</td>' +
                    '<td>' + ejecutivo.rut_ejecutivo + '</td>' +
                    '<td>' + ejecutivo.razon_social + '</td>' +
                    '<td>' + ejecutivo.rubro + '</td>' +
                    '<td>' + ejecutivo.contacto + '</td>' +
                    '<td>' + ejecutivo.telefono + '</td>' +
                    '<td>' + ejecutivo.fecha + '</td>' +
                    '<td>' + ejecutivo.estado + '</td>' +
                    '<td>' + (ejecutivo.observaciones || "") + '</td>' +
                    '<td>' + ejecutivo.fecha_registro + '</td>' +
                    '</tr>';
                $('#ejecutivos-table tbody').append(row);
            });

            console.log('Datos agregados a la tabla correctamente');

            // Configurar el buscador simple
            setupTableSearch();

            return true;
        } catch (error) {
            console.error('Error al renderizar la tabla:', error);
            alert('Error al cargar la tabla: ' + error.message);
        }
    }

    // Función para configurar la búsqueda en la tabla
    function setupTableSearch() {
        $('#ejecutivos-search').on('keyup', function() {
            var searchText = $(this).val().toLowerCase();
            $('#ejecutivos-table tbody tr').each(function() {
                var rowText = $(this).text().toLowerCase();
                $(this).toggle(rowText.indexOf(searchText) > -1);
            });
        });
    }

    // Modificar el manejo del formulario de prospectos para usar AJAX
    $('#formEjecutivos').on('submit', function(e) {
        e.preventDefault();

        if (!confirm('¿Está seguro de guardar este prospecto?')) {
            return false;
        }

        var form = $(this);
        var formData = form.serialize();

        // Debug - mostrar todos los campos que se están enviando
        console.log("Datos del formulario:", formData);

        // Enviar directamente sin validación adicional
        $.ajax({
            url: form.attr('action'),
            type: form.attr('method'),
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');
            },
            success: function(response) {
                console.log("Respuesta del servidor:", response);
                if (response.success) {
                    // Mostrar mensaje de éxito
                    mostrarMensaje(response.message, 'success');

                    // Limpiar el formulario
                    form[0].reset();

                    // Si el usuario tiene permisos, actualizar la tabla
                    if (<?php echo $_SESSION['usuario_id'] == 4 ? 'true' : 'false'; ?>) {
                        cargarEjecutivos();
                    }
                } else {
                    // Mostrar mensaje de error
                    mostrarMensaje(response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error AJAX al enviar formulario:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
                mostrarMensaje('Error al enviar el formulario. Revise la consola para más detalles.', 'error');
            },
            complete: function() {
                $('.loading-overlay').remove();
            }
        });
    });

    // Función para mostrar mensajes
    function mostrarMensaje(mensaje, tipo) {
        // Eliminar mensajes anteriores
        $('.mensaje-alerta').remove();

        // Crear elemento de mensaje
        var claseAlerta = tipo === 'success' ? 'mensaje-exito' : 'mensaje-error';
        var mensajeHTML = '<div class="mensaje-alerta ' + claseAlerta + '">' +
                          '<span class="mensaje-texto">' + mensaje + '</span>' +
                          '<button class="cerrar-mensaje">&times;</button>' +
                          '</div>';

        // Insertar el mensaje en la parte superior del formulario
        $('.new-option-container').prepend(mensajeHTML);

        // Animar entrada del mensaje
        $('.mensaje-alerta').hide().slideDown(300);

        // Configurar botón de cierre
        $('.cerrar-mensaje').on('click', function() {
            $(this).parent().slideUp(300, function() {
                $(this).remove();
            });
        });

        // Auto-ocultar después de 5 segundos para mensajes de éxito
        if (tipo === 'success') {
            setTimeout(function() {
                $('.mensaje-alerta.' + claseAlerta).slideUp(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }
    }

    // Mejorar la función para cargar ejecutivos
    function cargarEjecutivos() {
        // Mostrar indicador de carga en la tabla
        $('#ejecutivos-table tbody').html('<tr><td colspan="10" class="loading-data">Cargando datos...</td></tr>');

        $.ajax({
            url: 'cargar_prospecto.php',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Limpia la tabla
                    $('#ejecutivos-table tbody').empty();

                    // Si no hay datos
                    if (response.data.length === 0) {
                        $('#ejecutivos-table tbody').append('<tr><td colspan="10" class="no-data">No hay registros disponibles</td></tr>');
                        return;
                    }

                    // Agregar datos a la tabla
                    $.each(response.data, function(index, ejecutivo) {
                        var row = '<tr>' +
                            '<td>' + ejecutivo.id + '</td>' +
                            '<td>' + ejecutivo.nombre_ejecutivo + '</td>' +
                            '<td>' + ejecutivo.rut_ejecutivo + '</td>' +
                            '<td>' + ejecutivo.razon_social + '</td>' +
                            '<td>' + ejecutivo.rubro + '</td>' +
                            '<td>' + ejecutivo.contacto + '</td>' +
                            '<td>' + ejecutivo.telefono + '</td>' +
                            '<td>' + ejecutivo.fecha + '</td>' +
                            '<td>' + ejecutivo.estado + '</td>' +
                            '<td>' + (ejecutivo.observaciones || "") + '</td>' +
                            '<td>' + ejecutivo.fecha_registro + '</td>' +
                            '</tr>';
                        $('#ejecutivos-table tbody').append(row);
                    });
                } else {
                    console.error('Error al cargar ejecutivos:', response.message);
                    $('#ejecutivos-table tbody').html('<tr><td colspan="10" class="error-data">Error al cargar los datos: ' + response.message + '</td></tr>');
                    mostrarMensaje('Error al cargar la tabla de prospectos: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error AJAX al cargar ejecutivos:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
                $('#ejecutivos-table tbody').html('<tr><td colspan="10" class="error-data">Error de conexión. Intente nuevamente más tarde.</td></tr>');
                mostrarMensaje('Error de conexión al cargar la tabla de prospectos.', 'error');
            }
        });
    }

    // Cargar tablas automáticamente al inicio si están activas
    function loadInitialData() {
        // Verificar si la tabla de registros está activa al inicio
        if ($('#table-tab').hasClass('active')) {
            console.log('Cargando tabla de registros automáticamente...');
            loadTableData();
        }

        // Verificar si la tabla de prospectos está activa al inicio y el usuario es admin
        if ($('#new-tab').hasClass('active') && window.userIsAdmin) {
            console.log('Cargando tabla de prospectos automáticamente...');
            cargarEjecutivos();
        }
    }

    // Llamar a la función después de que jQuery está listo
    loadInitialData();
});
</script>
<script>
// Debugging script to isolate tab switching issues
console.log("Tab debugging script loaded");

// Check jQuery availability
if (typeof jQuery !== 'undefined') {
    console.log("jQuery is available, version: " + jQuery.fn.jquery);

    // Force-add tab switching functionality
    jQuery(document).ready(function($) {
        console.log("Debug document ready fired");

        // Log all tab buttons found
        console.log("Found tab buttons: ", $('.tab-button').length);

        // Log all tab contents found
        console.log("Found tab contents: ", $('.tab-content').length);

        // Log the active tab
        console.log("Currently active tab: ", $('.tab-button.active').data('tab'));

        // Directly attach click handlers to tabs
        $('.tab-button').off('click').on('click', function() {
            const tabId = $(this).data('tab');
            console.log("Tab clicked: " + tabId);

            // Update active state on buttons
            $('.tab-button').removeClass('active');
            $(this).addClass('active');

            // Show selected tab content
            $('.tab-content').removeClass('active');
            $('#' + tabId).addClass('active');

            console.log("Tab activation complete for: " + tabId);
            return false; // Prevent any default behavior
        });
    });
} else {
    console.log("jQuery is NOT available, using native JS");

    // Add native JS tab switching
    document.addEventListener('DOMContentLoaded', function() {
        console.log("Native JS debug loaded");

        // Log all tab buttons found
        console.log("Found tab buttons: ", document.querySelectorAll('.tab-button').length);

        document.querySelectorAll('.tab-button').forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const tabId = this.getAttribute('data-tab');
                console.log("Tab clicked (native): " + tabId);

                // Update buttons
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                // Update tab content
                document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
                const targetTab = document.getElementById(tabId);
                if (targetTab) {
                    targetTab.classList.add('active');
                    console.log("Tab content activated");
                } else {
                    console.error("Could not find tab content with ID: " + tabId);
                }
            });
        });
    });
}

// Test tab visibility with CSS check
document.addEventListener('DOMContentLoaded', function() {
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(function(tab) {
        const id = tab.id;
        const display = window.getComputedStyle(tab).display;
        const visibility = window.getComputedStyle(tab).visibility;
        const opacity = window.getComputedStyle(tab).opacity;
        console.log(`Tab ${id}: display=${display}, visibility=${visibility}, opacity=${opacity}, hasActiveClass=${tab.classList.contains('active')}`);
    });
});
</script>
<script>
// Inicialización simple sin DataTables
$(document).ready(function() {
    console.log('Document ready - Inicializando tabla simple');

    // Verificar si jQuery está disponible
    if (typeof $ === 'undefined') {
        console.error('jQuery no está disponible');
        alert('Error: jQuery no está disponible. La tabla no funcionará correctamente.');
        return;
    }

    console.log('jQuery está disponible');

    // Verificar si la tabla de prospectos está activa al inicio y el usuario es admin
    if ($('#new-tab').hasClass('active') && <?php echo $_SESSION['usuario_id'] == 4 ? 'true' : 'false'; ?>) {
        console.log('Cargando tabla de prospectos automáticamente al inicio...');
        setTimeout(function() {
            cargarEjecutivos();
        }, 500); // Retraso para asegurar que todo esté cargado
    }

    // Asegurarse de que la tabla se cargue cuando se haga clic en la pestaña
    $('.tab-button[data-tab="new-tab"]').on('click', function() {
        if (<?php echo $_SESSION['usuario_id'] == 4 ? 'true' : 'false'; ?>) {
            console.log('Tab clicked - Cargando tabla de prospectos...');
            setTimeout(function() {
                cargarEjecutivos();
            }, 100);
        }
    });

    // Configurar el buscador simple
    setupTableSearch();
});
</script>

<style>
/* Estilos simples para la tabla */
.table-wrapper {
    overflow-x: auto;
    width: 100%;
    display: block;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Estilos para el botón de exportación */
.export-button {
    margin-bottom: 15px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #007bff;
    color: white;
    cursor: pointer;
    font-weight: bold;
    display: inline-block;
}

.export-button:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Estilos para la tabla */
#ejecutivos-table, #user-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

#ejecutivos-table th, #user-table th {
    background-color: #f8f9fa;
    padding: 10px;
    border: 1px solid #ddd;
    font-weight: bold;
    text-align: left;
    position: sticky;
    top: 0;
    z-index: 1;
}

/* Nuevos estilos para encabezados ordenables */
.sortable-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-right: 20px; /* Espacio para el ícono */
}

.sortable-table th.sortable:hover {
    background-color: #e9ecef;
}

.sortable-table th.sortable .sort-icon {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
    font-size: 14px;
}

.sortable-table th.sorted-asc .sort-icon {
    content: '↑';
    opacity: 1;
}

.sortable-table th.sorted-desc .sort-icon {
    content: '↓';
    opacity: 1;
}

.sortable-table th.sorted-asc .sort-icon::after {
    content: '↑';
}

.sortable-table th.sorted-desc .sort-icon::after {
    content: '↓';
}

/* Estilos para las filas */
#ejecutivos-table td, #user-table td {
    padding: 8px;
    border: 1px solid #ddd;
}

#ejecutivos-table tbody tr:nth-child(even), #user-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

#ejecutivos-table tbody tr:hover, #user-table tbody tr:hover {
    background-color: #f0f0f0;
}

/* Estilos para el buscador */
.table-controls {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-search {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 200px;
}

/* Estilos para mensajes */
.no-data, .error-data, .loading-data {
    padding: 20px;
    text-align: center;
    font-style: italic;
}

.no-data {
    color: #6c757d;
}

.error-data {
    color: #dc3545;
}

.loading-data {
    color: #007bff;
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
    .table-controls {
        flex-direction: column;
        align-items: flex-start;
    }

    .table-search {
        width: 100%;
        margin-top: 10px;
    }

    .export-button {
        width: 100%;
        text-align: center;
    }
}
</style>
<!-- Inicializar explícitamente la función de ordenamiento de tablas -->
<script>
    $(document).ready(function() {
        // Código existente...

        // Asegurar que se inicialice el ordenamiento de tabla
        setTimeout(function() {
            if (typeof initTableSort === 'function') {
                console.log('Initializing table sort from document ready');
                initTableSort();
            } else {
                console.warn('Table sort function not found');
            }
        }, 500);

        // Inicializar cuando se cambie de tab
        $('.tab-button').on('click', function() {
            const tabId = $(this).data('tab');
            console.log('Tab clicked: ' + tabId);

            setTimeout(function() {
                if (typeof initTableSort === 'function') {
                    console.log('Initializing table sort after tab change');
                    initTableSort();
                }
            }, 300);
        });
    });
</script>

<!-- Estilos para el modal -->
<style>
/* Estilos para el modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 900px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.close-modal {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover,
.close-modal:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}

.btn-modal-open {
    background-color: #2980b9;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin-bottom: 20px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-modal-open:hover {
    background-color: #3498db;
}
</style>

<!-- JavaScript para el modal -->
<script>
// Funcionalidad del modal
document.addEventListener('DOMContentLoaded', function() {
    // Obtener elementos del modal
    var modal = document.getElementById('prospectoModal');
    var btn = document.getElementById('openProspectoModal');
    var span = document.getElementsByClassName('close-modal')[0];

    // Abrir el modal cuando se hace clic en el botón
    btn.onclick = function() {
        modal.style.display = 'block';
    }

    // Cerrar el modal cuando se hace clic en la X
    span.onclick = function() {
        modal.style.display = 'none';
    }

    // Cerrar el modal cuando se hace clic fuera de él
    window.onclick = function(event) {
        if (event.target == modal) {
            modal.style.display = 'none';
        }
    }
});
</script>
</body>
</html>
