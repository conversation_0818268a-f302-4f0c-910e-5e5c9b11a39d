<?php
/**
 * Version checking endpoint
 * 
 * This file returns the current application version as JSON
 * Used by client-side scripts to detect when a new version is available
 */
require_once 'cache_utils.php';

// Aplicar cabeceras para prevenir caché
no_cache_headers();
header('Content-Type: application/json');

// Devolver la versión actual
echo json_encode([
    'version' => APP_VERSION,
    'timestamp' => time()
]);