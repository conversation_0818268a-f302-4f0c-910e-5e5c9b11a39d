<?php

header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.
session_start();

$inc = include("con_db.php");


$usuario = $_GET['usuario'];


if (isset($_GET['valor'])) {

  $valor = $_GET['valor'];
  $area = $_GET['area'];

  $KPI_2023 = $conex->query("
      select RutTecnicoOrig, NombreTecnico, Supervisor, TotalPtos
      , DIAS_BASE_DRIVE, SUM_OPERATIVO, Promedio, Q_ACTIVIDAD, Q_OPERATIVO, Q_AUSENTE, Q_VACACIONES
      , Q_LICENCIA, FACTOR_AUSENCIA
      , FACTOR_VACACIONES, DiasTrabajadosNDC, Promedio_ndc, SUM_RGU, Zona_Factura23, Porcen_Asistencia
      , CRUCE_PRODUCCION, CRUCE_CALIDAD, Categoria, Geo_Zona, Geo_MetaProduccin, KPI_PRODUCCION, Calidad30
      , GeoMetaCalidad, KPI_CALIDAD, CALIDAD_PERFOMANCE, CALIDAD_PRODUCCION, MONTO_COMISION, MONTO_PONDERADO
      , MONTO_FINAL, NoCumple, TotalCasos, PERIODO , PTOS_DESCUENTO , PtosOrig
      from  tb_paso_KPI2023  
      WHERE PERIODO  = '202305'
      AND   RutTecnicoOrig = '" . $valor . "'");

  $rowtkpi = mysqli_fetch_array($KPI_2023);
} else {
  $KPI_2023 = $conex->query("
      SELECT RutTecnicoOrig, NombreTecnico, Supervisor, TotalPtos
      , DIAS_BASE_DRIVE, SUM_OPERATIVO, Promedio, Q_ACTIVIDAD, Q_OPERATIVO, Q_AUSENTE, Q_VACACIONES
      , Q_LICENCIA, FACTOR_AUSENCIA
      , FACTOR_VACACIONES, DiasTrabajadosNDC
      , Promedio_ndc, SUM_RGU, Zona_Factura23, Porcen_Asistencia
      , CRUCE_PRODUCCION, CRUCE_CALIDAD
      , Categoria, Geo_Zona, Geo_MetaProduccin, KPI_PRODUCCION, Calidad30
      , GeoMetaCalidad, KPI_CALIDAD
      , CALIDAD_PERFOMANCE, CALIDAD_PRODUCCION
      , MONTO_COMISION, MONTO_PONDERADO
      , MONTO_FINAL, NoCumple, TotalCasos, PERIODO , PTOS_DESCUENTO , PtosOrig
      FROM   tb_paso_KPI2023  
      WHERE PERIODO  = '202305'
      AND  RutTecnicoOrig = '" . $_SESSION['RUT'] . "'");

  $rowtkpi = mysqli_fetch_array($KPI_2023);

}




$query_NDC = $conex->query("
 SELECT  
  SUM(IF(TP_DECLARADA='No' AND SLA_DECLARACION = 'PEND_SLA' AND DIFDIAS = 2  ,Q_ordenes,0)) VENCE_HOY
  ,SUM(IF(TP_DECLARADA='No' AND SLA_DECLARACION = 'PEND_SLA' AND DIFDIAS = 2  ,Ptos,0)) VENCE_HOY_PTOS 
  , SUM(IF(TP_DECLARADA='Si' AND SLA_DECLARACION = 'Mayor 72 horas',Q_ordenes,0)) DECLAR_OUTSLA 
  FROM 
(
   SELECT 
   TP_DECLARADA  , SLA_DECLARACION 
   , RutTecnicoOrig  
   , SUM(Ptos_referencial) Ptos 
   , COUNT(distinct Orden ) Q_ordenes
   , DIFDIAS
   FROM 
       tb_paso_pyNdc tppn 
   WHERE 
         RutTecnicoOrig  =  '" . $_SESSION['RUT'] . "'
   GROUP BY
   TP_DECLARADA  , SLA_DECLARACION , RutTecnicoOrig     , DIFDIAS      
) A
");

$rowBaseNdc = mysqli_fetch_array($query_NDC);






$carga = $conex->query("
SELECT
SUM( IF(DATE_FORMAT(`Fecha fin#`, '%d/%m/%Y') =    DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%d/%m/%Y') , 1,0)) total
,MAX(`Fecha fin#`) AS FechaMax
      FROM tb_paso_pyNdc tppn  

      ");

$cargarow = mysqli_fetch_array($carga);


?>



<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="APP TQW">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- T\chathe above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>TQW APP - INDICADORES</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/logo_con.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>
 -->
  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">



      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">



        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">NDC actualizado al
            <?php echo $cargarow['FechaMax']; ?>
          </h6>
        </div>

        <div class="navbar--toggler" id="affanNavbarToggler" data-bs-toggle="offcanvas" data-bs-target="#affanOffcanvas"
          aria-controls="affanOffcanvas">
          <span class="d-block"></span>
          <span class="d-block"></span>
          <span class="d-block"></span>
        </div>


      </div>
    </div>
  </div>

  <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1"
    aria-labelledby="affanOffcanvsLabel">

    <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
      aria-label="Close"></button>

    <div class="offcanvas-body p-0">
      <div class="sidenav-wrapper">
        <!-- Sidenav Profile -->
        <div class="sidenav-profile bg-gradient">
          <div class="sidenav-style1"></div>

          <!-- User Thumbnail -->
          <div class="user-profile">
            <!-- <img src="img/partner-img/RODOLFO.jpg" alt=""> -->
          </div>

          <!-- User Info -->
          <div class="user-info">
            <h6 class="user-name mb-0">
              <?php echo $_SESSION['usuario'] ?>
            </h6>

          </div>
        </div>

        <!-- Sidenav Nav -->
        <ul class="sidenav-nav ps-0">
          <li>
            <a href="home.php"><i class="bi bi-house-door"></i>HOME</a>
          </li>
          <li>
            <a href="TecnicoProduccion.php"><i class="bi bi-folder2-open"></i>PRODUCCION NDC
              <!-- <span class="badge bg-danger rounded-pill ms-2">220+</span> -->
            </a>
          </li>
          <li>
            <a href="Tecnico_calidad.php"><i class="bi bi-collection"></i>CALIDAD REACTIVA
              <span class="badge bg-success rounded-pill ms-2"></span>
            </a>
          </li>

          <li>
            <a href="Tecnico_DeclaracionOT.php"><i class="bi bi-collection"></i>DECLARACION DE ORDENES
              <span class="badge bg-success rounded-pill ms-2"></span>
            </a>
          </li>



          <li>
            <a href="#" class="nav-url"><i class="bi bi-globe"></i>ACCESOS DIRECTOS<span
                class="dropdown-icon"></span></a>
            <ul>
              <li>
                <a href="https://entrenamientovirtual.cl/course/"><i class="bi bi-globe"></i>Desafio Tecnico
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
              <li>
                <a href="http://172.17.113.6/eps/index.do"><i class="bi bi-globe"></i>NDC Declaracion Material
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
              <li>
                <a href="https://forms.gle/3m3ZUDby4ie5Y5Us7"><i class="bi bi-globe"></i>Registro Serie instaladas
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
              <li>
                <a href="https://lla.cloudcheck.net/t1gui/login-page"><i class="bi bi-globe"></i>Cloudcheck
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
            </ul>
          </li>
          <li>
            <a href=""><i class="bi bi-calendar-check-fill"></i>TURNOS
              <span class="badge bg-success rounded-pill ms-2"></span>
            </a>
          </li>
          <li>
            <div class="night-mode-nav">
              <i class="bi bi-moon"></i>CAMBIAR MODO OSCURO
              <div class="form-check form-switch">
                <input class="form-check-input form-check-success" id="darkSwitch" type="checkbox">
              </div>
            </div>
          </li>
          <li>
            <a href="login.php"><i class="bi bi-box-arrow-right"></i>CERRAR SESIÓN</a>
          </li>
        </ul>

        <!-- Social Info -->



      </div>
    </div>
  </div>


  <div class="page-content-wrapper py-3">


    <div class="container">
      <div class="card-body direction-rtl">


        <div class="toast custom-toast-1 toast-warning mb-2" role="alert" aria-live="assertive" aria-atomic="true"
          data-bs-delay="5000" data-bs-autohide="false">
          <div class="toast-body">
            <i class="bi bi-shield-fill-exclamation h1 mb-0"></i>
            <div class="toast-text ms-3 me-2">
              <p class="mb-0">TODAS LAS OT FINALIZADAS EL DIA SABADO 29.04 ,
                HAN SIDO EXCEPCIONADAS Y TUVIERON PLAZO HASTA AYER MARTES 02.05
                PARA SER DECLARADAS
              </p>
            </div>
          </div>

          <button class="btn btn-close btn-close-white position-absolute p-1" type="button" data-bs-dismiss="toast"
            aria-label="Close"></button>
        </div>


        <?php
        if ($cargarow['total'] == 0) {
          // Si es mayor a 0, muestras el elemento web
          echo '
            <div class="toast toast-autohide custom-toast-1 toast-warning mb-2" role="alert" aria-live="assertive"
            aria-atomic="true" data-bs-delay="8000" data-bs-autohide="true">
            <div class="toast-body">
              <i class="bi bi-shield-fill-exclamation h1 mb-0"></i>
              <div class="toast-text ms-3 me-2">
                <p class="mb-0">LA PRODUCCION DEL DÍA DE AYER AUN NO ESTA DISPONIBLE DESDE EL SISTEMA NDC</p>
                 </div>
            </div>

            <button class="btn btn-close btn-close-white position-absolute p-1" type="button" data-bs-dismiss="toast"
            aria-label="Close"></button>
          </div>




            ';
        } else {
          // Si es mayor a 0, muestras el elemento web
          echo '
            <div class="toast toast-autohide custom-toast-1 toast-success mb-2" role="alert" aria-live="assertive"
            aria-atomic="true" data-bs-delay="8000" data-bs-autohide="true">
            <div class="toast-body">
            <i class="bi bi-shield-check text-white h1 mb-0"></i>
            <div class="toast-text ms-3 me-2">
                <p class="mb-0">LA PRODUCCION DEL DÍA DE AYER EN NDC YA ESTA DISPONIBLE </p>
             
              </div>
            </div>

            <button class="btn btn-close position-absolute p-1" type="button" data-bs-dismiss="toast"
              aria-label="Close"></button>
          </div>


            

          <!-- Warning Toast-->
          <div class="toast custom-toast-1 toast-danger mb-2" role="alert" aria-live="assertive" aria-atomic="true"
            data-bs-delay="5000" data-bs-autohide="false">
            <div class="toast-body">
              <i class="bi bi-shield-fill-exclamation text-white h1 mb-0"></i>
              <div class="toast-text ms-3 me-2">
               
              <p class="mb-0 text-white">HOY VENCERÁN ' . ROUND($rowBaseNdc['VENCE_HOY_PTOS'], 2) . ' PTOS POR NO DECLARACIÓN DE ORDENES</p>
              <small class="d-block">Un total de ' . $rowBaseNdc['VENCE_HOY'] . ' OT</small>
              </div>
            </div>

            <button class="btn btn-close btn-close-white position-absolute p-1" type="button" data-bs-dismiss="toast"
              aria-label="Close"></button>
          </div>

          

            ';
        }
        ?>



      </div>

    </div>


    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>SEGMENTO Y BONOS DEL MES EN CURSO</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body direction-rtl">
          <div class="row">
            <div class="col-6">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-danger"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo $rowtkpi['Geo_Zona']; ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">GEO ZONA</p>
              </div>
            </div>

            <div class="col-6">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-danger"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo $rowtkpi['Categoria']; ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">SEGMENTO</p>
              </div>
            </div>



            <div class="col-6">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-cash-stack mb-2 text-danger"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo '$' . number_format($rowtkpi['MONTO_COMISION'], 0, ',', '.'); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">BONO BRUTO</p>
              </div>
            </div>
            <div class="col-6">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-cash-stack mb-2 text-danger"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo '$' . number_format($rowtkpi['MONTO_FINAL'], 0, ',', '.'); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">BONO PONDERADO</p>
              </div>
            </div>



          </div>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="element-heading mt-3">
        <h6>PRODUCCION NDC</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body direction-rtl">
          <div class="row">
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['PtosOrig'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">PUNTAJE NDC</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-danger"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowBaseNdc['DECLAR_OUTSLA'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">OT DECLARADA FUERA DE SLA</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line-fill mb-2 text-danger"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['PTOS_DESCUENTO'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line">
                </span>
                <p class="mb-0 fz-12">PTOS A DESCONTAR</p>
              </div>
            </div>


            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line-fill mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['TotalPtos'], 1); ?>
                  </span></span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">PUNTAJE FINAL</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['Q_OPERATIVO'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">Dias operativo</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['Promedio'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">Promedio Produccion</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['Geo_MetaProduccin'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">Geo Meta Produccion</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo 100 * round($rowtkpi['KPI_PRODUCCION'], 3) . '%';
                    ; ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">Cumplimiento</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line-fill mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo 100 * round($rowtkpi['CALIDAD_PRODUCCION'], 3) . '%';
                    ; ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">Tramo de produccion</p>
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>




    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Calidad Reactiva</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body direction-rtl">
          <div class="row">
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter">
                    <?php echo 100 * round($rowtkpi['Calidad30'], 3) . '%';
                    ; ?>
                  </span>
                </h4>
                <p class="mb-0 fz-12">Calidad 30</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter">
                    <?php echo $rowtkpi['NoCumple']; ?>
                  </span>
                </h4>
                <p class="mb-0 fz-12">NO CUMPLE</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter">
                    <?php echo $rowtkpi['TotalCasos']; ?>
                  </span>
                </h4>
                <p class="mb-0 fz-12">TOTAL ACTIVIDAD</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter">
                    <?php echo 100 * round($rowtkpi['GeoMetaCalidad'], 3) . '%';
                    ; ?>
                  </span>
                </h4>
                <p class="mb-0 fz-12">GEO META CALIDAD</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter">
                    <?php echo 100 * round($rowtkpi['KPI_CALIDAD'], 3) . '%';
                    ; ?>
                  </span>
                </h4>
                <p class="mb-0 fz-12">Cumplimiento</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line-fill mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter">
                    <?php echo 100 * round($rowtkpi['CALIDAD_PERFOMANCE'], 3) . '%';
                    ; ?>
                  </span>
                </h4>
                <p class="mb-0 fz-12">Tramo cumplimiento</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Turnos</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body direction-rtl">
          <div class="row">
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['Q_OPERATIVO'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">Dias operativo</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['Q_AUSENTE'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">Dias Ausente</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['Q_VACACIONES'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">Dias Vacaciones</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['Q_LICENCIA'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">Dias Licencia</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['FACTOR_AUSENCIA'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">FACTOR AUSENCIA</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-success"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtkpi['FACTOR_VACACIONES'], 1); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">FACTOR VACACIONES</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>







    <!-- Footer Nav -->
    <div class="footer-nav-area" id="footerNav">
      <div class="container px-0">
        <!-- Footer Content -->
        <div class="footer-nav position-relative">
          <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
            <li class="active">
              <a href="home.php">
                <i class="bi bi-house"></i>
                <span>Home</span>
              </a>
            </li>

            <li class="active">
            <a href="TecnicoProduccion.php?usuario=<?php echo $usuario; ?>">
              <i class="bi bi-bar-chart-fill"></i>
              <span>Graficos</span>
            </a>
          </li>






          </ul>
        </div>
      </div>
    </div>

    <!-- All JavaScript Files -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/slideToggle.min.js"></script>
    <script src="js/internet-status.js"></script>
    <script src="js/tiny-slider.js"></script>
    <script src="js/venobox.min.js"></script>
    <script src="js/countdown.js"></script>
    <script src="js/rangeslider.min.js"></script>
    <script src="js/vanilla-dataTables.min.js"></script>
    <script src="js/index.js"></script>
    <script src="js/imagesloaded.pkgd.min.js"></script>
    <script src="js/isotope.pkgd.min.js"></script>
    <script src="js/dark-rtl.js"></script>
    <script src="js/active.js"></script>
    <script src="js/pwa.js"></script>
    <script src="js/chart-active.js"></script>
    <script src="js/apexcharts.min.js"></script>
</body>

</html>