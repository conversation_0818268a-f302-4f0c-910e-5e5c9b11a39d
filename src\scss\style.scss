/* -----------------------
[Master Stylesheet]

Template Name: <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> Mobile HTML Template
Template Author: Designing World
Template Author URL: https://themeforest.net/user/designing-world
Version: 1.6.0
Last Updated: 23 Dec, 2022
Created: December 10, 2020

[Table of Contents]
    * Variables
        + Fonts
        + Colors
        + Responsive
    * Main Styles
        + Preloader
        + Bs-color-reset
        + Reboot
        + Shortcode
        + Miscellaneous
        + Internet-status
        + Animation
        + Register
        + Header
        + Sidenav
        + Footer
    * Element CSS
        + Btn
        + Badge
        + Breadcrumb
        + Accordion
        + Alerts
        + Divider
        + Coming-soon
        + Card
        + Carousel
        + Countdown
        + Counterup
        + Form
        + Hero-block
        + Image-gallery
        + List-group
        + Loader
        + Modal
        + Notification
        + Pagination
        + Partner
        + Price-table
        + Progress
        + Rating
        + Scrollspy
        + Table
        + Testimonial
        + Timeline
        + Toast
    * Page CSS
        + Blog
        + Cart
        + Language
        + Product
        + Service
        + Team
        + User-profile
        + Demo
    * RTL & Dark CSS
        + RTL
        + Dark

# [font-family]
"Poppins", sans-serif;
----------------------- */

// Third Party CSS Libraries
@import url('css/bootstrap.min.css');
@import url('css/bootstrap-icons.css');
@import url('css/tiny-slider.css');
@import url('css/venobox.min.css');
@import url('css/rangeslider.css');
@import url('css/vanilla-dataTables.min.css');
@import url('css/apexcharts.css');

// Fonts 
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');

// Variables
@import "_fonts.scss";
@import "_colors.scss";
@import "_responsive.scss";

// Main CSS
@import "_preloader.scss";
@import "_bs-color-reset.scss";
@import "_reboot.scss";
@import "_animation.scss";

@import "_shortcode.scss";
@import "_miscellaneous.scss";
@import "_internet-status.scss";
@import "_register.scss";

@import "_header.scss";
@import "_sidenav.scss";
@import "_footer.scss";

// Element CSS
@import "_btn.scss";
@import "_badge.scss";
@import "_breadcrumb.scss";
@import "_accordion.scss";
@import "_alerts.scss";
@import "_divider.scss";
@import "_coming-soon.scss";
@import "_card.scss";
@import "_chats.scss";
@import "_carousel.scss";
@import "_countdown.scss";
@import "_counterup.scss";
@import "_form.scss";
@import "_hero-block.scss";
@import "_image-gallery.scss";
@import "_list-group.scss";
@import "_loader.scss";
@import "_modal.scss";
@import "_notification.scss";
@import "_pagination.scss";
@import "_partner.scss";
@import "_price-table.scss";
@import "_progress.scss";
@import "_rating.scss";
@import "_scrollspy.scss";
@import "_table.scss";
@import "_testimonial.scss";
@import "_timeline.scss";
@import "_toast.scss";
@import "_tabs.scss";

// Page CSS
@import "_blog.scss";
@import "_cart.scss";
@import "_language.scss";
@import "_product.scss";
@import "_service.scss";
@import "_team.scss";
@import "_user-profile.scss";
@import "_demo.scss";

// RTL & Dark CSS
@import "_rtl.scss";
@import "_dark.scss";