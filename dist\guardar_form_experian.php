<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Verificación de autenticación
if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
    // Redireccionar a login
    header('Location: ' . version_url('login.php'));
    exit;
}

// Incluir el archivo de conexión
require_once 'con_db.php';

// Verificar si es una solicitud POST
if ($_SERVER["REQUEST_METHOD"] != "POST") {
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

try {
    // Sanitizar todas las entradas
    $tipo_cliente = filter_input(INPUT_POST, 'tipo_cliente', FILTER_SANITIZE_STRING);
    $rut = filter_input(INPUT_POST, 'rut', FILTER_SANITIZE_STRING);
    $razon_social = filter_input(INPUT_POST, 'razon_social', FILTER_SANITIZE_STRING);
    // ...existing sanitization for other fields...
    
    // Nuevos campos para uso de claves
    $key_user_nombre = filter_input(INPUT_POST, 'clave_nombre', FILTER_SANITIZE_STRING);
    $key_user_rut = filter_input(INPUT_POST, 'clave_rut', FILTER_SANITIZE_STRING);
    $key_user_email = filter_input(INPUT_POST, 'clave_email', FILTER_SANITIZE_EMAIL);
    $key_user_telefono = filter_input(INPUT_POST, 'clave_telefono', FILTER_SANITIZE_STRING);
    
    // Nuevos campos para backup de claves
    $key_user_backup_nombre = filter_input(INPUT_POST, 'backup_clave_nombre', FILTER_SANITIZE_STRING);
    $key_user_backup_rut = filter_input(INPUT_POST, 'backup_clave_rut', FILTER_SANITIZE_STRING);
    $key_user_backup_email = filter_input(INPUT_POST, 'backup_clave_email', FILTER_SANITIZE_EMAIL);
    $key_user_backup_telefono = filter_input(INPUT_POST, 'backup_clave_telefono', FILTER_SANITIZE_STRING);
    
    // Preparar la consulta SQL incluyendo los nuevos campos
    $sql = "INSERT INTO form_experian (
                tipo_cliente, rut, razon_social, 
                /* ...otros campos existentes... */
                morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor,
                advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor,
                key_user_nombre, key_user_rut, key_user_email, key_user_telefono,
                key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono,
                usuario_id
            ) VALUES (
                :tipo_cliente, :rut, :razon_social,
                /* ...otros valores existentes... */
                :morosos_plan, :morosos_consultas, :morosos_uf, :morosos_descuento, :morosos_nuevo_valor,
                :advanced_plan, :advanced_consultas, :advanced_uf, :advanced_descuento, :advanced_nuevo_valor,
                :key_user_nombre, :key_user_rut, :key_user_email, :key_user_telefono,
                :key_user_backup_nombre, :key_user_backup_rut, :key_user_backup_email, :key_user_backup_telefono,
                :usuario_id
            )";
    
    $stmt = $pdo->prepare($sql);
    
    // Vincular parámetros
    $stmt->bindParam(':tipo_cliente', $tipo_cliente);
    $stmt->bindParam(':rut', $rut);
    $stmt->bindParam(':razon_social', $razon_social);
    // ...existing bindings for other fields...
    
    // Vincular nuevos parámetros para uso de claves
    $stmt->bindParam(':key_user_nombre', $key_user_nombre);
    $stmt->bindParam(':key_user_rut', $key_user_rut);
    $stmt->bindParam(':key_user_email', $key_user_email);
    $stmt->bindParam(':key_user_telefono', $key_user_telefono);
    
    // Vincular nuevos parámetros para backup de claves
    $stmt->bindParam(':key_user_backup_nombre', $key_user_backup_nombre);
    $stmt->bindParam(':key_user_backup_rut', $key_user_backup_rut);
    $stmt->bindParam(':key_user_backup_email', $key_user_backup_email);
    $stmt->bindParam(':key_user_backup_telefono', $key_user_backup_telefono);
    
    // Vincular ID de usuario de sesión
    $usuario_id = $_SESSION['usuario_id'];
    $stmt->bindParam(':usuario_id', $usuario_id);
    
    // Ejecutar la consulta
    if ($stmt->execute()) {
        // Respuesta exitosa
        echo json_encode(['success' => true, 'message' => 'Registro guardado exitosamente']);
    } else {
        // Error en la consulta
        echo json_encode(['success' => false, 'message' => 'Error al guardar el registro: ' . implode(', ', $stmt->errorInfo())]);
        error_log('Error en la inserción: ' . implode(', ', $stmt->errorInfo()));
    }
} catch (PDOException $e) {
    // Error de base de datos
    echo json_encode(['success' => false, 'message' => 'Error en la base de datos']);
    error_log('Error PDO: ' . $e->getMessage());
} catch (Exception $e) {
    // Otros errores
    echo json_encode(['success' => false, 'message' => 'Error inesperado']);
    error_log('Error general: ' . $e->getMessage());
}
?>
