<!DOCTYPE html>
<html id="previewPage" lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="APP TQW">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>APP TQW - Bienvenido</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/logo_con.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  

  <!-- Offcanvas -->
  <div class="offcanvas offcanvas-start" data-bs-scroll="true" tabindex="-1" id="othersTemplate"
    aria-labelledby="othersTemplateLabel">
    <div class="offcanvas-header">
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>

    <div class="offcanvas-body">
      <!-- Single Item -->
      <div class="others-items-preview shadow-sm mb-3">
        <div class="alert alert-info mb-0" role="alert">
          <img class="mb-2" src="img/demo-img/suha.png" alt="">
          <h6>Suha - PWA Ecommerce Mobile</h6>
          <a class="btn btn-success btn-sm w-100 rounded-pill" target="_blank"
            href="https://themeforest.net/item/suha-multipurpose-ecommerce-mobile-template/25294162">View Demo <i
              class="ms-1 bi bi-box-arrow-up-right"></i></a>
        </div>
      </div>

      <!-- Single Item -->
      <div class="others-items-preview shadow-sm mb-3">
        <div class="alert alert-info mb-0" role="alert">
          <img class="mb-2" src="img/demo-img/newsten.png" alt="">
          <h6>Newsten - Blog & Magazine Mobile</h6>
          <a class="btn btn-success btn-sm w-100 rounded-pill" target="_blank"
            href="https://themeforest.net/item/newsten-blog-magazine-mobile-html-template/26265024">View Demo <i
              class="ms-1 bi bi-box-arrow-up-right"></i></a>
        </div>
      </div>

      <!-- Single Item -->
      <div class="others-items-preview shadow-sm mb-3">
        <div class="alert alert-info mb-0" role="alert">
          <img class="mb-2" src="img/demo-img/saasbox.png" alt="">
          <h6>Saasbox - Multipurpose HTML Template</h6>
          <a class="btn btn-success btn-sm w-100 rounded-pill" target="_blank"
            href="https://themeforest.net/item/saasbox-multipurpose-html-template-for-saas/25607146">View Demo <i
              class="ms-1 bi bi-box-arrow-up-right"></i></a>
        </div>
      </div>

      <!-- Single Item -->
      <div class="others-items-preview shadow-sm">
        <div class="alert alert-info mb-0" role="alert">
          <img class="mb-2" src="img/demo-img/funto.png" alt="">
          <h6>Funto - HTML NFT Marketplace</h6>
          <a class="btn btn-success btn-sm w-100 rounded-pill" target="_blank"
            href="https://themeforest.net/item/funto-html-nft-marketplace/35740238">View Demo <i
              class="ms-1 bi bi-box-arrow-up-right"></i></a>
        </div>
      </div>
    </div>
  </div>

  <div class="preview-iframe-wrapper">
    <!-- Header Area -->
    <div class="demo-header-wrapper">
      <div class="container demo-container">
        <!-- Header Content -->
        <div
          class="header-content header-style-five position-relative d-flex align-items-center justify-content-between">
          <!-- Logo Wrapper 
          <div class="logo-wrapper">
            <a href="home.html">
              <img src="img/core-img/logo.png" alt="">
            </a>
          </div>-->

          <!-- Settings -->
          <div class="setting-wrapper">
            <div class="setting-trigger-btn" id="settingTriggerBtn">
              <i class="bi bi-gear"></i>
              <span></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview Hero Area -->
    <div class="preview-hero-area">
      <span class="big-shadow-text">TQW</span>
      <div class="container demo-container direction-rtl">
        <div class="row g-2 align-items-center justify-content-between">
         

          <div class="col-12 col-lg-5">
            <div class="text-center">
              <iframe class="shadow-lg" src="login.html"></iframe>
            </div>
          </div>

          <div class="col-12 col-lg-3">
            <div class="text-lg-end">
              <!-- Mobile Live Preview -->
              <div class="live-preview-btn mb-3">
                <a class="btn btn-primary btn-lg d-lg-none mb-5 rounded-pill" href="hero-blocks.html" target="_blank">
                  w the button to live preview
                </a>
              </div>

              <!-- QR Code -->
              <div class="qr-code-wrapper shadow border">
                <img src="img/demo-img/QR_CODE_TQW.png" alt="">
                <h6 class="mb-0">Escanea el codigo QR y <br> continua en formato Mobile</h6>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    

    <!-- Footer Area -->
  
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>

  <script>
    if (document.querySelectorAll('.promotionVideo').length > 0) {
      window.addEventListener('load', function () {
        new VenoBox({
          selector: '.promo-video',
          popup: true,
          overlayColor: 'rgba(15,7,15,0.75)',
          spinner: 'pulse',
          navSpeed: 400
        });
      });
    }
  </script>
</body>

</html>