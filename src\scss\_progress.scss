/* :: Progress */

.skill-progress-bar {
    position: relative;
    z-index: 1;
    margin-bottom: 1rem;

    &:last-child {
        margin-bottom: 0;
    }

    .skill-icon {
        width: 2.5rem;
        height: 2.5rem;
        flex: 0 0 2.5rem;
        max-width: 2.5rem;
        margin-right: 1rem;
        border-radius: .5rem;
        border: 1px solid $border;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .skill-data {
        width: 100%;
    }

    .skill-name {
        p {
            color: $heading;
            font-weight: 500;
            font-size: 14px;
        }

        small {
            font-weight: 500;
        }
    }
}

.progress-info {
    span {
        display: inline-block;
        font-size: 12px;
        margin-top: 0.25rem;
    }
}

.single-task-progress {
    position: relative;
    z-index: 1;
    margin-bottom: 1.5rem;

    &:last-child {
        margin-bottom: 0;
    }

    .who-working {
        a {
            display: inline-block;
            margin-left: -0.375rem;

            &:first-child {
                margin-left: 0;
            }

            img {
                width: 1.5rem;
                height: 1.5rem;
                border: 2px solid $border;
                border-radius: 50%;
            }
        }
    }
}