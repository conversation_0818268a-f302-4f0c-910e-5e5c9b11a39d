<?php
// Conexión a la base de datos


$inc = include("con_db.php");
session_start();

// Obtener los datos del formulario
$orden = $_POST['orden'];
$categoria = $_POST['Categoria'];
$motivo = $_POST['motivo'];
$rev_super = $_POST['Aplica'];
$usuario = $_POST['Usuario'];

// Realizar la consulta SQL para insertar los datos en la tabla
$sql = "UPDATE tb_solicitud_excep_cal 
    SET rev_super = '$rev_super' , 
    fecha_super = now() ,
    mot_gen_super = '$categoria' , 
    motivo_rechazo = '$motivo' 
    WHERE orden = '$orden'
    AND rev_super = 'PENDIENTE'
";

// Ejecutar la consulta
if (mysqli_query($conex, $sql)) {
    echo "<script>alert('Datos guardados correctamente');window.history.back();</script>";
} else {
    echo "Error al guardar los datos: " . mysqli_error($conex);
}

// Cerrar la conexión
mysqli_close($conex);
?>