/* Chat */

.chat-user-status-slides {
	position: relative;
	z-index: 1;

	.user-status-slide {
		a {
			margin: .5rem 0;
			padding: .5rem .25rem .375rem;
			background-color: $white;
			border-radius: 8px;
			box-shadow: 0 1px 2px 1px #d7def4;
			display: block;
			position: relative;
			z-index: 1;
			width: 3.75rem;
			text-align: center;

			img {
				width: 2rem;
				border-radius: 50%;
				margin: 0 auto;
			}

			.active-status {
				position: absolute;
				width: .75rem;
				height: .75rem;
				border-radius: 50%;
				top: .5rem;
				right: .5rem;
				border: 2px solid $white;
				background-color: $success;
			}

			p {
				font-size: 11px;
			}
		}

		&.offline {
			a {
				.active-status {
					background-color: $secondary;
				}
			}
		}
	}
}

.add-new-contact-wrap a {
	position: fixed;
	width: 2.5rem;
	height: 2.5rem;
	z-index: 1000;
	background-color: $primary;
	text-align: center;
	line-height: 2.25rem;
	border-radius: 50%;
	bottom: 82px;
	right: 20px;
	color: $white;

	i {
		line-height: 2.5rem;
	}
}

.add-new-contact-modal {
	.modal-body {
		textarea.form-control {
			min-height: 90px;
		}
	}
}

.chat-user-info {
	width: calc(100% - 60px);
}

.chat-user-list {
	border-radius: .5rem;
	box-shadow: 0 1px 2px 1px #d7def4;
}

.chat-user-list li {
	display: flex;
	align-items: center;
	background-color: $white;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	transition-duration: 300ms;
	justify-content: space-between;

	&:hover,
	&:focus {
		background-color: $gray;
	}

	.chat-options-btn {
		line-height: 1;

		.dropdown-toggle::before {
			display: none;
		}

		.btn {
			padding: 0 !important;
			color: $text;
			border: 0 !important;

			&.show {
				border: 0 !important;
			}

			&:focus {
				box-shadow: none;
			}
		}

		.dropdown-menu {
			min-width: 6.5rem;
			padding: .5rem .75rem;
			border: 0;

			li {
				display: block;

				&:hover,
				&:focus {
					background-color: $white;
				}
			}

			a {
				color: $text;
				display: flex;
				align-items: center;
				padding: .375rem 0;
				font-size: 12px;

				i {
					margin-right: .25rem;
					font-size: 14px;
					min-width: 1rem;
				}

				&:hover,
				&:focus {
					color: $primary;
				}
			}
		}
	}

	a {
		width: 94%;
	}

	&:first-child {
		border-top-left-radius: .5rem;
		border-top-right-radius: .5rem;
	}

	&:last-child {
		border-bottom-left-radius: .5rem;
		border-bottom-right-radius: .5rem;
		border-bottom: 0;
	}

	.chat-user-thumbnail {
		position: relative;
		z-index: 1;
		width: 2.25rem;
		height: 2.25rem;
		background-color: $text-gray;
		border-radius: 50%;
		flex: 0 0 2.25rem;

		.active-status {
			position: absolute;
			width: .75rem;
			height: .75rem;
			border-radius: 50%;
			bottom: 1px;
			right: 0;
			border: 2px solid $white;
			background-color: $success;
		}
	}

	.chat-user-info {
		h6 {
			font-size: 14px;
			color: $text;
		}

		.last-chat {
			p {
				font-size: 12px;
			}
		}
	}

	&.chat-unread {
		.chat-user-info {
			.last-chat {
				p {
					color: $heading;
					font-weight: 500;
				}
			}
		}
	}

	&.offline {
		.chat-user-thumbnail {
			.active-status {
				background-color: $secondary;
			}
		}
	}
}

#chat-wrapper {
	min-height: calc(100vh - 112px);

	&.calling-screen-active {
		max-height: calc(100vh - 115px) !important;
	}
}

.chat-user--info {
	.user-thumbnail-name {
		display: flex;
		align-items: center;
		margin-left: 0.375rem;

		img {
			width: 30px;
			height: 30px;
			border-radius: 50%;
		}

		p {
			color: $heading;
			display: block;
			line-height: 1;
			margin-bottom: 2px;
			font-weight: 500;
		}

		.active-status,
		.offline-status {
			line-height: 1;
			display: block;
			color: $success;
			font-size: 11px;
		}
	}
}

.single-chat-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 1rem;
	width: 100%;

	&:last-child {
		margin-bottom: 0;
	}

	.user-avatar {
		position: relative;
		z-index: 1;
		margin-right: .5rem;
		flex: 0 0 2rem;
		width: 2rem;
		height: 2rem;
		border-radius: 50%;
		background-color: $white;

		.name-first-letter {
			position: absolute;
			font-size: 1rem;
			font-weight: 700;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			z-index: -5;
			line-height: 1;
			color: $primary;
		}

		img {
			width: 2rem;
			height: 2rem;
			border-radius: 50%;
		}
	}

	.user-message {
		max-width: 22rem;

		.message-content {
			display: flex;
			align-items: center;
			margin-bottom: .25rem;
		}

		.download-file-wrap {
			padding: 12px;
			border-radius: 18px;
			background-color: $white;

			.download-avatar {
				flex: 0 0 3rem;
				width: 3rem;
				height: 3rem;
				margin-right: .5rem;
				border-radius: .25rem;
				font-size: 1.5rem;
				color: $primary;
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;

				.dl-icon i {
					transition-duration: 400ms;
				}

				.download-btn {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					opacity: 0;
					visibility: hidden;
					z-index: 10;
				}
			}

			.download-file-info {
				.file-name {
					font-size: 14px;
					font-weight: 500;
					max-width: 160px;
				}

				.file-size {
					font-size: 12px;
				}
			}

			&:hover,
			&:focus {
				.download-avatar {
					.dl-icon i {
						opacity: 0;
					}

					.download-btn {
						opacity: 1;
						visibility: visible;
					}
				}
			}
		}

		.single-message {
			display: block;

			p {
				padding: 8px 12px;
				background-color: $white;
				border-radius: 18px;
				font-size: 14px;
				margin-bottom: 0;
			}

			img {
				border-radius: 12px;
				max-width: 120px;
			}

			.typing {
				background-color: $white;
				display: flex;
				align-items: center;
				padding: 18px 15px 14px;
				border-radius: 60px;

				.dot {
					position: relative;
					flex: 0 0 .375rem;
					width: .375rem;
					height: .375rem;
					border-radius: 50%;
					background-color: $border;
					animation: typingg 1000ms linear 0s infinite;
					margin: 0 .25rem;

					&:nth-child(2) {
						animation-delay: 250ms;
					}

					&:nth-child(3) {
						animation-delay: 500ms;
					}
				}
			}
		}

		.message-time-status {
			display: flex;
			align-items: center;
			line-height: 1;
			margin-top: 0.25rem;

			.sent-time {
				font-size: 10px;
				font-weight: 500;
				margin-left: .5rem;
			}

			.sent-status {
				font-size: 10px;
				width: 12px;
				height: 12px;
				margin-left: .25rem;
				text-align: center;
				border-radius: 50%;
				background-color: $white;
				color: $heading;
				box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

				i {
					line-height: 12px;
				}

				&.delivered {
					color: $primary;
				}

				&.seen {
					background-color: $success;
					color: $white;
				}
			}
		}

		.dropstart {
			line-height: 1;
			margin-top: -2px;
		}

		.btn-options {
			flex: 0 0 12px;
			width: 12px;
			padding: 0 !important;
			margin-right: .5rem;
			color: $text;
			line-height: 1;
			border: 0 !important;

			&.show {
				border: 0 !important;
			}

			&:focus {
				box-shadow: none;
				color: $primary;
			}

			&.dropdown-toggle::after,
			&.dropdown-toggle::before {
				display: none !important;
			}
		}

		.dropdown-menu {
			min-width: 6.5rem;
			padding: .5rem .75rem;
			border: 0;

			li {
				border-bottom: 1px solid rgba(0, 0, 0, 0.1);

				&:last-child {
					border-bottom: 0;
				}
			}

			a {
				color: $text;
				display: flex;
				align-items: center;
				padding: .25rem 0;
				font-size: 12px;

				i {
					margin-right: .25rem;
					font-size: 1rem;
					min-width: 1rem;

					&.bi-trash {
						font-size: 14px;
					}
				}

				&:hover,
				&:focus {
					color: $primary;
				}
			}
		}
	}

	&.outgoing {
		flex-direction: row-reverse;

		.user-avatar {
			margin-right: 0;
			margin-left: 0.5rem;
		}

		.user-message {
			text-align: right;
			max-width: 22rem;

			.message-content {
				flex-direction: row-reverse;
				margin-bottom: 0.25rem;

				&:last-child {
					margin-bottom: 0;
				}
			}

			.single-message {

				p {
					background-color: $primary;
					color: $white;
				}
			}

			.message-time-status {
				justify-content: flex-end;
			}
		}

		.dropdown-menu {
			background-color: $primary;

			a {
				color: $white;

				&:hover,
				&:focus {
					color: $warning;
				}
			}
		}
	}
}

.chat-footer {
	position: fixed;
	width: 100%;
	height: 62px;
	background-color: $white;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1000;

	form {
		position: relative;
		display: flex;
		align-items: center;
		width: 100% !important;

		.form-control {
			background-color: $gray;
			border-color: $gray;
			color: #073984;
			font-size: 14px;
		}
	}

	.btn-emoji,
	.btn-add-file {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 2rem;
		padding: 0 !important;
		color: $text;
		line-height: 1;
		border: 0 !important;
		font-size: 1.125rem;

		&:focus {
			box-shadow: none;
			color: $heading;
			border: 0 !important;
		}

		&.dropdown-toggle::after {
			display: none !important;
		}
	}

	.btn-submit {
		display: flex;
		align-items: center;
		justify-content: center;
		flex: 0 0 2rem;
		width: 2rem;
		height: 2rem;
		padding: 0 !important;
		background-color: $primary;
		color: $white;
		border-radius: 50%;
		border: 0 !important;
		font-size: 1.125rem;

		&:hover,
		&:focus {
			box-shadow: none;
			color: $white;
		}
	}

	.dropdown-menu {
		min-width: 7rem;
		padding: .5rem .75rem;
		border: 0;

		a {
			color: $text;
			display: flex;
			align-items: center;
			padding: .25rem 0;

			i {
				margin-right: .25rem;
				font-size: 18px;
				min-width: 18px;

				&.bi-trash {
					font-size: 16px;
				}
			}

			&:hover,
			&:focus {
				color: $primary;
			}
		}
	}
}

.video-calling-popup-wrap {
	position: fixed;
	background-color: rgba(6, 18, 56, 0.9);
	width: 100vw;
	height: 100vh;
	z-index: 10000000;
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition-duration: 500ms;
	visibility: hidden;

	&.screen-active {
		opacity: 1;
		visibility: visible;
	}

	.video-calling-popup-body {
		position: relative;
		max-width: 300px;
		left: 0;
		background: $white;
		padding: 2rem 1rem;
		width: 80%;
		text-align: center;
		border-radius: 2rem;
		background-size: cover;
		background-position: center center;

		&::after {
			border-radius: 2rem;
		}

		.user-thumbnail {
			position: relative;
			z-index: 1;

			img {
				width: 4rem;
				height: 4rem;
				margin: 0 auto;
				border-radius: 50%;
			}
		}

		.video-icon {
			font-size: 2.5rem;
		}
	}
}

.calling-popup-wrap {
	position: fixed;
	background-color: rgba(6, 18, 56, 0.9);
	width: 100vw;
	height: 100vh;
	z-index: 10000000;
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition-duration: 500ms;
	visibility: hidden;

	&.screen-active {
		opacity: 1;
		visibility: visible;
	}

	.calling-popup-body {
		position: relative;
		max-width: 300px;
		left: 0;
		background: $white;
		padding: 2rem 1rem;
		width: 80%;
		text-align: center;
		border-radius: 2rem;
		background-size: cover;
		background-position: center center;

		&::after {
			border-radius: 2rem;
		}

		.user-thumbnail {
			position: relative;
			z-index: 1;

			img {
				width: 4rem;
				height: 4rem;
				margin: 0 auto;
				border-radius: 50%;
			}
		}

		.call-icon {
			font-size: 2rem;
		}
	}
}

.video-call-screen {
	position: relative;
	z-index: 1;
	width: 100vw;
	height: 100vh;
	background-position: center center;
	background-size: cover;

	.call-back-button {
		a {
			position: absolute;
			width: 2rem;
			height: 2rem;
			border-radius: 50%;
			background-color: rgba(255, 255, 255, 0.5);
			top: 1.5rem;
			left: 1.5rem;
			z-index: 10;
			color: $text;
			font-size: 1.5rem;
			text-align: center;

			i {
				line-height: 1.5rem;
			}
		}
	}

	.call-btn-group {
		position: absolute;
		z-index: 10;
		bottom: 3rem;
		width: 210px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		left: 50%;
		margin-left: -105px;

		a>i {
			font-size: 1rem;
		}

		.btn-call-cancel {
			padding: 9px !important;
			width: 3rem;
			height: 3rem;
			border-radius: 50%;
			box-shadow: 0 0 0 .5rem rgba(225, 83, 97, .5);
		}
	}
}