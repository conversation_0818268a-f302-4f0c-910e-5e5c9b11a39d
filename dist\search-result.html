<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content header-style-five position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="pages.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Search Result</h6>
        </div>

        <!-- Navbar Toggler -->
        <div class="navbar--toggler" id="affanNavbarToggler" data-bs-toggle="offcanvas" data-bs-target="#affanOffcanvas"
          aria-controls="affanOffcanvas">
          <span class="d-block"></span>
          <span class="d-block"></span>
          <span class="d-block"></span>
        </div>
      </div>
    </div>
  </div>

  <!-- # Sidenav Left -->
  <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1"
    aria-labelledby="affanOffcanvsLabel">

    <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
      aria-label="Close"></button>

    <div class="offcanvas-body p-0">
      <div class="sidenav-wrapper">
        <!-- Sidenav Profile -->
        <div class="sidenav-profile bg-gradient">
          <div class="sidenav-style1"></div>

          <!-- User Thumbnail -->
          <div class="user-profile">
            <img src="img/bg-img/2.jpg" alt="">
          </div>

          <!-- User Info -->
          <div class="user-info">
            <h6 class="user-name mb-0">Affan Islam</h6>
            <span>CEO, Designing World</span>
          </div>
        </div>

        <!-- Sidenav Nav -->
        <ul class="sidenav-nav ps-0">
          <li>
            <a href="home.html"><i class="bi bi-house-door"></i> Home</a>
          </li>
          <li>
            <a href="elements.html"><i class="bi bi-folder2-open"></i> Elements
              <span class="badge bg-danger rounded-pill ms-2">220+</span>
            </a>
          </li>
          <li>
            <a href="pages.html"><i class="bi bi-collection"></i> Pages
              <span class="badge bg-success rounded-pill ms-2">100+</span>
            </a>
          </li>
          <li>
            <a href="#"><i class="bi bi-cart-check"></i> Shop</a>
            <ul>
              <li>
                <a href="shop-grid.html"> Shop Grid</a>
              </li>
              <li>
                <a href="shop-list.html"> Shop List</a>
              </li>
              <li>
                <a href="shop-details.html"> Shop Details</a>
              </li>
              <li>
                <a href="cart.html"> Cart</a>
              </li>
              <li>
                <a href="checkout.html"> Checkout</a>
              </li>
            </ul>
          </li>
          <li>
            <a href="settings.html"><i class="bi bi-gear"></i> Settings</a>
          </li>
          <li>
            <div class="night-mode-nav">
              <i class="bi bi-moon"></i> Night Mode
              <div class="form-check form-switch">
                <input class="form-check-input form-check-success" id="darkSwitch" type="checkbox">
              </div>
            </div>
          </li>
          <li>
            <a href="login.html"><i class="bi bi-box-arrow-right"></i> Logout</a>
          </li>
        </ul>

        <!-- Social Info -->
        <div class="social-info-wrap">
          <a href="#">
            <i class="bi bi-facebook"></i>
          </a>
          <a href="#">
            <i class="bi bi-twitter"></i>
          </a>
          <a href="#">
            <i class="bi bi-linkedin"></i>
          </a>
        </div>

        <!-- Copyright Info -->
        <div class="copyright-info">
          <p>
            <span id="copyrightYear"></span>
            &copy; Made by <a href="#">Designing World</a>
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <div class="container">
      <div class="card">
        <div class="card-body direction-rtl">

          <!-- Search Form Wrapper -->
          <div class="search-form-wrapper">
            <p class="mb-2 fz-12">397+ results found for Affan template</p>
            <!-- Search Form -->
            <form class="mb-3 pb-4 border-bottom" action="#">
              <div class="input-group">
                <input class="form-control form-control-clicked" type="search" value="Affan template">
                <button class="btn btn-dark" type="submit"><i class="bi bi-search fz-14"></i></button>
              </div>
            </form>
          </div>

          <!-- Single Search Result -->
          <div class="single-search-result mb-3 border-bottom pb-3">
            <h6 class="text-truncate mb-1">Affan - Multipurpose Ecommerce Mobile HTML Template</h6>
            <a class="text-truncate mb-2 d-block fz-12 text-decoration-underline"
              href="#">https://themeforest.net/item/suha-multipurpose-ecommerce-mobile-template/25294162</a>
            <p class="mb-0">Suha – Multipurpose Ecommerce Mobile HTML Template. A versatile mobile e-commerce shop
              template.</p>
          </div>

          <!-- Single Search Result -->
          <div class="single-search-result mb-3 border-bottom pb-3">
            <h6 class="text-truncate mb-1">WordPress Themes &amp; Website Templates from ThemeForest</h6>
            <a class="text-truncate mb-2 d-block fz-12 text-decoration-underline" href="#">https://themeforest.net/</a>
            <p class="mb-0">Discover 1000s of premium WordPress themes &amp; website templates, including multipurpose
              and responsive Bootstrap templates, email templates &amp; HTML</p>
          </div>

          <!-- Single Search Result -->
          <div class="single-search-result mb-3 border-bottom pb-3">
            <h6 class="text-truncate mb-1">Sign in to ThemeForest - Envato Account</h6>
            <a class="text-truncate mb-2 d-block fz-12 text-decoration-underline"
              href="https://account.envato.com/">https://account.envato.com/</a>
            <p class="mb-0">Your Envato Account gives you access not just to ThemeForest, but also to the other Envato
              Market sites listed below. They're stocked with everything from</p>
          </div>

          <!-- Single Search Result -->
          <div class="single-search-result mb-3 border-bottom pb-3">
            <h6 class="text-truncate mb-1">Envato ThemeForest Cyber Monday &amp; Black Friday Deals</h6>
            <a class="text-truncate mb-2 d-block fz-12 text-decoration-underline"
              href="#">https://envato.com/c/cybermonday/themes/</a>
            <p class="mb-0">Enjoy a week of savings across Envato with Cyber Monday &amp; Black Friday 2019 deals. Save
              on premium creative assets from Envato Market, Envato Elements ...</p>
          </div>

          <!-- Single Search Result -->
          <div class="single-search-result mb-3 border-bottom pb-3">
            <h6 class="text-truncate mb-1">Affan - Multipurpose Ecommerce Mobile HTML Template</h6>
            <a class="text-truncate mb-2 d-block fz-12 text-decoration-underline"
              href="#">https://themeforest.net/item/suha-multipurpose-ecommerce-mobile-template/25294162</a>
            <p class="mb-0">Suha – Multipurpose Ecommerce Mobile HTML Template. A versatile mobile e-commerce shop
              template.</p>
          </div>

          <!-- Single Search Result -->
          <div class="single-search-result mb-3 border-bottom pb-3">
            <h6 class="text-truncate mb-1">WordPress Themes &amp; Website Templates from ThemeForest</h6>
            <a class="text-truncate mb-2 d-block fz-12 text-decoration-underline"
              href="https://themeforest.net">https://themeforest.net/</a>
            <p class="mb-0">Discover 1000s of premium WordPress themes &amp; website templates, including multipurpose
              and responsive Bootstrap templates, email templates &amp; HTML</p>
          </div>

          <!-- Single Search Result -->
          <div class="single-search-result mb-3 border-bottom pb-3">
            <h6 class="text-truncate mb-1">Sign in to ThemeForest - Envato Account</h6>
            <a class="text-truncate mb-2 d-block fz-12 text-decoration-underline"
              href="https://account.envato.com/">https://account.envato.com/</a>
            <p class="mb-0">Your Envato Account gives you access not just to ThemeForest, but also to the other Envato
              Market sites listed below. They're stocked with everything from</p>
          </div>

          <!-- Single Search Result -->
          <div class="single-search-result mb-3 border-bottom pb-3">
            <h6 class="text-truncate mb-1">Envato ThemeForest Cyber Monday &amp; Black Friday Deals</h6>
            <a class="text-truncate mb-2 d-block fz-12 text-decoration-underline"
              href="#">https://envato.com/c/cybermonday/themes/</a>
            <p class="mb-0">Enjoy a week of savings across Envato with Cyber Monday &amp; Black Friday 2019 deals. Save
              on premium creative assets from Envato Market, Envato Elements ...</p>
          </div>

          <!-- Pagination -->
          <nav aria-label="Page navigation example">
            <ul class="pagination pagination-two justify-content-center">
              <li class="page-item">
                <a class="page-link" href="#" aria-label="Previous">
                  <i class="bi bi-chevron-left"></i>
                </a>
              </li>
              <li class="page-item active">
                <a class="page-link" href="#">1</a>
              </li>
              <li class="page-item">
                <a class="page-link" href="#">2</a>
              </li>
              <li class="page-item">
                <a class="page-link" href="#">3</a>
              </li>
              <li class="page-item">
                <a class="page-link" href="#">...</a>
              </li>
              <li class="page-item">
                <a class="page-link" href="#">9</a>
              </li>
              <li class="page-item">
                <a class="page-link" href="#" aria-label="Next">
                  <i class="bi bi-chevron-right"></i>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>