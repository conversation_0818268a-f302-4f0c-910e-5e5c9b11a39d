/* :: Animation CSS */

@keyframes spin {
    0% {
        transform: rotate(0);
    }

    100% {
        transform: rotate(359deg);
    }
}

@keyframes flashing {

    0%,
    90% {
        opacity: 1;
    }

    45% {
        opacity: 0;
    }
}

@keyframes circlebig {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes dotloader {

    50% {
        transform: scale(1.8);
    }
}

@keyframes circleloader2 {

    0% {
        width: 0%;
        height: 0%;
    }

    40% {
        width: 100%;
        height: 100%;
        opacity: 0.7;
    }

    100% {
        width: 240%;
        height: 240%;
        opacity: 0;
    }
}

@keyframes toast-animation {
    0% {
        width: 100%;
    }

    100% {
        width: 0%;
    }
}

@keyframes typingg {
    0% {
        top: 0;
    }

    50% {
        top: -6px;
    }

    100% {
        top: 0;
    }
}