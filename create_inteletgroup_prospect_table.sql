-- Tabla para prospectos de InteletGroup
CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    nombre_ejecutivo VARCHAR(255) NOT NULL,
    rut_cliente VARCHAR(20) NOT NULL UNIQUE,
    razon_social VARCHAR(255) NOT NULL,
    rubro VARCHAR(255) NOT NULL,
    direccion_comercial TEXT NOT NULL,
    telefono_celular VARCHAR(15) NOT NULL,
    email VARCHAR(255) NOT NULL,
    numero_pos VARCHAR(50),
    tipo_cuenta ENUM('Cuenta Vista', 'Cuenta Corriente') NOT NULL,
    numero_cuenta_bancaria VARCHAR(50) NOT NULL,
    dias_atencion VARCHAR(255) NOT NULL,
    horario_atencion VARCHAR(255) NOT NULL,
    contrata_boleta ENUM('Si', 'No') NOT NULL,
    competencia_actual ENUM('Transbank', 'Getnet', '<PERSON>mpra Aquí (Bco Estado)', 'Klap', 'SumUp', 'Tuu', 'Ya Ganaste', 'Mercado Pago') NOT NULL,
    documentos_adjuntos TEXT,
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    estado ENUM('Activo', 'Inactivo') DEFAULT 'Activo',
    FOREIGN KEY (usuario_id) REFERENCES tb_experian_usuarios(id) ON DELETE CASCADE,
    INDEX idx_rut_cliente (rut_cliente),
    INDEX idx_usuario_id (usuario_id),
    INDEX idx_fecha_registro (fecha_registro)
);

-- Tabla para bitácora de prospectos InteletGroup
CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospecto_bitacora (
    id INT AUTO_INCREMENT PRIMARY KEY,
    prospecto_id INT NOT NULL,
    usuario_id INT NOT NULL,
    accion ENUM('Crear', 'Editar', 'Subir Documento', 'Eliminar Documento') NOT NULL,
    descripcion TEXT NOT NULL,
    fecha_accion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (prospecto_id) REFERENCES tb_inteletgroup_prospectos(id) ON DELETE CASCADE,
    FOREIGN KEY (usuario_id) REFERENCES tb_experian_usuarios(id) ON DELETE CASCADE,
    INDEX idx_prospecto_id (prospecto_id),
    INDEX idx_usuario_id (usuario_id),
    INDEX idx_fecha_accion (fecha_accion)
);

-- Tabla para documentos de prospectos InteletGroup
CREATE TABLE IF NOT EXISTS tb_inteletgroup_documentos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    prospecto_id INT NOT NULL,
    usuario_id INT NOT NULL,
    rut_cliente VARCHAR(20) NOT NULL,
    nombre_archivo VARCHAR(255) NOT NULL,
    nombre_original VARCHAR(255) NOT NULL,
    tipo_archivo VARCHAR(100) NOT NULL,
    tamaño_archivo INT NOT NULL,
    ruta_archivo VARCHAR(500) NOT NULL,
    fecha_subida TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    estado ENUM('Activo', 'Eliminado') DEFAULT 'Activo',
    FOREIGN KEY (prospecto_id) REFERENCES tb_inteletgroup_prospectos(id) ON DELETE CASCADE,
    FOREIGN KEY (usuario_id) REFERENCES tb_experian_usuarios(id) ON DELETE CASCADE,
    INDEX idx_prospecto_id (prospecto_id),
    INDEX idx_rut_cliente (rut_cliente),
    INDEX idx_usuario_id (usuario_id),
    INDEX idx_fecha_subida (fecha_subida)
);
