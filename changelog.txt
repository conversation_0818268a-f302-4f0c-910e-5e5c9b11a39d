================
:: Version 1.6.0
================
Updated: Bootstrap v5.2.0 > Bootstrap v5.2.3
Updated: Bootstrap Icons v1.9.1 > Bootstrap Icons v1.10.2
Updated: Apexcharts v3.35.3 > Apexcharts v3.36.3
Updated: baguettebox.js has been dropped. Instead, we used 'venobox' js plugin.

================
:: Version 1.5.0
================
Updated: Design Quality
Updated: PUG has been dropped. Now, we used plain HTML5 code in the template. So that everyone can easily edit or customize.
Updated: Bootstrap v5.1.3 > Bootstrap v5.2.0
Updated: Bootstrap Icons v1.8.1 > Bootstrap Icons v1.9.1
Updated: Apexcharts v3.33.2 > Apexcharts v3.35.3

=================================
:: Version 1.4.1 - March 26, 2022
=================================
Updated: Apexcharts v3.19.3 > Apexcharts v3.33.2
Updated: Bootstrap Icons v1.5.0 > Bootstrap Icons v1.8.1
Updated: Imagesloaded v4.1.4 > Imagesloaded v5.0.0
Updated: Tiny Slider v2.9.3 > Tiny Slider v2.9.4

===============================
:: Version 1.4.0 - Oct 12, 2021
===============================
Updated: Bootstrap v5.1.0 > Bootstrap v5.1.3
Improved: Code Quality
Added: Isotope layout in the Image Gallery page (Premium Plugin)

==============================
:: Version 1.3.0 - Sep 3, 2021
==============================
New Added: The whole code is written with Vanilla JavaScript. From now, there is no need for jQuery dependency.
New Added: Filter Menu on Elements Page
New Added: Offcanvas Navbar
New Added: Horizontal Scroll Menu
New Added: Notification Button
New Added: Login Page via Social Media
New Added: Large Checkbox
New Added: Toggle Password Visibility
New Added: Comparison Table
Updated: Bootstrap v5.0.1 > Bootstrap v5.1.0
Improved: Design & Code Quality

===============================
:: Version 1.2.1 - Jun 28, 2021
===============================
The documentation has been completely updated with enriched content.

===============================
:: Version 1.2.0 - Jun 21, 2021
===============================
Improved: Design Quality
Improved: Code Quality
Updated: Bootstrap v5.0.0-beta2 > Bootstrap v5.0.1
Updated: Owl Carousel 2.2.1 > Owl Carousel 2.3.4
Updated: Font Family - Public Sans to Poppins
Removed: Clipboard js Plugin
New Added: The static folder in the package (this folder contains all the static files).

=================================
:: Version 1.1.0 - March 13, 2021
=================================
New Added: 3 chat page & 1 tab page
Improved: Dark & RTL mode
Improved: Design quality
Improved: Code quality
Updated: Bootstrap v5.0.0-beta1 > Bootstrap v5.0.0-beta2
Updated: Commissioner Google Fonts > Public Sans Google Fonts

===============================
:: Version 1.0.0 - Dec 14, 2020
===============================
Initial Realease