-- Security Infrastructure Tables
-- Create tables for enhanced security features

-- Table for tracking login attempts (rate limiting)
CREATE TABLE IF NOT EXISTS tb_login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    success TINYINT(1) DEFAULT 0,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_agent TEXT,
    INDEX idx_email_time (email, attempt_time),
    INDEX idx_ip_time (ip_address, attempt_time),
    INDEX idx_success (success)
);

-- Table for session management
CREATE TABLE IF NOT EXISTS tb_user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_id VARCHAR(128) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES tb_experian_usuarios(id) ON DELETE CASCADE,
    UNIQUE KEY unique_session (session_id),
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_last_activity (last_activity)
);

-- Table for security events logging
CREATE TABLE IF NOT EXISTS tb_security_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES tb_experian_usuarios(id) ON DELETE SET NULL,
    INDEX idx_event_type (event_type),
    INDEX idx_severity (severity),
    INDEX idx_created_at (created_at),
    INDEX idx_user_events (user_id, created_at)
);

-- Table for password reset tokens
CREATE TABLE IF NOT EXISTS tb_password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES tb_experian_usuarios(id) ON DELETE CASCADE,
    UNIQUE KEY unique_token (token),
    INDEX idx_user_token (user_id, token),
    INDEX idx_expires (expires_at)
);

-- Create backup of current usuarios table before password migration
CREATE TABLE IF NOT EXISTS tb_experian_usuarios_backup AS 
SELECT * FROM tb_experian_usuarios;

-- Add indexes for better performance on usuarios table
ALTER TABLE tb_experian_usuarios 
ADD INDEX idx_correo (correo),
ADD INDEX idx_rol (rol),
ADD INDEX idx_proyecto (proyecto),
ADD INDEX idx_rut_ejecutivo (rut_ejecutivo);

-- Clean up old login attempts (older than 30 days)
CREATE EVENT IF NOT EXISTS cleanup_login_attempts
ON SCHEDULE EVERY 1 DAY
DO
  DELETE FROM tb_login_attempts 
  WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Clean up old security events (older than 90 days)
CREATE EVENT IF NOT EXISTS cleanup_security_events
ON SCHEDULE EVERY 1 DAY
DO
  DELETE FROM tb_security_events 
  WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- Clean up expired password reset tokens
CREATE EVENT IF NOT EXISTS cleanup_password_tokens
ON SCHEDULE EVERY 1 HOUR
DO
  DELETE FROM tb_password_reset_tokens 
  WHERE expires_at < NOW() OR used = 1;
