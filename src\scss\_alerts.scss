/* :: Alert */

.alert {
    padding: .625rem .75rem;
    font-size: 14px;
    border-radius: .25rem;
}

.alert-dismissible .btn-close {
    font-size: 10px;
}

.custom-alert-1 {
    display: flex;
    align-items: center;
    padding: .5rem 1rem;
    padding-left: 27px;
    font-size: 14px;
    border-color: $border;
    border-radius: .25rem;

    &::after {
        content: "";
        position: absolute;
        width: 4px;
        height: calc(100% - 30px);
        top: 15px;
        left: 12px;
        z-index: 1;
        border-radius: 1rem;
    }

    &.alert-dismissible .close {
        position: relative;
        padding: .25rem;
        margin-left: .75rem;
        margin-left: auto;
    }

    i {
        font-size: 1.25rem;
        margin-right: .5rem;
    }

    &.alert-primary {
        color: $primary;
        background-color: transparent;

        &::after {
            background-color: $primary;
        }
    }

    &.alert-secondary {
        color: $secondary;
        background-color: transparent;

        &::after {
            background-color: $secondary;
        }
    }

    &.alert-success {
        color: $success;
        background-color: transparent;

        &::after {
            background-color: $success;
        }
    }

    &.alert-danger {
        color: $danger;
        background-color: transparent;

        &::after {
            background-color: $danger;
        }
    }

    &.alert-warning {
        color: $warning;
        background-color: transparent;

        &::after {
            background-color: $warning;
        }
    }

    &.alert-info {
        color: $info;
        background-color: transparent;

        &::after {
            background-color: $info;
        }
    }

    &.alert-light {
        color: $text;
        background-color: transparent;

        &::after {
            background-color: $gray;
        }
    }

    &.alert-dark {
        color: $heading;
        background-color: transparent;

        &::after {
            background-color: $dark;
        }
    }
}

.custom-alert-2 {
    display: flex;
    align-items: center;
    padding: .5rem 1rem;
    font-size: 14px;
    color: $white;
    border-color: transparent;

    &.alert-dismissible .close {
        position: relative;
        padding: .25rem;
        margin-left: .75rem;
        margin-left: auto;
    }

    i {
        font-size: 1.25rem;
        margin-right: .5rem;
    }

    &.alert-primary {
        background-color: $primary;
    }

    &.alert-secondary {
        background-color: $secondary;
    }

    &.alert-success {
        background-color: $success;
    }

    &.alert-danger {
        background-color: $danger;
    }

    &.alert-warning {
        background-color: $warning;
    }

    &.alert-info {
        background-color: $info;
    }

    &.alert-light {
        color: $text;
        background-color: $white;
    }

    &.alert-dark {
        background-color: $dark;
    }
}

.custom-alert-3 {
    display: flex;
    padding: 1rem;
    font-size: 13px;

    &.alert-dismissible .close {
        position: relative;
        padding: 0.25rem;
        margin-left: 0.75rem;
        margin-left: auto;
    }

    i {
        font-size: 1.5rem;
        margin-right: .75rem;
        margin-top: .25rem;
        line-height: 1;
    }

    .alert-text {
        h6 {
            margin-bottom: 4px;
            color: inherit;
        }

        span {
            display: block;
            font-size: 12px;
            color: $text;
        }
    }
}