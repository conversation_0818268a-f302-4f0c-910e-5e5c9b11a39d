/* :: Accordion */

.accordion-item {
    border: 0;
}

.accordion-button {
    padding: .75rem 1rem;
    font-size: 14px;
    color: $heading;
    border-color: $border;
    font-weight: 500;
}

.accordion-collapse {
    border-color: $border;
}

.accordion-style-one {
    .accordion-item {
        margin-bottom: 0;
        border-bottom: 0;

        h6 {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0;
            width: 100%;
            padding: 1rem;
            border-bottom: 1px solid $border;
            color: $primary;
            cursor: pointer;

            &.collapsed {
                color: $heading;
            }

            i {
                transition-duration: 400ms;
                margin-right: 0;
                transform: rotate(180deg);
            }

            &.collapsed i {
                transform: rotate(0deg);
            }
        }

        .accordion-collapse {
            border: 0 !important;
        }

        .accordion-body {
            border-bottom: 1px solid $border;
        }

        &:last-child {
            h6 {
                border-bottom: 1px solid $border;

                &.collapsed {
                    border-bottom: 0;
                }
            }

            .accordion-body {
                border-bottom: 0;
            }
        }
    }
}

.accordion-style-two {
    .accordion-item {
        margin-bottom: 0;
        border-bottom: 0;

        h6 {
            transition-duration: 400ms;
            display: flex;
            align-items: center;
            margin-bottom: 0;
            width: 100%;
            padding: .75rem 1rem;
            border-bottom: 1px solid $border;
            color: $danger;
            cursor: pointer;

            &.collapsed {
                color: $heading;
            }

            i {
                transition-duration: 400ms;
                margin-right: 0.5rem;
                width: 1.5rem;
                height: 1.5rem;
                background-color: $danger;
                color: $white;
                font-size: 14px;
                text-align: center;
                line-height: 1.5rem;
                border-radius: 50%;
                transform: rotate(45deg);
            }

            &.collapsed i {
                background-color: $heading;
                transform: rotate(0deg);
            }
        }

        .accordion-collapse {
            border: 0 !important;
        }

        .accordion-body {
            border-bottom: 1px solid $border;
        }

        &:last-child {
            h6 {
                border-bottom: 1px solid $border;

                &.collapsed {
                    border-bottom: 0;
                }
            }

            .accordion-body {
                border-bottom: 0;
            }
        }
    }
}

.accordion-style-three {
    .accordion-item {
        margin-bottom: .25rem;
        border: 0;

        &:last-child {
            margin-bottom: 0;
        }

        h6 {
            background-color: $white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0;
            width: 100%;
            padding: .375rem 1rem;
            border-bottom: 1px solid $border;
            color: $primary;
            cursor: pointer;
            font-size: 14px;

            &.collapsed {
                color: $heading;
            }

            i {
                font-size: 1rem;
                transition-duration: 400ms;
                margin-right: 0;
                transform: rotate(180deg);
            }

            &.collapsed i {
                transform: rotate(0deg);
            }
        }

        .accordion-collapse {
            border: 0 !important;
        }

        .accordion-body {
            padding: 0.5rem;
        }

        &:last-child {
            h6 {
                border-bottom: 0;
            }
        }
    }
}

.accordion-style-four {
    .accordion-item {
        margin-bottom: 0;

        h6 {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0;
            width: 100%;
            padding: .5rem 0;
            color: $warning;
            cursor: pointer;

            &.collapsed {
                color: $white;
            }

            i {
                font-size: 18px;
                transition-duration: 400ms;
                margin-right: 0;
                transform: rotate(180deg);
            }

            &.collapsed i {
                transform: rotate(0deg);
            }
        }

        .accordion-collapse {
            border: 0 !important;
        }

        .accordion-body {
            padding: 0;
            padding-bottom: 0.5rem;

            p {
                color: $text-gray;
            }
        }

        &:last-child {
            h6 {
                border-bottom: 0;
            }
        }
    }
}

.accordion-bg-primary {
    background-color: rgba(13, 110, 253, .1);
    border-left: 4px solid rgba(13, 110, 253, .75);
}

.accordion-bg-danger {
    background-color: rgba(220, 53, 69, .1);
    border-left: 4px solid rgba(220, 53, 69, .75);
}

.accordion-bg-warning {
    background-color: rgba(255, 193, 7, .1);
    border-left: 4px solid rgba(255, 193, 7, .75);
}

.accordion-bg-info {
    background-color: rgba(13, 202, 240, .1);
    border-left: 4px solid rgba(13, 202, 240, .75);
}

.accordion-bg-success {
    background-color: rgba(25, 135, 84, .1);
    border-left: 4px solid rgba(25, 135, 84, .75);
}

.accordion-style-five {
    .accordion-item {
        border-radius: .25rem;
        padding: .75rem;
        margin-bottom: 0.5rem;

        &:last-child {
            margin-bottom: 0;
        }

        h6 {
            cursor: pointer;
            margin-bottom: 0;
            color: $heading;

            &.collapsed {
                color: $heading;
            }

            i {
                transition-duration: 400ms;
                margin-right: 0.5rem;
                font-size: 1.25rem;
                transform: rotate(45deg);
            }

            &.collapsed i {
                transform: rotate(0deg);
            }
        }
    }

    .accordion-collapse {
        border-color: transparent;
        padding-left: 1.75rem;
    }
}

.accordion-style-six {
    .accordion-item {
        position: relative;
        z-index: 1;
        background-position: center center;
        background-repeat: no-repeat;
        border-radius: .375rem;
        padding: 1rem;
        margin-bottom: 0.5rem;

        &::after {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: -10;
            content: "";
            background-color: rgba(13, 13, 53, .75);
            border-radius: .375rem;
        }

        &:last-child {
            margin-bottom: 0;
        }

        h6 {
            cursor: pointer;
            margin-bottom: 0;
            color: $white;

            &.collapsed {
                color: $white;
            }
        }
    }
}