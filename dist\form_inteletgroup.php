<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Inicializar variables importantes
$es_admin = false; // Variable que indica si el usuario es administrador

// Aplicar headers anti-caché
no_cache_headers();

// Verificación mejorada de autenticación
function checkAuth() {
    if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
        // Registrar el intento fallido
        error_log("[" . date('Y-m-d H:i:s') . "] Error de autenticación - Usuario no identificado - IP: " . $_SERVER['REMOTE_ADDR']);

        // Redireccionar a login con mensaje de error
        $errorMsg = urlencode("Usuario no autenticado. Inicie sesión para continuar.");
        header('Location: ' . version_url('login.php?error=' . $errorMsg));
        exit;
    }

    // Renovar la sesión para evitar su caducidad prematura
    $_SESSION['last_activity'] = time();
    return true;
}

// Verificar si el usuario está autenticado
checkAuth();

// Verificar que el usuario pertenezca al proyecto InteletGroup
if (!isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    error_log("[" . date('Y-m-d H:i:s') . "] Acceso denegado - Usuario no pertenece al proyecto InteletGroup - Usuario: " . $_SESSION['usuario']);
    $errorMsg = urlencode("Acceso denegado. No tiene permisos para acceder a esta sección.");
    header('Location: ' . version_url('login.php?error=' . $errorMsg));
    exit;
}

// Incluir el archivo de conexión
require_once 'con_db.php';

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = $_SESSION['nombre_usuario'] ?? 'Usuario';
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InteletGroup - Panel de Control</title>
    <?php echo no_cache_meta(); ?>
    <link rel="stylesheet" href="<?php echo version_url('css/form_experian.css'); ?>">
    <!-- Estilos para tablas ordenables -->
    <link rel="stylesheet" href="<?php echo version_url('css/table-sort.css'); ?>">
    <!-- Estilos para formulario de prospectos InteletGroup -->
    <link rel="stylesheet" href="<?php echo version_url('css/inteletgroup-prospect.css'); ?>">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <style>
        /* Estilos para el nuevo header corporativo */
        .corporate-header {
            color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* Barra superior con fecha/hora e información rápida */
        .top-bar {
            background-color: #2c3e50;
            color: rgba(255,255,255,0.8);
            font-size: 0.85rem;
        }
        
        .top-bar a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: color 0.2s;
        }
        
        .top-bar a:hover {
            color: #fff;
        }
        
        /* Barra principal con logo y navegación */
        .main-bar {
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
        }
        
        /* Menú de navegación */
        .main-navigation .nav-link {
            color: rgba(255,255,255,0.8);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s;
        }
        
        .main-navigation .nav-link:hover,
        .main-navigation .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        /* Perfil de usuario */
        .user-profile .dropdown-toggle {
            color: white;
            text-decoration: none;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background-color: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .user-name {
            font-weight: 600;
            line-height: 1.2;
        }
        
        .user-role {
            opacity: 0.8;
        }
        
        /* Barra de título de página y breadcrumbs */
        .page-title-bar {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            color: #495057;
        }
        
        .page-title {
            color: #2c3e50;
            font-weight: 600;
        }
        
        .breadcrumb {
            font-size: 0.85rem;
            padding: 0;
            margin: 0;
            background: transparent;
        }
        
        /* Estilos adicionales */
        .welcome-card {
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 15px;
        }
        
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        
        .coming-soon {
            opacity: 0.7;
            position: relative;
        }
        
        .coming-soon::after {
            content: "Próximamente";
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ffc107;
            color: #000;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
        }
        
        /* Estilos para notificaciones y botones */
        #inteletgroup-notifications-container {
            z-index: 9999;
        }
        
        #inteletgroup-notifications-container .alert {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-left: 4px solid;
        }
        
        #inteletgroup-notifications-container .alert-success {
            border-left-color: #28a745;
        }
        
        #inteletgroup-notifications-container .alert-danger {
            border-left-color: #dc3545;
        }
        
        #inteletgroup-notifications-container .alert-info {
            border-left-color: #17a2b8;
        }
        
        /* Animación para los botones */
        #saveInteletGroupProspectBtn {
            transition: all 0.3s ease;
        }
        
        #saveInteletGroupProspectBtn:active {
            transform: scale(0.97);
        }
        
        /* Estilos para mensajes en el modal */
        #inteletgroup-message-container .alert {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            animation: fadeInDown 0.5s;
        }
        
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Header Corporativo -->
    <header class="corporate-header">
        <!-- Barra superior con información de usuario y acciones rápidas -->
        <div class="top-bar">
            <div class="container">
                <div class="row py-2">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-calendar-check me-2"></i>
                            <span class="current-date"><?php echo date('d M, Y'); ?></span>
                            <span class="mx-3">|</span>
                            <i class="bi bi-clock me-2"></i>
                            <span id="current-time">...</span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="quick-actions">
                            <a href="#" class="me-3"><i class="bi bi-question-circle"></i> Ayuda</a>
                            <a href="#" class="me-3"><i class="bi bi-gear"></i> Configuración</a>
                            <a href="logout.php"><i class="bi bi-box-arrow-right"></i> Cerrar sesión</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Barra principal del header con logo y navegación -->
        <div class="main-bar">
            <div class="container">
                <div class="row py-3 align-items-center">
                    <div class="col-lg-3 col-md-4">
                        <div class="d-flex align-items-center">
                            <img src="img/logo-inteletgroup.png" alt="Logo InteletGroup" height="50" class="me-3" 
                                 onerror="this.style.display='none'">
                            <div>
                                <h3 class="mb-0 fw-bold">InteletGroup</h3>
                                <div class="badge bg-primary">Panel Corporativo</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-4 d-none d-md-block">
                        <nav class="main-navigation">
                            <ul class="nav">
                                <li class="nav-item"><a href="#" class="nav-link active"><i class="bi bi-house-door me-1"></i> Inicio</a></li>
                                <li class="nav-item"><a href="#" class="nav-link"><i class="bi bi-people me-1"></i> Clientes</a></li>
                                <li class="nav-item"><a href="#" class="nav-link"><i class="bi bi-graph-up me-1"></i> Reportes</a></li>
                                <li class="nav-item"><a href="#" class="nav-link"><i class="bi bi-tools me-1"></i> Herramientas</a></li>
                            </ul>
                        </nav>
                    </div>
                    <div class="col-lg-3 col-md-4">
                        <div class="user-profile">
                            <div class="dropdown text-end">
                                <a href="#" class="d-block link-light text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2">
                                            <span class="user-initial"><?php echo substr($nombre_usuario, 0, 1); ?></span>
                                        </div>
                                        <div class="user-info text-start">
                                            <div class="user-name"><?php echo htmlspecialchars($nombre_usuario); ?></div>
                                            <div class="user-role small"><?php echo htmlspecialchars($proyecto); ?></div>
                                        </div>
                                    </div>
                                </a>
                                <ul class="dropdown-menu text-small" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>Mi Perfil</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Configuración</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="bi bi-bell me-2"></i>Notificaciones</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right me-2"></i>Cerrar sesión</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Barra de breadcrumbs y título de la página -->
        <div class="page-title-bar">
            <div class="container">
                <div class="row py-2">
                    <div class="col-md-8">
                        <h4 class="page-title mb-0">Panel de Control</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="#">Inicio</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Panel</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-light btn-sm" onclick="window.location.reload()">
                            <i class="bi bi-arrow-clockwise me-1"></i> Actualizar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Welcome Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card welcome-card">
                    <div class="card-body text-center py-5">
                        <h2 class="card-title mb-3">
                            <i class="bi bi-rocket-takeoff me-2"></i>
                            Bienvenido a InteletGroup
                        </h2>
                        <p class="card-text lead">
                            Tu plataforma integral para la gestión de proyectos y clientes
                        </p>
                        <p class="card-text">
                            Accede a todas las herramientas que necesitas para impulsar tu negocio
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Grid -->
        <div class="row g-4">
            <!-- Registro de Prospectos -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100" style="border: 2px solid #28a745;">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-person-plus-fill text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title text-success">Registro de Prospectos</h5>
                        <p class="card-text">
                            Registra nuevos prospectos comerciales con toda su información y documentación.
                        </p>
                        <button class="btn btn-success" onclick="abrirModalInteletGroupProspecto()">
                            <i class="bi bi-plus-circle me-1"></i> Nuevo Prospecto
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gestión de Documentos -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100" style="border: 2px solid #17a2b8;">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-file-earmark-arrow-up text-info" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title text-info">Gestión de Documentos</h5>
                        <p class="card-text">
                            Sube y gestiona documentos de prospectos existentes por RUT.
                        </p>
                        <a href="inteletgroup_documentos.php" class="btn btn-info">
                            <i class="bi bi-files me-1"></i> Gestionar Docs
                        </a>
                    </div>
                </div>
            </div>

            <!-- Reportes -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100 coming-soon">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-graph-up text-info" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">Reportes y Analytics</h5>
                        <p class="card-text">
                            Obtén insights valiosos con reportes detallados y análisis de datos.
                        </p>
                        <button class="btn btn-info" disabled>
                            <i class="bi bi-arrow-right me-1"></i> Acceder
                        </button>
                    </div>
                </div>
            </div>

            <!-- Configuración -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100 coming-soon">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-gear-fill text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">Configuración</h5>
                        <p class="card-text">
                            Personaliza tu experiencia y configura las preferencias del sistema.
                        </p>
                        <button class="btn btn-warning" disabled>
                            <i class="bi bi-arrow-right me-1"></i> Acceder
                        </button>
                    </div>
                </div>
            </div>

            <!-- Soporte -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-headset text-secondary" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">Soporte Técnico</h5>
                        <p class="card-text">
                            ¿Necesitas ayuda? Contacta con nuestro equipo de soporte técnico.
                        </p>
                        <a href="mailto:<EMAIL>" class="btn btn-secondary">
                            <i class="bi bi-envelope me-1"></i> Contactar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Acceso Temporal a Experian -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card h-100" style="border: 2px solid #dc3545;">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-link-45deg text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title text-danger">Acceso Temporal</h5>
                        <p class="card-text">
                            Acceso temporal al sistema Experian mientras desarrollamos las nuevas funcionalidades.
                        </p>
                        <a href="form_experian2.php" class="btn btn-danger">
                            <i class="bi bi-arrow-right me-1"></i> Ir a Experian
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Information Alert -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-info" role="alert">
                    <h4 class="alert-heading">
                        <i class="bi bi-info-circle me-2"></i>
                        Información Importante
                    </h4>
                    <p>
                        Bienvenido al nuevo panel de InteletGroup. Estamos trabajando en el desarrollo de nuevas 
                        funcionalidades específicas para tu proyecto. 
                    </p>
                    <hr>
                    <p class="mb-0">
                        <strong>Fecha de lanzamiento estimada:</strong> Hoy<br>
                        <strong>Contacto para consultas:</strong> <EMAIL>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4 bg-light">
        <div class="container text-center">
            <p class="mb-0 text-muted">
                &copy; <?php echo date('Y'); ?> Gestar servicios. Todos los derechos reservados.
            </p>
        </div>
    </footer>

    <!-- Modal del formulario de prospectos -->
    <?php include 'inteletgroup_prospect_modal.html'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript del formulario de prospectos -->
    <script src="<?php echo version_url('js/inteletgroup-prospect.js'); ?>"></script>

    <script>
        // Variables globales para el formulario
        window.currentUserName = '<?php echo addslashes($nombre_usuario); ?>';
        window.currentUserId = <?php echo $usuario_id; ?>;

        // Log del proyecto para debugging
        console.log('InteletGroup Panel - Usuario:', '<?php echo htmlspecialchars($nombre_usuario); ?>');
        console.log('Proyecto:', '<?php echo htmlspecialchars($proyecto); ?>');
        console.log('Usuario ID:', <?php echo $usuario_id; ?>);

        // Mensaje de bienvenida y inicialización
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Panel de InteletGroup cargado correctamente');
            
            // Inicializar reloj en tiempo real
            initializeClock();
        });
        
        // Función para mostrar reloj en tiempo real
        function initializeClock() {
            const clockElement = document.getElementById('current-time');
            if (!clockElement) return;
            
            function updateClock() {
                const now = new Date();
                let hours = now.getHours();
                const minutes = now.getMinutes().toString().padStart(2, '0');
                const seconds = now.getSeconds().toString().padStart(2, '0');
                const ampm = hours >= 12 ? 'PM' : 'AM';
                
                // Convertir a formato 12 horas
                hours = hours % 12;
                hours = hours ? hours : 12; // la hora '0' debe ser '12'
                
                const timeString = `${hours}:${minutes}:${seconds} ${ampm}`;
                clockElement.textContent = timeString;
            }
            
            // Actualizar cada segundo
            updateClock();
            setInterval(updateClock, 1000);
        }
    </script>
</body>
</html>
