<?php
/**
 * Utilidades para manejo de cache y versiones de recursos
 */

/**
 * Aplica los headers necesarios para evitar el cacheo del contenido
 */
function no_cache_headers() {
    // Headers para evitar cacheo en navegadores y proxies
    header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
    header("Cache-Control: post-check=0, pre-check=0", false);
    header("Pragma: no-cache");
    header("Expires: Mon, 26 Jul 1997 05:00:00 GMT"); // Fecha en el pasado
}

/**
 * Genera meta tags para evitar el cacheo
 */
function no_cache_meta() {
    return '
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    ';
}

/**
 * Añade un parámetro de versión a las URLs para forzar la recarga
 * de recursos estáticos cuando cambian
 */
function version_url($path) {
    $fullPath = __DIR__ . '/' . $path;
    $version = file_exists($fullPath) ? filemtime($fullPath) : '';
    return $path . '?v=' . $version;
}

/**
 * Establece un valor para la cookie de sesión con configuración segura
 */
function set_secure_session_cookie() {
    // Obtener el nombre actual de la sesión
    $session_name = session_name();
    
    // Configurar la cookie para ser más segura
    $cookie_params = session_get_cookie_params();
    
    setcookie(
        $session_name,
        session_id(),
        [
            'expires' => time() + $cookie_params['lifetime'],
            'path' => $cookie_params['path'],
            'domain' => $cookie_params['domain'],
            'secure' => true,                  // Solo sobre HTTPS
            'httponly' => true,                // No accesible via JavaScript
            'samesite' => 'Lax'                // Restringir cookies cross-site
        ]
    );
}

/**
 * Regenera el ID de sesión para prevenir ataques de fijación de sesión
 */
function regenerate_session() {
    // Regenerar ID de sesión y mantener datos
    if (!headers_sent()) {
        session_regenerate_id(true);
        set_secure_session_cookie();
    }
}

/**
 * Registra información de depuración en un archivo de log
 */
function debug_log($message, $data = []) {
    $log_file = __DIR__ . '/logs/debug.log';
    $log_dir = dirname($log_file);
    
    // Crear directorio de logs si no existe
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    // Formatear mensaje
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $session_id = session_id() ?: 'No session';
    
    $log_entry = "[$timestamp] [$ip] [$session_id] $message";
    
    // Añadir datos extra si existen
    if (!empty($data)) {
        $log_entry .= " | Data: " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    // Añadir info del user agent
    $log_entry .= " | UA: $user_agent";
    $log_entry .= PHP_EOL;
    
    // Escribir al archivo
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

function dev_no_cache_headers() {
    if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
        header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
        header("Cache-Control: post-check=0, pre-check=0", false);
        header("Pragma: no-cache");
        header('Expires: Sun, 02 Jan 1990 00:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    }
}
