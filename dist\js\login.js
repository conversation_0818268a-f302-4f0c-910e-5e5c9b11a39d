const form = document.querySelector('#login-form');
const errorMessage = document.querySelector('#error-message');

form.addEventListener('submit', (event) => {
  event.preventDefault();

  const username = form.username.value;
  const password = form.password.value;

  // Connect to the MySQL database
  const connection = mysql.createConnection({
    host: 'localhost',
    user: 'telqwayc_ncornejo',
    password: 'N1c0l7as17',
    database: 'database_name',
  });

  connection.connect((err) => {
    if (err) {
      console.error('Error connecting to MySQL database:', err);
      return;
    }
    console.log('Connected to MySQL database');

    // Query the database to check the login credentials
    const sql = `SELECT * FROM users WHERE username='${username}' AND password='${password}'`;
    connection.query(sql, (err, result) => {
      if (err) {
        console.error('Error querying MySQL database:', err);
        return;
      }

      // If the login credentials are incorrect, display an error message
      if (result.length === 0) {
        errorMessage.textContent = 'Incorrect login credentials!';
        errorMessage.style.display = 'block';
        return;
      }

      // If the login credentials are correct, submit the form
      form.submit();
    });
  });
});