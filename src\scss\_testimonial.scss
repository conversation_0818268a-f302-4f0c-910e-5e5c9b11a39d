/* :: Testimonial */

.testimonial-slide-one-wrapper {
    position: relative;
    z-index: 1;

    .tns-nav {
        position: absolute;
        z-index: 10;
        right: 1.5rem;
        bottom: 1.625rem;
        display: flex;
        align-items: center;
        justify-content: center;

        button {
            border: 0;
            width: .5rem;
            height: .5rem;
            background-color: $text-gray;
            margin: 0 .25rem;
            border-radius: 50%;

            &.tns-nav-active {
                background-color: $heading;
            }
        }
    }
}

.testimonial-slide-two-wrapper {
    position: relative;
    z-index: 1;

    .tns-controls button[data-controls="prev"],
    .tns-controls button[data-controls="next"] {
        border: 0;
        position: absolute;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        top: 50%;
        left: 5%;
        z-index: 10;
        background-color: $white;
        text-align: center;
        color: $heading;
        margin-top: -1rem;
        box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
        transition-duration: 500ms;

        i {
            line-height: 34px;
        }

        &:hover,
        &:focus {
            background-color: $warning;
        }
    }

    .tns-controls button[data-controls="next"] {
        left: auto;
        right: 5%;
    }
}

.testimonial-slide-three-wrapper {
    position: relative;
    z-index: 1;

    .tns-nav {
        display: flex;
        align-items: center;
        margin-top: 1rem;

        button {
            border: 0;
            width: 1rem;
            height: 0.25rem;
            background-color: $text-gray;
            margin: 0 .25rem;
            border-radius: .25rem;

            &.tns-nav-active {
                background-color: $heading;
            }
        }
    }
}

.testimonial-style1 {
    .single-testimonial-slide {
        position: relative;
        z-index: 1;
        display: flex;
        align-items: flex-start;

        .image-wrapper {
            margin-right: 1.25rem;
            flex: 0 0 80px;
            width: 80px;
            max-width: 80px;
            border-radius: 1rem;

            img {
                border-radius: 1rem;
            }
        }

        i {
            position: absolute;
            top: -10px;
            left: 60px;
            z-index: 1;
            font-size: 24px;
        }
    }
}

.testimonial-style2 {
    position: relative;
    z-index: 1;

    .single-testimonial-slide {
        transition-duration: 500ms;
        position: relative;
        z-index: 1;
        border: 1px solid transparent;
        padding: 1rem;
        border-radius: .5rem;
        margin: 1rem 0;

        .image-wrapper {
            position: relative;
            z-index: 1;
            margin: 0 auto 1.5rem;
            height: 80px;
            width: 80px;
            border-radius: 50%;

            &::after {
                width: 100%;
                height: 100%;
                border: 2px solid $gray;
                position: absolute;
                content: "";
                top: -5px;
                left: 5px;
                border-radius: 50%;
                z-index: -1;
            }

            >i {
                position: absolute;
                bottom: -5px;
                left: 5px;
                width: 30px;
                height: 30px;
                background-color: $warning;
                border-radius: 50%;
                text-align: center;
                line-height: 30px;
                color: $white;
                font-size: 1rem;
            }

            img {
                border-radius: 50%;
            }
        }

        .text-content {
            text-align: center;
        }
    }
}

.testimonial-style3 {
    position: relative;
    z-index: 1;

    .single-testimonial-slide {
        .text-content {
            p {
                font-weight: 500;
            }
        }
    }
}