<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulario de Cliente</title>
    <style>
        :root {
            --primary-color: #1e3a8a;
            --primary-light: #e9effd;
            --secondary-color: #f8f9fa;
            --border-color: #dee2e6;
            --text-color: #333;
            --header-blue: #1565c0; /* Nuevo color más intenso para los headers */
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        header {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .logo {
            max-height: 60px;
            margin-bottom: 15px;
        }
        
        h1 {
            color: var(--primary-color);
            margin: 0;
            font-size: 24px;
        }
        
        .section-header {
            background-color: var(--header-blue);
            padding: 10px 15px;
            margin: 20px 0 15px 0;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            text-align: center;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        td {
            padding: 8px 10px;
            border: 1px solid var(--border-color);
            width: 25%;
        }
        
        td.label {
            width: 20%;
            font-weight: 500;
            background-color: var(--secondary-color);
        }
        
        td.input-cell {
            width: 30%;
        }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            box-sizing: border-box;
            font-family: inherit;
        }
        
        .field-info, .field-type {
            display: none;
        }
        
        .has-dropdown::after {
            content: "▼";
            color: var(--primary-color);
            margin-left: 5px;
            font-size: 0.8em;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 20px;
        }
        
        button:hover {
            opacity: 0.9;
        }
        
        .actions {
            text-align: center;
            margin-top: 20px;
        }
        
        /* Reglas responsive para dispositivos móviles */
        @media screen and (max-width: 768px) {
            .container {
                padding: 10px;
                width: 100%;
            }
            
            table {
                display: block;
                width: 100%;
            }
            
            tr {
                display: flex;
                flex-direction: column;
                margin-bottom: 15px;
                border: 1px solid var(--border-color);
                width: 100%;
            }
            
            td {
                width: 100% !important;
                display: block;
                box-sizing: border-box;
                border: none;
                border-bottom: 1px solid var(--border-color);
            }
            
            td:last-child {
                border-bottom: none;
            }
            
            td.label {
                background: var(--primary-light);
                text-align: center;
                padding: 10px;
            }
            
            td.input-cell {
                padding: 10px;
                width: 100%;
            }
            
            input, select {
                width: 100%;
                max-width: 100%;
                box-sizing: border-box;
            }
            
            .section-header {
                margin: 30px 0 15px 0;
            }
        }
        
        /* Ajustes adicionales para pantallas muy pequeñas */
        @media screen and (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 20px;
            }
            
            .logo {
                max-height: 40px;
            }
        }

        /* Eliminar los estilos para inputs inválidos */
        input:invalid {
            border-color: var(--border-color);
            background-color: white;
        }

        /* Mensaje de error */
        .error-message {
            color: #dc3545;
            font-size: 0.8em;
            margin-top: 4px;
            display: none;
        }

        input:invalid + .error-message {
            display: block;
        }

        /* Estilo para mensajes informativos */
        .info-message {
            color: #6c757d;
            font-size: 0.8em;
            margin-top: 4px;
            display: block;
        }

        /* Reset básico para todos los elementos */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        /* Contenedor principal */
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        }
        
        /* Estilos base para la tabla e inputs */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        td {
            padding: 8px;
            border: 1px solid var(--border-color);
        }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 16px; /* Mejor tamaño para móviles */
        }
        
        /* Reglas responsive mejoradas */
        @media screen and (max-width: 768px) {
            body {
                padding: 0;
            }
            
            .container {
                padding: 10px;
                width: 100%;
                max-width: 100%;
            }
            
            table, tbody, tr {
                display: block;
                width: 100%;
            }
            
            tr {
                margin-bottom: 15px;
                background: #fff;
                border: 1px solid var(--border-color);
            }
            
            td {
                display: block;
                width: 100% !important;
                padding: 10px;
                border: none;
                border-bottom: 1px solid var(--border-color);
            }
            
            td:last-child {
                border-bottom: none;
            }
            
            td.label {
                background: var(--primary-light);
                text-align: center;
                font-weight: bold;
            }
            
            td.input-cell {
                padding: 15px;
            }
            
            /* Asegurar que los inputs ocupen todo el ancho */
            input, 
            select {
                display: block;
                width: 100%;
                max-width: none;
                min-width: 0;
                margin: 0;
                box-sizing: border-box;
            }
            
            /* Ajuste para los mensajes de error */
            .error-message {
                width: 100%;
                margin-top: 5px;
                font-size: 14px;
            }
        }
        
        /* Ajustes específicos para pantallas muy pequeñas */
        @media screen and (max-width: 480px) {
            .container {
                padding: 5px;
            }
            
            td.input-cell {
                padding: 10px;
            }
            
            input, 
            select {
                font-size: 16px;
                padding: 10px;
            }
        }

        /* Estilos para el indicador de pasos */
        .steps-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }

        .step-indicator {
            width: 30%;
            text-align: center;
            padding: 15px 0;
            border-radius: 5px;
            background-color: var(--secondary-color);
            position: relative;
            z-index: 2;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .step-indicator.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: bold;
        }

        .step-indicator:hover:not(.active) {
            background-color: #e0e0e0;
        }

        .progress-line {
            position: absolute;
            top: 50%;
            height: 3px;
            width: 100%;
            background-color: var(--secondary-color);
            z-index: 1;
            transform: translateY(-50%);
        }

        .progress-line .fill {
            height: 100%;
            background-color: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }

        .step-indicator::before {
            content: attr(data-step);
            display: inline-block;
            width: 24px;
            height: 24px;
            line-height: 24px;
            border-radius: 50%;
            background-color: var(--header-blue);
            color: white;
            margin-right: 8px;
        }

        /* Contenedor de secciones */
        .section-container {
            display: none;
        }

        .section-container.active {
            display: block;
        }

        /* Botones de navegación */
        .nav-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding: 10px 0;
        }

        .btn-prev, .btn-next, .btn-submit {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .btn-prev {
            background-color: #6c757d;
        }

        .btn-prev:hover, .btn-next:hover, .btn-submit:hover {
            opacity: 0.9;
        }

        /* Ocultamos la sección de acciones original */
        .actions {
            display: none;
        }

        @media screen and (max-width: 768px) {
            .steps-container {
                flex-direction: column;
                margin-bottom: 20px;
            }

            .step-indicator {
                width: 100%;
                margin-bottom: 10px;
            }

            .progress-line {
                display: none;
            }
        }

        /* Estilo para el mensaje de validación del RUT */
        .rut-message {
            color: #6c757d;
            font-size: 0.8em;
            margin-top: 4px;
            display: block;
            font-style: italic;
        }
        
        /* Estilo para los encabezados de subsección */
        .subsection-header {
            background-color: var(--primary-light);
            color: var(--primary-color);
            padding: 10px 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.95em;
        }
        
        /* Estilo para inputs readonly */
        input[readonly] {
            background-color: #f8f9fa;
            cursor: not-allowed;
        }
        
        /* Ajuste de márgenes entre subsecciones */
        table {
            margin-bottom: 25px;
        }
        
    </style>
</head>
<body>
    <div class="container">
        <header>
            <img src="img/img_experian/logo.jpg" alt="Logo Corporativo" class="logo" style="max-height: 100px;">
            <h1>Formulario de Registro de Cliente</h1>
        </header>
        
        <!-- Indicador de pasos -->
        <div class="steps-container">
            <div class="progress-line">
                <div class="fill"></div>
            </div>
            <div class="step-indicator active" data-step="1">1. Identificación del Cliente</div>
            <div class="step-indicator" data-step="2">2. Datos de Contactos</div>
            <div class="step-indicator" data-step="3">3. Servicios y Transacciones</div>
        </div>

        <!-- Sección 1: Identificación del cliente -->
        <div class="section-container active" id="section1">
            <div class="section-header">1. IDENTIFICACIÓN DEL CLIENTE</div>
            
            <table>
                <tr>
                    <td class="label">Tipo de Cliente</td>
                    <td class="input-cell">
                        <select title="Tipo de Cliente" required>
                            <option value="">Seleccione...</option>
                            <option>Cliente Vigente</option>
                            <option>Cliente No vigente</option>
                        </select>
                    </td>
                    <td class="label">Rut</td>
                    <td class="input-cell">
                        <input type="text" 
                               title="Rut" 
                               class="rut-input"
                               pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                               placeholder="12.345.678-9"
                               maxlength="12"
                               required>
                        <div class="rut-message">Formato: 12.345.678-9</div>
                    </td>
                </tr>
                <tr>
                    <td class="label">Razón Social</td>
                    <td class="input-cell">
                        <input type="text" title="Razón Social" required>
                    </td>
                    <td class="label">Nombre Representante Legal 1</td>
                    <td class="input-cell">
                        <input type="text" title="Nombre Representante Legal 1" required>
                    </td>
                </tr>
                <tr>
                    <td class="label">Rut Representante 1</td>
                    <td class="input-cell">
                        <input type="text" 
                               title="Rut Representante 1" 
                               class="rut-input"
                               pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                               placeholder="12.345.678-9"
                               maxlength="12">
                        <div class="rut-message">Formato: 12.345.678-9</div>
                    </td>
                    <td class="label">Nombre Representante Legal 2</td>
                    <td class="input-cell"><input type="text" title="Nombre Representante Legal 2"></td>
                </tr>
                <tr>
                    <td class="label">Rut Representante 2</td>
                    <td class="input-cell">
                        <input type="text" 
                               title="Rut Representante 2" 
                               class="rut-input"
                               pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                               placeholder="12.345.678-9"
                               maxlength="12">
                        <div class="rut-message">Formato: 12.345.678-9</div>
                    </td>
                    <td class="label">Nombre Representante Legal 3</td>
                    <td class="input-cell"><input type="text" title="Nombre Representante Legal 3"></td>
                </tr>
                <tr>
                    <td class="label">Rut Representante 3</td>
                    <td class="input-cell">
                        <input type="text" 
                               title="Rut Representante 3" 
                               class="rut-input"
                               pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                               placeholder="12.345.678-9"
                               maxlength="12">
                        <div class="rut-message">Formato: 12.345.678-9</div>
                    </td>
                    <td class="label">Sistema Creación de Empresa</td>
                    <td class="input-cell">
                        <select title="Sistema Creación de Empresa">
                            <option>Tradicional</option>
                            <option>Empresa por un día</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label">Fecha de creación</td>
                    <td class="input-cell">
                        <input type="date" title="Fecha de creación" required>
                    </td>
                    <td class="label">Notaría</td>
                    <td class="input-cell"><input type="text" title="Notaría"></td>
                </tr>
                <tr>
                    <td class="label">Actividad Económica SII</td>
                    <td class="input-cell"><input type="text" title="Actividad Económica SII"></td>
                    <td class="label">Fecha de Constitución</td>
                    <td class="input-cell">
                        <input type="date" title="Fecha de Constitución" required>
                    </td>
                </tr>
                <tr>
                    <td class="label">Dirección</td>
                    <td class="input-cell"><input type="text" title="Dirección"></td>
                    <td class="label">Comuna</td>
                    <td class="input-cell"><input type="text" title="Comuna"></td>
                </tr>
                <tr>
                    <td class="label">Página Web</td>
                    <td class="input-cell"><input type="text" title="Página Web"></td>
                    <td class="label">Correo Electrónico contacto</td>
                    <td class="input-cell">
                        <input type="email" title="Correo Electrónico contacto" 
                               pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$" 
                               required>
                        <div class="info-message">Formato: <EMAIL></div>
                    </td>
                </tr>
                <tr>
                    <td class="label">Teléfono</td>
                    <td class="input-cell">
                        <input type="tel" title="Teléfono" 
                               pattern="[0-9]{9}" 
                               maxlength="9"
                               placeholder="912345678"
                               required>
                        <div class="info-message">Debe contener 9 dígitos</div>
                    </td>
                    <td class="label">Clasificación de Cliente SII</td>
                    <td class="input-cell">
                        <select title="Clasificación de Cliente SII">
                            <option>Nuevo</option>
                            <option>Antiguo</option>
                        </select>
                    </td>
                </tr>
            </table>

            <div class="nav-buttons">
                <div></div> <!-- Espacio vacío para alinear a la derecha -->
                <button type="button" class="btn-next" data-next="2">Siguiente</button>
            </div>
        </div>
        
        <!-- Sección 2: Datos de Contactos -->
        <div class="section-container" id="section2">
            <div class="section-header">2. DATOS DE CONTACTOS</div>
            
            <table>
                <tr>
                    <td class="label">Contacto Nombre</td>
                    <td class="input-cell"><input type="text" title="Contacto Nombre"></td>
                    <td class="label">Rut</td>
                    <td class="input-cell">
                        <input type="text" 
                               title="Rut" 
                               class="rut-input"
                               pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                               placeholder="12.345.678-9"
                               maxlength="12">
                        <div class="rut-message">Formato: 12.345.678-9</div>
                    </td>
                </tr>
                <tr>
                    <td class="label">Teléfono</td>
                    <td class="input-cell">
                        <input type="text" title="Teléfono"
                               pattern="[0-9]{9}" 
                               maxlength="9"
                               placeholder="912345678">
                        <div class="info-message">Debe contener 9 dígitos</div>
                    </td>
                    <td class="label">Correo Electrónico</td>
                    <td class="input-cell">
                        <input type="email" title="Correo Electrónico"
                               pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                        <div class="info-message">Formato: <EMAIL></div>
                    </td>
                </tr>
                <td class="label">2.1 DATOS DE BACKUP</td>
                <tr>
                    <td class="label">Contacto Backup Nombre</td>
                    <td class="input-cell"><input type="text" title="Contacto Backup Nombre"></td>
                    <td class="label">Rut</td>
                    <td class="input-cell">
                        <input type="text" 
                               title="Rut" 
                               class="rut-input"
                               pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                               placeholder="12.345.678-9"
                               maxlength="12">
                        <div class="rut-message">Formato: 12.345.678-9</div>
                    </td>
                </tr>
                <tr>
                    <td class="label">Teléfono</td>
                    <td class="input-cell">
                        <input type="text" title="Teléfono"
                               pattern="[0-9]{9}" 
                               maxlength="9"
                               placeholder="912345678">
                        <div class="info-message">Debe contener 9 dígitos</div>
                    </td>
                    <td class="label">Correo Electrónico</td>
                    <td class="input-cell">
                        <input type="email" title="Correo Electrónico"
                               pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                        <div class="info-message">Formato: <EMAIL></div>
                    </td>
                </tr>
            </table>

            <div class="nav-buttons">
                <button type="button" class="btn-prev" data-prev="1">Anterior</button>
                <button type="button" class="btn-next" data-next="3">Siguiente</button>
            </div>
        </div>
        
        <!-- Sección 3: Servicios y Nivel de Transacciones -->
        <div class="section-container" id="section3">
            <div class="section-header">3. SERVICIOS Y NIVEL DE TRANSACCIONES / PUNTOS</div>
            
            <!-- Subsección 3.1: Publicación de Morosos -->
            <div class="subsection-header">3.1 PUBLICACIÓN DE MOROSOS</div>
            <table>
                <tr>
                    <td class="label">Plan</td>
                    <td class="input-cell">
                        <select title="Plan Publicación de Morosos">
                            <option value="">Seleccione...</option>
                            <option>XS</option>
                            <option>S</option>
                            <option>M</option>
                            <option>L</option>
                            <option>XL</option>
                        </select>
                    </td>
                    <td class="label">Número de Consultas</td>
                    <td class="input-cell">
                        <input type="text" title="Número de Consultas" placeholder="Ingrese número">
                    </td>
                </tr>
                <tr>
                    <td class="label">UF Mensual</td>
                    <td class="input-cell">
                        <input type="number" step="0.01" title="UF Mensual" placeholder="0.00">
                    </td>
                    <td class="label">% de descuento</td>
                    <td class="input-cell">
                        <select title="% de descuento">
                            <option value="0">0%</option>
                            <option value="5">5%</option>
                            <option value="10">10%</option>
                            <option value="15">15%</option>
                            <option value="20">20%</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label">Nuevo Valor</td>
                    <td class="input-cell">
                        <input type="number" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                    </td>
                    <td></td>
                    <td></td>
                </tr>
            </table>

            <!-- Subsección 3.2: Informe Advanced SME -->
            <div class="subsection-header">3.2 INFORME ADVANCED SME</div>
            <table>
                <tr>
                    <td class="label">Plan</td>
                    <td class="input-cell">
                        <select title="Plan Advanced SME">
                            <option value="">Seleccione...</option>
                            <option value="10">10 consultas</option>
                            <option value="20">20 consultas</option>
                            <option value="30">30 consultas</option>
                            <option value="40">40 consultas</option>
                        </select>
                    </td>
                    <td class="label">Número de Consultas</td>
                    <td class="input-cell">
                        <input type="text" title="Número de Consultas" placeholder="Ingrese número">
                    </td>
                </tr>
                <tr>
                    <td class="label">UF Mensual</td>
                    <td class="input-cell">
                        <input type="number" step="0.01" title="UF Mensual" placeholder="0.00">
                    </td>
                    <td class="label">% de descuento</td>
                    <td class="input-cell">
                        <select title="% de descuento">
                            <option value="0">0%</option>
                            <option value="5">5%</option>
                            <option value="10">10%</option>
                            <option value="15">15%</option>
                            <option value="20">20%</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label">Nuevo Valor</td>
                    <td class="input-cell">
                        <input type="number" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                    </td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
            
            <div class="nav-buttons">
                <button type="button" class="btn-prev" data-prev="2">Anterior</button>
                <button type="submit" class="btn-submit">Guardar Formulario</button>
            </div>
        </div>
    </div>

    <script>
        // Función mejorada para formatear RUT
        function formatRut(input) {
            // Eliminar todos los caracteres que no sean números o 'k'
            let value = input.value.replace(/[^\dkK]/g, '');
            
            // Limitar a 9 caracteres
            if (value.length > 9) {
                value = value.slice(0, 9);
            }
            
            if (value.length > 1) {
                // Separar el dígito verificador
                let dv = value.slice(-1);
                let rut = value.slice(0, -1);
                
                // Formatear con puntos
                while(/(\d)(?=(\d{3})+(?!\d))/g.test(rut)) {
                    rut = rut.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
                }
                
                input.value = rut + '-' + dv.toUpperCase();
            }
        }

        // Validación de RUT en tiempo real
        document.querySelectorAll('input[pattern*="[0-9]{1,2}"]').forEach(input => {
            input.addEventListener('input', function(e) {
                // Permitir solo números y 'k'
                this.value = this.value.replace(/[^\d\.kK\-]/g, '');
                formatRut(this);
            });
            
            input.addEventListener('keydown', function(e) {
                // Permitir solo teclas específicas
                const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight'];
                if (!allowedKeys.includes(e.key) && !/[\dkK]/.test(e.key)) {
                    e.preventDefault();
                }
            });
        });

        // Navegación entre secciones
        document.querySelectorAll('.btn-next').forEach(btn => {
            btn.addEventListener('click', function() {
                const nextSection = this.getAttribute('data-next');
                showSection(nextSection);
            });
        });

        document.querySelectorAll('.btn-prev').forEach(btn => {
            btn.addEventListener('click', function() {
                const prevSection = this.getAttribute('data-prev');
                showSection(prevSection);
            });
        });

        document.querySelectorAll('.step-indicator').forEach(step => {
            step.addEventListener('click', function() {
                const stepNumber = this.getAttribute('data-step');
                showSection(stepNumber);
            });
        });

        function showSection(sectionNumber) {
            // Ocultar todas las secciones
            document.querySelectorAll('.section-container').forEach(section => {
                section.classList.remove('active');
            });
            
            // Mostrar la sección seleccionada
            document.getElementById('section' + sectionNumber).classList.add('active');
            
            // Actualizar indicadores de paso
            document.querySelectorAll('.step-indicator').forEach(step => {
                step.classList.remove('active');
                if (step.getAttribute('data-step') === sectionNumber) {
                    step.classList.add('active');
                }
            });
            
            // Actualizar la barra de progreso
            const progressFill = document.querySelector('.progress-line .fill');
            const percentage = ((parseInt(sectionNumber) - 1) / 2) * 100;
            progressFill.style.width = percentage + '%';
        }

        // Validación del formulario antes de enviar
        document.querySelector('.btn-submit').addEventListener('click', function(e) {
            let isValid = true;
            
            // Validar todos los campos requeridos
            document.querySelectorAll('input[required], select[required]').forEach(input => {
                if (!input.value) {
                    isValid = false;
                    input.classList.add('invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Por favor, complete todos los campos requeridos correctamente.');
            }
        });

        // Función para validar RUT chileno
        function validarRut(rut) {
            if (!/^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$/.test(rut)) {
                return false;
            }
            
            const rutLimpio = rut.replace(/\./g, '').replace('-', '');
            const dv = rutLimpio.slice(-1).toUpperCase();
            const rutNumero = parseInt(rutLimpio.slice(0, -1));
            
            let suma = 0;
            let multiplicador = 2;
            
            for (let i = String(rutNumero).split('').reverse().join(''); i; i = i.slice(1)) {
                suma += parseInt(i) * multiplicador;
                multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
            }
            
            const dvEsperado = 11 - (suma % 11);
            const dvCalculado = dvEsperado === 11 ? '0' : dvEsperado === 10 ? 'K' : String(dvEsperado);
            
            return dv === dvCalculado;
        }

        // Función mejorada para formatear RUT
        function formatearRut(input) {
            let valor = input.value.replace(/[^\dkK]/g, '');
            
            // Limitar a 9 caracteres
            if (valor.length > 9) {
                valor = valor.slice(0, 9);
            }
            
            if (valor.length > 1) {
                const dv = valor.slice(-1);
                const rut = valor.slice(0, -1);
                
                // Formatear con puntos
                let rutFormateado = '';
                for (let i = rut.length - 1, j = 0; i >= 0; i--, j++) {
                    rutFormateado = rut.charAt(i) + rutFormateado;
                    if ((j + 1) % 3 === 0 && i !== 0) {
                        rutFormateado = '.' + rutFormateado;
                    }
                }
                
                input.value = rutFormateado + '-' + dv.toUpperCase();
            }
        }

        // Aplicar formato y validación a todos los campos RUT
        document.querySelectorAll('.rut-input').forEach(input => {
            input.addEventListener('input', function(e) {
                // Permitir solo números y 'k'
                this.value = this.value.replace(/[^\d\.kK\-]/g, '');
                formatearRut(this);
            });
            
            input.addEventListener('blur', function() {
                if (this.value && !validarRut(this.value)) {
                    this.setCustomValidity('RUT inválido');
                    this.classList.add('is-invalid');
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('is-invalid');
                }
            });
            
            input.addEventListener('keydown', function(e) {
                // Permitir solo teclas específicas
                const teclasPermitidas = ['Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight'];
                if (!teclasPermitidas.includes(e.key) && !/[\dkK]/.test(e.key)) {
                    e.preventDefault();
                }
            });
        });

        // Función para calcular el nuevo valor basado en el descuento
        function calcularNuevoValor(ufMensual, descuento) {
            const uf = parseFloat(ufMensual) || 0;
            const desc = parseFloat(descuento) || 0;
            return (uf * (1 - desc/100)).toFixed(2);
        }

        // Aplicar cálculo automático de nuevo valor para ambas subsecciones
        document.querySelectorAll('table').forEach(table => {
            const ufInput = table.querySelector('input[title="UF Mensual"]');
            const descuentoSelect = table.querySelector('select[title="% de descuento"]');
            const nuevoValorInput = table.querySelector('input[title="Nuevo Valor"]');
            
            if (ufInput && descuentoSelect && nuevoValorInput) {
                const calcular = () => {
                    nuevoValorInput.value = calcularNuevoValor(
                        ufInput.value,
                        descuentoSelect.value
                    );
                };
                
                ufInput.addEventListener('input', calcular);
                descuentoSelect.addEventListener('change', calcular);
            }
        });

    </script>
</body>
</html>