<?php
/**
 * Script de pruebas para el sistema de registro de prospectos
 * Verifica todas las funcionalidades y dependencias
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 Test del Sistema de Registro de Prospectos</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
</style>";

$tests_passed = 0;
$tests_failed = 0;

function test_result($test_name, $passed, $message = '') {
    global $tests_passed, $tests_failed;
    
    if ($passed) {
        echo "<p class='success'>✅ $test_name: PASÓ</p>";
        $tests_passed++;
    } else {
        echo "<p class='error'>❌ $test_name: FALLÓ - $message</p>";
        $tests_failed++;
    }
}

// Test 1: Verificar conexión a base de datos
echo "<div class='test-section'>";
echo "<h2>1. Conexión a Base de Datos</h2>";

try {
    require_once("dist/con_db.php");
    test_result("Conexión a BD", isset($mysqli) && !$mysqli->connect_error, $mysqli->connect_error ?? '');
} catch (Exception $e) {
    test_result("Conexión a BD", false, $e->getMessage());
}

// Test 2: Verificar estructura de tablas
echo "</div><div class='test-section'>";
echo "<h2>2. Estructura de Tablas</h2>";

$required_tables = [
    'tb_experian_usuarios',
    'tb_experian_prospecto',
    'tb_experian_prospecto_bitacora'
];

foreach ($required_tables as $table) {
    try {
        $result = $mysqli->query("SHOW TABLES LIKE '$table'");
        test_result("Tabla $table", $result && $result->num_rows > 0);
    } catch (Exception $e) {
        test_result("Tabla $table", false, $e->getMessage());
    }
}

// Test 3: Verificar campos de tb_experian_usuarios
echo "</div><div class='test-section'>";
echo "<h2>3. Campos de tb_experian_usuarios</h2>";

$required_user_fields = ['id', 'correo', 'clave', 'rol', 'nombre_usuario', 'proyecto', 'rut_ejecutivo'];

try {
    $result = $mysqli->query("DESCRIBE tb_experian_usuarios");
    $existing_fields = [];
    
    while ($row = $result->fetch_assoc()) {
        $existing_fields[] = $row['Field'];
    }
    
    foreach ($required_user_fields as $field) {
        test_result("Campo usuarios.$field", in_array($field, $existing_fields));
    }
} catch (Exception $e) {
    test_result("Verificación campos usuarios", false, $e->getMessage());
}

// Test 4: Verificar campos de tb_experian_prospecto
echo "</div><div class='test-section'>";
echo "<h2>4. Campos de tb_experian_prospecto</h2>";

$required_prospect_fields = [
    'id', 'nombre_ejecutivo', 'rut_ejecutivo', 'razon_social', 'rubro',
    'direccion_comercial', 'telefono', 'email', 'num_pos', 'tipo_cuenta',
    'num_cuenta_bancaria', 'dias_atencion', 'horario_atencion',
    'contrata_boleta', 'competencia_actual', 'observaciones',
    'archivo_documentacion', 'fecha_registro', 'usuario_id'
];

try {
    $result = $mysqli->query("DESCRIBE tb_experian_prospecto");
    $existing_fields = [];
    
    while ($row = $result->fetch_assoc()) {
        $existing_fields[] = $row['Field'];
    }
    
    foreach ($required_prospect_fields as $field) {
        test_result("Campo prospecto.$field", in_array($field, $existing_fields));
    }
} catch (Exception $e) {
    test_result("Verificación campos prospecto", false, $e->getMessage());
}

// Test 5: Verificar archivos del sistema
echo "</div><div class='test-section'>";
echo "<h2>5. Archivos del Sistema</h2>";

$required_files = [
    'dist/guardar_prospecto_nuevo.php',
    'dist/prospect_form_modal.html',
    'dist/css/prospect-form.css',
    'dist/js/prospect-form.js',
    'dist/endpoints/get_user_info.php',
    'dist/email_config.php',
    'dist/upload_manager.php'
];

foreach ($required_files as $file) {
    test_result("Archivo $file", file_exists($file));
}

// Test 6: Verificar directorio de uploads
echo "</div><div class='test-section'>";
echo "<h2>6. Directorio de Uploads</h2>";

$upload_dir = 'dist/uploads/prospectos/';
test_result("Directorio uploads existe", is_dir($upload_dir));
test_result("Directorio uploads escribible", is_writable($upload_dir));

// Test 7: Verificar configuración de email
echo "</div><div class='test-section'>";
echo "<h2>7. Configuración de Email</h2>";

try {
    require_once("dist/email_config.php");
    $email_errors = validarConfiguracionEmail();
    
    if (empty($email_errors)) {
        test_result("Configuración email", true);
    } else {
        test_result("Configuración email", false, implode(', ', $email_errors));
        echo "<p class='warning'>⚠️ Configurar: " . implode(', ', $email_errors) . "</p>";
    }
} catch (Exception $e) {
    test_result("Configuración email", false, $e->getMessage());
}

// Test 8: Verificar funciones de validación
echo "</div><div class='test-section'>";
echo "<h2>8. Funciones de Validación</h2>";

// Test RUT válido
$rut_valido = '12345678K';
$rut_invalido = '123456789';

// Simular función de validación de RUT
function validarRUTTest($rut) {
    $rut = preg_replace('/[^0-9kK]/', '', $rut);
    
    if (strlen($rut) < 8 || strlen($rut) > 9) {
        return false;
    }
    
    $dv = strtoupper(substr($rut, -1));
    $numero = substr($rut, 0, -1);
    
    $suma = 0;
    $multiplicador = 2;
    
    for ($i = strlen($numero) - 1; $i >= 0; $i--) {
        $suma += $numero[$i] * $multiplicador;
        $multiplicador = $multiplicador == 7 ? 2 : $multiplicador + 1;
    }
    
    $resto = $suma % 11;
    $dv_calculado = 11 - $resto;
    
    if ($dv_calculado == 11) $dv_calculado = '0';
    if ($dv_calculado == 10) $dv_calculado = 'K';
    
    return $dv == $dv_calculado;
}

test_result("Validación RUT válido", validarRUTTest($rut_valido));
test_result("Validación RUT inválido", !validarRUTTest($rut_invalido));

// Test 9: Verificar usuarios de prueba
echo "</div><div class='test-section'>";
echo "<h2>9. Usuarios de Prueba</h2>";

try {
    $result = $mysqli->query("SELECT COUNT(*) as count FROM tb_experian_usuarios");
    $row = $result->fetch_assoc();
    $user_count = $row['count'];
    
    test_result("Usuarios existentes", $user_count > 0, "Encontrados: $user_count usuarios");
    
    // Verificar si existen usuarios con proyecto 'inteletGroup'
    $result = $mysqli->query("SELECT COUNT(*) as count FROM tb_experian_usuarios WHERE proyecto = 'inteletGroup'");
    $row = $result->fetch_assoc();
    $intelet_users = $row['count'];
    
    test_result("Usuarios InteletGroup", $intelet_users > 0, "Encontrados: $intelet_users usuarios");
    
} catch (Exception $e) {
    test_result("Verificación usuarios", false, $e->getMessage());
}

// Test 10: Test de inserción de prospecto (simulado)
echo "</div><div class='test-section'>";
echo "<h2>10. Test de Inserción (Simulado)</h2>";

$test_data = [
    'nombre_ejecutivo' => 'Test User',
    'rut_ejecutivo' => '12345678K',
    'razon_social' => 'EMPRESA TEST LTDA',
    'telefono' => '987654321'
];

try {
    // Verificar que no existe el RUT de prueba
    $stmt = $mysqli->prepare("SELECT id FROM tb_experian_prospecto WHERE rut_ejecutivo = ?");
    $stmt->bind_param("s", $test_data['rut_ejecutivo']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        test_result("RUT test disponible", true);
    } else {
        test_result("RUT test disponible", false, "RUT ya existe en BD");
    }
    
    $stmt->close();
} catch (Exception $e) {
    test_result("Test inserción", false, $e->getMessage());
}

// Resumen final
echo "</div><div class='test-section'>";
echo "<h2>📊 Resumen de Pruebas</h2>";

$total_tests = $tests_passed + $tests_failed;
$success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;

echo "<p><strong>Total de pruebas:</strong> $total_tests</p>";
echo "<p class='success'><strong>Pruebas exitosas:</strong> $tests_passed</p>";
echo "<p class='error'><strong>Pruebas fallidas:</strong> $tests_failed</p>";
echo "<p><strong>Tasa de éxito:</strong> $success_rate%</p>";

if ($success_rate >= 90) {
    echo "<p class='success'>🎉 <strong>Sistema listo para producción!</strong></p>";
} elseif ($success_rate >= 70) {
    echo "<p class='warning'>⚠️ <strong>Sistema funcional con algunas mejoras pendientes</strong></p>";
} else {
    echo "<p class='error'>❌ <strong>Sistema requiere correcciones antes de usar</strong></p>";
}

// Instrucciones de configuración
echo "</div><div class='test-section'>";
echo "<h2>📋 Instrucciones de Configuración</h2>";

echo "<h3>Para completar la configuración:</h3>";
echo "<ol>";
echo "<li>Ejecutar <code>update_usuarios_schema.php</code> para actualizar la estructura de usuarios</li>";
echo "<li>Ejecutar <code>create_new_users.php</code> para crear los usuarios de InteletGroup</li>";
echo "<li>Ejecutar <code>create_prospect_table.sql</code> para actualizar la tabla de prospectos</li>";
echo "<li>Configurar credenciales de email en <code>dist/email_config.php</code></li>";
echo "<li>Verificar permisos del directorio <code>dist/uploads/prospectos/</code></li>";
echo "<li>Integrar el formulario en <code>form_experian2.php</code> o usar <code>prospect_integration.php</code></li>";
echo "</ol>";

echo "<h3>Para usar el sistema:</h3>";
echo "<ol>";
echo "<li>Incluir los archivos CSS y JS en la página principal</li>";
echo "<li>Agregar el modal HTML donde sea necesario</li>";
echo "<li>Llamar a <code>abrirModalProspecto()</code> para abrir el formulario</li>";
echo "</ol>";

echo "</div>";

if (isset($mysqli)) {
    $mysqli->close();
}
?>
