<?php
/**
 * Script de prueba para verificar la redirección basada en proyectos
 * Este script muestra los usuarios y sus proyectos para verificar la configuración
 */

require_once("dist/con_db.php");

echo "<h2>Test de Redirección por Proyecto</h2>";
echo "<p>Este script muestra los usuarios y sus proyectos configurados en la base de datos.</p>";

try {
    // Consultar todos los usuarios y sus proyectos
    $sql = "SELECT id, correo, nombre_usuario, rol, proyecto, rut_ejecutivo FROM tb_experian_usuarios ORDER BY proyecto, correo";
    $result = $mysqli->query($sql);
    
    if (!$result) {
        throw new Exception("Error en la consulta: " . $mysqli->error);
    }
    
    echo "<h3>Usuarios en la Base de Datos:</h3>";
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Correo</th><th>Nombre Usuario</th><th>Rol</th><th>Proyecto</th><th>RUT Ejecutivo</th><th>Redirección</th>";
    echo "</tr>";
    
    $count_experian = 0;
    $count_inteletgroup = 0;
    $count_otros = 0;
    
    while ($row = $result->fetch_assoc()) {
        $proyecto = $row['proyecto'] ?? 'NULL';
        
        // Determinar URL de redirección
        $redirectUrl = '';
        $bgColor = '';
        if ($proyecto === 'experian') {
            $redirectUrl = 'form_experian2.php';
            $bgColor = '#e3f2fd'; // Azul claro
            $count_experian++;
        } elseif ($proyecto === 'inteletGroup') {
            $redirectUrl = 'form_inteletgroup.php';
            $bgColor = '#e8f5e8'; // Verde claro
            $count_inteletgroup++;
        } else {
            $redirectUrl = 'form_experian2.php (por defecto)';
            $bgColor = '#fff3e0'; // Naranja claro
            $count_otros++;
        }
        
        echo "<tr style='background-color: $bgColor;'>";
        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['correo']) . "</td>";
        echo "<td>" . htmlspecialchars($row['nombre_usuario'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($row['rol']) . "</td>";
        echo "<td><strong>" . htmlspecialchars($proyecto) . "</strong></td>";
        echo "<td>" . htmlspecialchars($row['rut_ejecutivo'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($redirectUrl) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Mostrar resumen
    echo "<h3>Resumen de Proyectos:</h3>";
    echo "<ul>";
    echo "<li><strong>Usuarios Experian:</strong> $count_experian (van a form_experian2.php)</li>";
    echo "<li><strong>Usuarios InteletGroup:</strong> $count_inteletgroup (van a form_inteletgroup.php)</li>";
    echo "<li><strong>Otros/NULL:</strong> $count_otros (van a form_experian2.php por defecto)</li>";
    echo "</ul>";
    
    // Mostrar lógica de redirección
    echo "<h3>Lógica de Redirección Implementada:</h3>";
    echo "<div style='background-color: #f5f5f5; padding: 15px; border-left: 4px solid #2196F3;'>";
    echo "<pre>";
    echo "if (\$proyecto === 'experian') {\n";
    echo "    \$redirectUrl = 'form_experian2.php';\n";
    echo "} elseif (\$proyecto === 'inteletGroup') {\n";
    echo "    \$redirectUrl = 'form_inteletgroup.php';\n";
    echo "} else {\n";
    echo "    \$redirectUrl = 'form_experian2.php'; // Fallback\n";
    echo "}";
    echo "</pre>";
    echo "</div>";
    
    // Instrucciones para cambiar proyectos
    echo "<h3>Para Cambiar el Proyecto de un Usuario:</h3>";
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107;'>";
    echo "<p><strong>Ejecutar en la base de datos:</strong></p>";
    echo "<pre>";
    echo "-- Para cambiar a proyecto Experian:\n";
    echo "UPDATE tb_experian_usuarios SET proyecto = 'experian' WHERE correo = '<EMAIL>';\n\n";
    echo "-- Para cambiar a proyecto InteletGroup:\n";
    echo "UPDATE tb_experian_usuarios SET proyecto = 'inteletGroup' WHERE correo = '<EMAIL>';";
    echo "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background-color: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}

echo "<hr>";
echo "<h3>Archivos Modificados:</h3>";
echo "<ul>";
echo "<li><strong>ControllerGestar_Secure.php:</strong> Controlador principal de login con redirección por proyecto</li>";
echo "<li><strong>ControllerGestar.php:</strong> Controlador de respaldo con redirección por proyecto</li>";
echo "<li><strong>form_inteletgroup.php:</strong> Nuevo formulario para usuarios de InteletGroup</li>";
echo "</ul>";

echo "<h3>Próximos Pasos:</h3>";
echo "<ol>";
echo "<li>Probar el login con usuarios de diferentes proyectos</li>";
echo "<li>Verificar que la redirección funcione correctamente</li>";
echo "<li>Desarrollar las funcionalidades específicas para InteletGroup</li>";
echo "<li>Migrar usuarios según corresponda</li>";
echo "</ol>";

echo "<p><em>Fecha de prueba: " . date('Y-m-d H:i:s') . "</em></p>";
?>
