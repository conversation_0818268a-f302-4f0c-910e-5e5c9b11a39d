/* :: Table */

.table {
    font-size: 14px;

    tr {

        td,
        th {
            font-weight: 400;
        }
    }

    thead {
        tr {

            td,
            th {
                font-weight: 500;
            }
        }
    }
}

.dataTable-top {
    padding: 0;

    .dataTable-selector {
        width: 3.75rem;
        transition-duration: 500ms;
        border: 1px solid $border;
        padding: .125rem .5rem;
        height: 2rem;
        font-size: 12px;
        color: $heading;
        background-color: $white;
        border-radius: .25rem;

        &:focus {
            outline: none !important;
        }
    }

    .dataTable-input {
        width: 7rem;
        height: 2rem;
        transition-duration: 500ms;
        border: 1px solid $border;
        font-size: 12px;
        color: $heading;
        padding: .125rem .5rem;
        border-radius: .25rem;

        &:focus-visible {
            border: 0;
        }
    }
}

.dataTable-container {
    border-bottom: 0 !important;
    margin: 1rem 0;

    .dataTable-table {
        overflow-x: scroll;

        thead {
            >tr {
                >th {
                    border: 1px solid $border;
                    font-size: 12px;
                    background-color: $gray;
                }
            }
        }

        tbody {
            tr {

                td,
                th {
                    font-size: 12px;
                    border: 1px solid $border;
                }
            }
        }
    }
}

.dataTable-bottom {
    padding: 0;
}

.dataTable-pagination {
    display: flex;
    align-items: center;

    li {
        a {
            height: 1.25rem;
            color: $heading;
            font-size: 14px;
            padding: 0 .375rem;
            line-height: 1.25rem;
            border-radius: .25rem;

            &:hover,
            &:focus {
                background-color: $primary;
                color: $white;
            }
        }

        &.active {
            a {
                background-color: $primary;
                color: $white;
            }
        }

        &:first-child a,
        &:last-child a {
            font-size: 18px;
            padding: 0 .125rem;
        }
    }
}

.dataTable-info {
    margin: 0;
    font-size: 14px;
}

.dataTable-sorter::after,
.dataTable-sorter::before {
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    opacity: .3;
    right: 0;
}

.dataTable-sorter::before {
    border-top: 6px solid $heading;
    bottom: 1px;
}

.dataTable-sorter::after {
    border-bottom: 6px solid $heading;
    border-top: 6px solid transparent;
    top: -3px;
}

.asc .dataTable-sorter::after,
.desc .dataTable-sorter::before {
    opacity: 1;
}