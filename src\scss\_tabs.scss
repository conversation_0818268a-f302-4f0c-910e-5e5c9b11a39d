/* Tab */

.tab-content {
    border: 1px solid $border;
}

.nav-tabs {
    .nav-link {
        font-size: 14px;
    }
}

.standard-tab {
    .nav {
        border: 1px solid $border;
    }

    .tab-content {
        border: 1px solid $border;
    }

    .nav-item {
        flex-grow: 1;
    }

    .btn {
        padding: 6px 8px;
        width: 100%;
        border: 0 !important;

        &.active {
            background-color: $primary;
            color: $white;
            box-shadow: 0 0 0 .25rem rgba(13, 110, 253, .25);
        }
    }
}

.minimal-tab {
    .btn {
        padding: 0 .625rem .5rem;
        width: 100%;
        border-radius: 0;
        line-height: 1;
        border-left: 0;
        border-right: 0;
        border-top: 0;
        border-bottom: 3px solid transparent;
        margin-bottom: -2px;

        &:focus {
            box-shadow: none;
        }

        &.active {
            border-bottom-color: $primary;
            color: $primary;
        }
    }
}

.colorful-tab {
    .nav {
        border-radius: 20rem;
        background-color: rgba(255, 255, 255, .125);

        .nav-item {
            flex-grow: 1;
        }
    }

    .btn {
        border-radius: 5rem;
        width: 100%;
        background-color: transparent;
        border: 0;
        color: rgba(255, 255, 255, .5);
        font-weight: 500;

        &:focus {
            box-shadow: none;
        }

        &.active {
            background-color: rgba(255, 255, 255, .25);
            color: $white;
        }
    }

    .tab-content {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 1rem;
        border: 0;
    }
}