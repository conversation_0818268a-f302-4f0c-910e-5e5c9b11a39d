<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">

      <!-- # Header Five Layout -->
      <!-- Header Content -->
      <div class="header-content header-style-five position-relative d-flex align-items-center justify-content-between">
        <!-- Logo Wrapper -->
        <div class="logo-wrapper">
          <a href="home.html">
            <img src="img/core-img/logo.png" alt="">
          </a>
        </div>

        <!-- Navbar Toggler -->
        <div class="navbar--toggler" id="affanNavbarToggler" data-bs-toggle="offcanvas" data-bs-target="#affanOffcanvas"
          aria-controls="affanOffcanvas">
          <span class="d-block"></span>
          <span class="d-block"></span>
          <span class="d-block"></span>
        </div>
      </div>
      <!-- # Header Five Layout End -->

    </div>
  </div>

  <!-- Offcanvas Start -->
  <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1"
    aria-labelledby="affanOffcanvsLabel">

    <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
      aria-label="Close"></button>

    <div class="offcanvas-body p-0">
      <div class="sidenav-wrapper">
        <!-- Sidenav Profile -->
        <div class="sidenav-profile bg-gradient">
          <div class="sidenav-style1"></div>

          <!-- User Thumbnail -->
          <div class="user-profile">
            <img src="img/bg-img/2.jpg" alt="">
          </div>

          <!-- User Info -->
          <div class="user-info">
            <h6 class="user-name mb-0">Affan Islam</h6>
            <span>CEO, Designing World</span>
          </div>
        </div>

        <!-- Sidenav Nav -->
        <ul class="sidenav-nav ps-0">
          <li>
            <a href="home.html"><i class="bi bi-house-door"></i> Home</a>
          </li>
          <li>
            <a href="elements.html"><i class="bi bi-folder2-open"></i> Elements
              <span class="badge bg-danger rounded-pill ms-2">220+</span>
            </a>
          </li>
          <li>
            <a href="pages.html"><i class="bi bi-collection"></i> Pages
              <span class="badge bg-success rounded-pill ms-2">100+</span>
            </a>
          </li>
          <li>
            <a href="#"><i class="bi bi-cart-check"></i> Shop</a>
            <ul>
              <li>
                <a href="shop-grid.html"> Shop Grid</a>
              </li>
              <li>
                <a href="shop-list.html"> Shop List</a>
              </li>
              <li>
                <a href="shop-details.html"> Shop Details</a>
              </li>
              <li>
                <a href="cart.html"> Cart</a>
              </li>
              <li>
                <a href="checkout.html"> Checkout</a>
              </li>
            </ul>
          </li>
          <li>
            <a href="settings.html"><i class="bi bi-gear"></i> Settings</a>
          </li>
          <li>
            <div class="night-mode-nav">
              <i class="bi bi-moon"></i> Night Mode
              <div class="form-check form-switch">
                <input class="form-check-input form-check-success" id="darkSwitch" type="checkbox">
              </div>
            </div>
          </li>
          <li>
            <a href="login.html"><i class="bi bi-box-arrow-right"></i> Logout</a>
          </li>
        </ul>

        <!-- Social Info -->
        <div class="social-info-wrap">
          <a href="#">
            <i class="bi bi-facebook"></i>
          </a>
          <a href="#">
            <i class="bi bi-twitter"></i>
          </a>
          <a href="#">
            <i class="bi bi-linkedin"></i>
          </a>
        </div>

        <!-- Copyright Info -->
        <div class="copyright-info">
          <p>
            <span id="copyrightYear"></span>
            &copy; Made by <a href="#">Designing World</a>
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3" id="elementsSearchList">
    <div class="container">
      <!-- Search Form -->
      <div class="card">
        <div class="card-body p-3">
          <div class="form-group mb-0">
            <input class="form-control" id="elementsSearchInput" type="text" onkeyup="elementsSearch()"
              placeholder="Search element...">
          </div>
        </div>
      </div>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-list"></i>
          <div class="heading-text">
            <h6 class="mb-1">Navigation</h6>
            <span>Modern header, footer, and sidebar nav.</span>
          </div>
        </div>
      </div>

      <a class="affan-element-item" href="header-menu.html">
        Header Variations
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="footer-menu.html">
        Footer Variations
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="sidebar-left-menu.html">
        Left Sidebar Nav
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="sidebar-right-menu.html">
        Right Sidebar Nav
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-bell-fill"></i>
          <div class="heading-text">
            <h6 class="mb-1">Notifications</h6>
            <span>Display alerts &amp; notifications creatively.</span>
          </div>
        </div>
      </div>

      <a class="affan-element-item" href="alerts.html">
        Alerts
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="toasts.html">
        Toasts
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="online-offline-detection.html">
        Online / Offline Detection
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-ui-checks"></i>
          <div class="heading-text">
            <h6 class="mb-1">Form Layouts</h6>
            <span>All form input components.</span>
          </div>
        </div>
      </div>

      <a class="affan-element-item" href="form-input.html">
        Input
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-textarea.html">
        Textarea
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-select.html">
        Select
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-input-group.html">
        Input Group
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-check.html">
        Checkbox
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-radio.html">
        Radio
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-file-upload.html">
        File Upload
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-range.html">
        Range
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-auto-complete.html">
        Auto Complete
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-switches.html">
        Switches
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="form-validation.html">
        Form Validation
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-columns-gap"></i>
          <div class="heading-text">
            <h6 class="mb-1">UI Elements</h6>
            <span>Beautifully designed lots of UI elements.</span>
          </div>
        </div>
      </div>

      <a class="affan-element-item" href="accordion.html">
        Accordion
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="badge.html">
        Badge
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="button.html">
        Button
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="breadcrumb.html">
        Breadcrumb
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="timeline.html">
        Timeline
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="card.html">
        Card
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="image-gallery.html">
        Image Gallery
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="hero-blocks.html">
        Hero Blocks
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="tab.html">
        Tab
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="offcanvas.html">
        Offcanvas
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="user-ratings.html">
        User Ratings
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="testimonial.html">
        Testimonials
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="call-to-action.html">
        Call to Action
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="partner-logo.html">
        Partner Logo
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-tools"></i>
          <div class="heading-text">
            <h6 class="mb-1">Helpers</h6>
            <span>Quickly create any blocks with helpers.</span>
          </div>
        </div>
      </div>

      <a class="affan-element-item" href="borders.html">
        Borders
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="colors.html">
        Colors
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="dividers.html">
        Dividers
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="embed-video.html">
        Embeds Video
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="images.html">
        Images
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="list-group.html">
        List Group
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="modal.html">
        Modal
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="pagination.html">
        Pagination
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="progress-bar.html">
        Progress Bar
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="scrollspy.html">
        Scrollspy
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="spinners.html">
        Spinners
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="stretched-link.html">
        Stretched link
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="shadows.html">
        Shadows
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="sizing.html">
        Sizing
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="tooltips.html">
        Tooltips
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="text-truncation.html">
        Text truncation
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="typography.html">
        Typography
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="text.html">
        Text
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-sliders"></i>
          <div class="heading-text">
            <h6 class="mb-1">Carousels</h6>
            <span>Create a variety of carousels.</span>
          </div>
        </div>
      </div>

      <a class="affan-element-item" href="bootstrap-carousel.html">
        Bootstrap Carousel
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="tiny-slider.html">
        Tiny Slider
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-table"></i>
          <div class="heading-text">
            <h6 class="mb-1">Tables</h6>
            <span>Make responsive table layouts.</span>
          </div>
        </div>
      </div>

      <a class="affan-element-item" href="basic-table.html">
        Basic Table
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="data-table.html">
        Data Table
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="price-table.html">
        Price Table
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="comparison-table.html">
        Comparison Table
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-clock-history"></i>
          <div class="heading-text">
            <h6 class="mb-1">Timer</h6>
            <span>Countdown or countup your milestones.</span>
          </div>
        </div>
      </div>

      <a class="affan-element-item" href="countdown.html">
        Count Down
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="counterup.html">
        Counter Up
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-bar-chart"></i>
          <div class="heading-text">
            <h6 class="mb-1">Charts</h6>
            <span>Lots of charts for showing data.</span>
          </div>
        </div>
      </div>

      <a class="affan-element-item" href="area-charts.html">
        Area Chart
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="column-charts.html">
        Column Chart
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item" href="line-charts.html">
        Line Chart
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>
      <a class="affan-element-item mb-0" href="pie-charts.html">
        Pie Chart
        <i class="bi bi-caret-right-fill fz-12"></i>
      </a>

      <script>
        function elementsSearch() {
          var input = document.getElementById('elementsSearchInput');
          var filter = input.value.toUpperCase();
          var list = document.getElementById("elementsSearchList");
          var listItem = list.getElementsByClassName('affan-element-item');

          for (i = 0; i < listItem.length; i++) {
            var a = listItem[i];
            var textValue = a.textContent || a.innerText;
            if (textValue.toUpperCase().indexOf(filter) > -1) {
              listItem[i].style.display = "";
            } else {
              listItem[i].style.display = "none";
            }
          }
        }
      </script>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li>
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li class="active">
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>