<!DOCTYPE html>
<?php

header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.
session_start();

$inc = include("con_db.php");

$usuario = $_GET["usuario"];

?>

<html lang="en">



<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>TQW APP</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/logo_con.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">

  <style>
    .pop-up {
      display: none;
      position: fixed;
      z-index: 1;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0, 0, 0, 0.4);
    }

    .pop-up-contenido {
      background-color: #fefefe;
      margin: 5% auto;
      padding: 20px;
      border: 1px solid #888;
      width: 90%;
      height: 700px;
      overflow-x: auto;
    }


    .cerrar {
      color: #aaaaaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
    }

    .cerrar:hover,
    .cerrar:focus {
      color: #000;
      text-decoration: none;
      cursor: pointer;
    }

    .tabla-contenedor {
      width: 100%;
    }

    #tabla-ejemplo th,
    #tabla-ejemplo td {
      white-space: nowrap;
      /* Evita el salto de línea en las celdas */
    }
  </style>

</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>


  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">

      <!-- # Header Five Layout -->
      <!-- Header Content -->
      <div class="header-content header-style-five position-relative d-flex align-items-center justify-content-between">
        <!-- Logo Wrapper -->
        <div class="logo-wrapper">
          <a href="Suoer_Excep_Calid30.php">
            <img src="img/core-img/Definitivo.png" alt="">
          </a>
        </div>
        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">CUBO DE DATOS
          </h6>
        </div>
        <!-- Navbar Toggler -->
        <div class="navbar--toggler" id="affanNavbarToggler" data-bs-toggle="offcanvas" data-bs-target="#affanOffcanvas"
          aria-controls="affanOffcanvas">
          <span class="d-block"></span>
          <span class="d-block"></span>
          <span class="d-block"></span>
        </div>
      </div>
      <!-- # Header Five Layout End -->

    </div>
  </div>

  <!-- Offcanvas Start -->
  <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1"
    aria-labelledby="affanOffcanvsLabel">

    <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
      aria-label="Close"></button>

    <div class="offcanvas-body p-0">
      <div class="sidenav-wrapper">
        <!-- Sidenav Profile -->
        <div class="sidenav-profile bg-gradient">
          <div class="sidenav-style1"></div>

          <!-- User Thumbnail -->
          <div class="user-profile">
            <img src="img/bg-img/2.jpg" alt="">
          </div>

          <!-- User Info -->
          <!-- User Info -->
          <div class="user-info">
            <h6 class="user-name mb-0">
              <?php echo $_SESSION['usuario'] ?>
            </h6>

          </div>
        </div>

        <!-- Sidenav Nav -->
        <ul class="sidenav-nav ps-0">
          <li>
            <a href=""><i class="bi bi-house-door"></i>HOME</a>
          </li>
          <li>
            <a href="Home_Admin.php"><i class="bi bi-file-person"></i>USUARIOS</a>
          </li>
          <li>
            <a href="#"><i class="bi bi-globe"></i></i>REPORTES WEB</a>
            <ul>
              <li>
                <a
                  href="https://app.powerbi.com/view?r=eyJrIjoiMDI5MjIxOGItZTQ4YS00ODQwLWFjZjItZjVkMjRjOTIxYjlhIiwidCI6ImE0ZDNhYWYwLWJlZjAtNDAzMS1iZGQ3LTM1MzZkYTFmMjQ2ZCJ9"><i
                    class="bi bi-globe"></i>Power BI Produccion</a>
              </li>
              <li>
                <a
                  href="https://app.powerbi.com/view?r=eyJrIjoiNTZhZDZiNTUtZjNlZS00NzY3LThiNzctYTJhZGY0YjI5N2Q5IiwidCI6ImE0ZDNhYWYwLWJlZjAtNDAzMS1iZGQ3LTM1MzZkYTFmMjQ2ZCJ9"><i
                    class="bi bi-globe"></i>Power BI Gestión Soporte</a>
              </li>
            </ul>
          </li>


          <li>
            <div class="night-mode-nav">
              <i class="bi bi-moon"></i>Cambiar a modo OSCURO
              <div class="form-check form-switch">
                <input class="form-check-input form-check-success" id="darkSwitch" type="checkbox">
              </div>
            </div>
          </li>
          <li>
            <a href="login.php"><i class="bi bi-box-arrow-right"></i>Cerrar sesión</a>
          </li>





        </ul>



      </div>
    </div>
  </div>



  <div class="page-content-wrapper py-7">
    <?php
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
      if (isset($_POST['periodo'])) {
        $periodo = $_POST['periodo'];
      } else {
        $periodo = '';
      }

      // Aquí puedes realizar las operaciones o acciones necesarias con los valores capturados.
    }
    ?>

    <?php
    // Imprime el valor de $usuario en una variable JavaScript
    echo '<script>var usuario = "' . $usuario . '";</script>';
    ?>


    <div class="container">
      <div class="card">
        <div class="card-body">
          <form method="post" action="">
            <div class="form-group">
              <label class="form-label" for="exampleSelectMonth">Seleccione el periodo a descargar</label>
              <select class="form-control" id="exampleSelectMonth" name="periodo" onchange="actualizarIframe()">
                <option value="202303">03 Marzo</option>
                <option value="202304">04 Abril</option>
                <option value="202305">05 Mayo</option>
                <option value="202306">06 Junio</option>
                <option value="202307">07 Julio</option>
                <option value="202308" selected>08 Agosto</option>
              </select>
            </div>
          </form>
        </div>
      </div>

    </div>



    <script>


      

      function actualizarIframe() {
        var select = document.getElementById("exampleSelectMonth");
        var periodo = select.value;
        var iframe = document.getElementById("miIframe");
        iframe.src = "Pop2.php?periodo=" + periodo;
      }
    </script>

    <!-- Resto del código... -->
  </div>



  <div class="container">
    <div class="card">
      <div class="card-body">
        <div class="direction-rtl">


<!-- ... Código HTML ... -->

<a id="exportLink2" class="btn m-1 btn-danger" href="Export.php?param=VarCalidad&periodo=" + periodo>
  <i class="bi bi-arrow-down"></i>Base Calidad Reactiva
</a>

<a id="exportLink" class="btn m-1 btn-danger" href="Export.php?param=VARProducNDC&periodo=" + periodo>
  <i class="bi bi-arrow-down"></i>Base Producción NDC
</a>

<a id="exportLink3" class="btn m-1 btn-danger" href="Export.php?param=reversa">
  <i class="bi bi-arrow-down"></i>Base Reversa
</a>

<a id="exportLinkNuevo" class="btn m-1 btn-danger" href="Export.php?param=justificacion">
  <i class="bi bi-arrow-down"></i>Base Justificacion inventario
</a>


<a id="exportLinkNuevo" class="btn m-1 btn-danger" href="mail_PHP2.php">
  <i class="bi bi-arrow-down"></i>Base Justificacion inventario
</a>

<!-- ... Resto del código HTML ... -->




          <a class="btn m-1 btn-danger" onclick="mostrarPopUp()">
            <i class="bi bi-arrow-down"></i>Reporte Comisiones
          </a>



          <div id="miPopUp" class="pop-up">
            <div class="pop-up-contenido">
              <span onclick="cerrarPopUp()" class="cerrar">&times;</span>

              <a id="exportLink4" class="btn m-1 btn-danger"
                href="Export.php?usuario=<?php echo $usuario; ?>&param=KPI">
                <i class="bi bi-arrow-down"></i>Base KPI COMISIONES
              </a>
              <iframe id="miIframe" src="Pop2.php?periodo=" width="4000px" height="700px" frameborder="0"></iframe>
            </div>
          </div>




        </div>
      </div>
    </div>
  </div>


  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <!-- <li>
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li> -->


        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>


  <script>

    function mostrarPopUp() {
      document.getElementById("miPopUp").style.display = "block";
    }

    function cerrarPopUp() {
      document.getElementById("miPopUp").style.display = "none";
    }

  </script>
</body>

</html>