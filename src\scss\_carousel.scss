/* :: Carousel */

.carousel-control-prev-icon {
    background-image: url("data:image/svg+xml,<svg viewBox='0 0 16 16' fill='%23fff' xmlns='http://www.w3.org/2000/svg'><path fill-rule='evenodd' d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>");
    width: 24px;
    height: 24px;
}

.carousel-control-next-icon {
    background-image: url("data:image/svg+xml,<svg viewBox='0 0 16 16' fill='%23fff' xmlns='http://www.w3.org/2000/svg'><path fill-rule='evenodd' d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>");
    width: 24px;
    height: 24px;
}

.carousel-indicators button {
    width: 10px !important;
    height: 10px !important;
    border-radius: 50%;

    &.active {
        background-color: $warning;
    }
}

/* :: Tiny Slider One CSS */

.tiny-slider-one-wrapper {
    position: relative;
    z-index: 1;

    .single-hero-slide {
        position: relative;
        z-index: 1;
        width: 100%;
        height: 220px;
        background-position: center center;
        background-size: cover;

        @media #{$breakpoint-xs-landscape} {
            height: 220px;
        }

        @media #{$breakpoint-sm} {
            height: 240px;
        }

        @media #{$breakpoint-md} {
            height: 300px;
        }

        @media #{$breakpoint-lg} {
            height: 320px;
        }

        @media #{$breakpoint-xl} {
            height: 450px;
        }

        .slide-content {
            position: absolute;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 10;

            p {
                font-size: 14px;
            }
        }
    }

    .tns-controls {
        position: absolute;
        z-index: 100;
        bottom: 1.25rem;
        right: 1.25rem;
        display: flex;
        align-items: center;

        button[data-controls="prev"],
        button[data-controls="next"] {
            outline: none;
            background-color: rgba(255, 255, 255, 0.1);
            width: 26px;
            height: 26px;
            text-align: center;
            color: $white;
            border-radius: 50%;
            transition-duration: 400ms;
            font-size: 12px;
            border: none;

            i {
                line-height: 26px;
            }

            &:hover,
            &:focus {
                background-color: $warning;
                color: $heading;
            }
        }

        button[data-controls="next"] {
            margin-left: 0.5rem;
        }
    }

    .tns-nav {
        position: absolute;
        bottom: 1.375rem;
        left: 1.25rem;
        display: flex;
        align-items: center;

        button {
            outline: none;
            border: none;
            transition-duration: 500ms;
            position: relative;
            z-index: 1;
            margin-right: .375rem;
            width: .5rem;
            height: .5rem;
            flex: 0 0 .5rem;
            max-width: .5rem;
            background-color: $border;
            border-radius: 50%;

            &.tns-nav-active {
                background-color: $warning;
            }
        }
    }
}

/* :: Tiny Slider Two CSS */

.tiny-slider-two-wrapper {
    position: relative;
    z-index: 1;

    .single-hero-slide {
        height: 200px;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        border-radius: 1rem;

        &.bg-overlay::after {
            border-radius: 1rem;
        }
    }

    .tns-nav {
        position: absolute;
        right: 3.625rem;
        bottom: 1.5rem;
        z-index: 10;
        display: flex;
        align-items: center;

        button {
            outline: none;
            background-color: transparent;
            transition-duration: 400ms;
            line-height: 1;
            border: 0;
            opacity: 0;
            visibility: hidden;
            position: absolute;
            z-index: 10;
            right: 0;
            bottom: 0;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
            font-weight: 700;

            &.tns-nav-active {
                color: $warning;
                opacity: 1;
                visibility: visible;
            }
        }
    }

    #totaltnsDotsCount {
        position: absolute;
        z-index: 1;
        bottom: 1.5rem;
        right: 1.5rem;
        color: $warning;
        font-size: 14px;
        font-weight: 700;
        line-height: 1;

        &::before {
            width: 1rem;
            height: 3px;
            background-color: $white;
            position: absolute;
            border-radius: 8px;
            content: '';
            left: -1.25rem;
            z-index: 1;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

/* :: Tiny Slider Three CSS */

.tiny-slider-three-wrapper {
    position: relative;
    z-index: 1;

    .single-hero-slide {
        position: relative;
        z-index: 1;
        transition-duration: 500ms;
        width: 100%;
        padding: 2rem;
        border-radius: 1rem;

        &.bg-overlay::after {
            border-radius: 1rem;
            opacity: .6;
        }
    }
}