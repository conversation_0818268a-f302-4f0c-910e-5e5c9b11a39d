<?php
// Iniciar output buffering para capturar cualquier salida no deseada
ob_start();

// Headers para prevenir caché
header('Content-Type: application/json');
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT");

// Configuración para debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // No mostrar errores directamente al usuario

// Función para registrar errores en archivo de log específico
function log_auth_error($message, $level = 'ERROR') {
    $log_file = dirname(__FILE__) . '/auth_errors.log';
    $date = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $log_message = "[$date] [$level] [$ip] [UA: $user_agent] $message" . PHP_EOL;
    error_log($log_message, 3, $log_file);
}

log_auth_error("Inicio de proceso de autenticación", 'INFO');

try {
    require_once("con_db.php");
    log_auth_error("Archivo con_db.php incluido correctamente", 'INFO');
    
    // Asegurar que la sesión está limpia antes de iniciar
    log_auth_error("Estado de sesión antes: " . session_status(), 'DEBUG');
    
    if (session_status() !== PHP_SESSION_NONE) {
        // Destruir sesión existente si hay una
        log_auth_error("Destruyendo sesión existente", 'INFO');
        session_destroy();
    }
    
    // Iniciar una nueva sesión
    log_auth_error("Iniciando nueva sesión", 'INFO');
    session_start();
    // Regenerar ID de sesión para prevenir session fixation
    session_regenerate_id(true);
    log_auth_error("Sesión iniciada. ID: " . session_id(), 'INFO');
    
    // Verificar que la solicitud sea POST
    log_auth_error("Método de solicitud: " . $_SERVER['REQUEST_METHOD'], 'INFO');
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    }
    
    // Verificar que se recibieron las credenciales
    log_auth_error("Verificando credenciales", 'INFO');
    log_auth_error("POST recibido: " . json_encode(array_keys($_POST)), 'DEBUG');
    
    if (!isset($_POST['rut']) || !isset($_POST['clave'])) {
        throw new Exception("Credenciales incompletas: " . json_encode(array_keys($_POST)));
    }
    
    $rut = $_POST['rut'];
    $clave = $_POST['clave'];
    
    log_auth_error("Credenciales recibidas para: " . $rut, 'INFO');
    
    // Verificar la conexión
    if ($mysqli->connect_error) {
        throw new Exception("Error de conexión: " . $mysqli->connect_error);
    }
    
    log_auth_error("Conexión a base de datos OK", 'INFO');
    
    // Debug: Verificar que la tabla existe
    $check_table = $mysqli->query("SHOW TABLES LIKE 'tb_experian_usuarios'");
    if ($check_table->num_rows === 0) {
        throw new Exception("La tabla 'tb_experian_usuarios' no existe");
    }
    
    log_auth_error("Tabla tb_experian_usuarios existe", 'INFO');
    
    // Consultar la tabla tb_experian_usuarios - INCLUIR PROYECTO para redirección condicional
    $sql = "SELECT id, correo, clave, rol, nombre_usuario, proyecto FROM tb_experian_usuarios WHERE correo = ?";
    log_auth_error("Preparando query: $sql", 'DEBUG');
    
    $stmt = $mysqli->prepare($sql);
    
    if ($stmt === false) {
        throw new Exception("Error en la preparación de la consulta: " . $mysqli->error);
    }
    
    log_auth_error("Query preparada correctamente", 'INFO');
    log_auth_error("Binding parameter: $rut", 'DEBUG');
    
    $stmt->bind_param("s", $rut);
    
    log_auth_error("Ejecutando consulta", 'INFO');
    
    if (!$stmt->execute()) {
        throw new Exception("Error al ejecutar la consulta: " . $stmt->error);
    }
    
    log_auth_error("Consulta ejecutada, procesando resultados", 'INFO');
    
    $stmt->store_result();
    
    log_auth_error("Número de resultados: " . $stmt->num_rows, 'INFO');
    
    if ($stmt->num_rows === 0) {
        log_auth_error("Usuario no encontrado: $rut", 'WARNING');
        // Limpiar cualquier salida no deseada antes de enviar JSON
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => 'Usuario no encontrado',
            'timestamp' => time(),
            'redirectUrl' => 'https://www.gestarservicios.cl/intranet/dist/login.php'
        ]);
        exit;
    }
    
    $stmt->bind_result($db_id, $db_correo, $db_clave, $db_rol, $db_nombre_usuario, $db_proyecto);
    $stmt->fetch();
    
    log_auth_error("Usuario encontrado, rol: $db_rol", 'INFO');
    
    // Verificar credenciales
    if ($clave === $db_clave) {
        // Credenciales correctas
        log_auth_error("Autenticación exitosa para: $rut", 'INFO');

        // Guardar datos en sesión con un token de seguridad
        $_SESSION['usuario'] = $db_correo;
        $_SESSION['usuario_id'] = $db_id;
        $_SESSION['rol'] = $db_rol;
        $_SESSION['nombre_usuario'] = $db_nombre_usuario;
        $_SESSION['proyecto'] = $db_proyecto; // Agregar proyecto a la sesión
        $_SESSION['auth_token'] = bin2hex(random_bytes(32)); // Token de seguridad
        $_SESSION['session_time'] = time(); // Tiempo de inicio de sesión

        // Determinar URL de redirección basada en el proyecto
        $redirectUrl = '';
        if ($db_proyecto === 'experian') {
            // Usuarios legacy de Experian van al formulario actual
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/form_experian2.php';
        } elseif ($db_proyecto === 'inteletGroup') {
            // Usuarios nuevos de InteletGroup van a un formulario específico
            // TODO: Crear o especificar el formulario para InteletGroup
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/form_inteletgroup.php';
        } else {
            // Fallback por defecto (para casos donde proyecto sea NULL o tenga otro valor)
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/form_experian2.php';
        }

        log_auth_error("Datos guardados en sesión. ID sesión: " . session_id(), 'INFO');
        log_auth_error("Usuario $rut con proyecto '$db_proyecto' será redirigido a: $redirectUrl", 'INFO');

        // Limpiar cualquier salida no deseada antes de enviar JSON
        ob_clean();

        echo json_encode([
            'success' => true,
            'rol' => $db_rol,
            'usuario_id' => $db_id,
            'nombre_usuario' => $db_nombre_usuario,
            'proyecto' => $db_proyecto,
            'message' => 'Login exitoso',
            'timestamp' => time(),
            'redirectUrl' => $redirectUrl // Usar la URL determinada por el proyecto
        ]);
    } else {
        // Contraseña incorrecta
        log_auth_error("Contraseña incorrecta para: $rut", 'WARNING');

        // Limpiar cualquier salida no deseada antes de enviar JSON
        ob_clean();

        echo json_encode([
            'success' => false,
            'message' => 'Contraseña incorrecta',
            'timestamp' => time(),
            'redirectUrl' => 'https://www.gestarservicios.cl/intranet/dist/login.php'
        ]);
    }
    
} catch (Exception $e) {
    // Error en la base de datos
    log_auth_error("ERROR CRÍTICO: " . $e->getMessage(), 'ERROR');
    log_auth_error("Trace: " . $e->getTraceAsString(), 'ERROR');

    // Limpiar cualquier salida no deseada antes de enviar JSON
    ob_clean();

    echo json_encode([
        'success' => false,
        'message' => 'Error al procesar la solicitud: ' . $e->getMessage(),
        'timestamp' => time(),
        'redirectUrl' => 'https://www.gestarservicios.cl/intranet/dist/login.php'
    ]);
} finally {
    // Cerrar recursos
    log_auth_error("Cerrando recursos", 'INFO');

    if (isset($stmt) && $stmt !== false) {
        $stmt->close();
        log_auth_error("Statement cerrado", 'DEBUG');
    }

    if (isset($mysqli)) {
        $mysqli->close();
        log_auth_error("Conexión MySQL cerrada", 'DEBUG');
    }

    log_auth_error("Proceso de autenticación finalizado", 'INFO');

    // Finalizar output buffering
    if (ob_get_level()) {
        ob_end_flush();
    }
}
?>