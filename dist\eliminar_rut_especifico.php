<?php
// Script para eliminar un RUT específico de InteletGroup
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    die('Acceso denegado. Solo usuarios de InteletGroup pueden ejecutar este script.');
}

require_once 'con_db.php';
$conexion = $mysqli;

if (!isset($conexion) || $conexion->connect_error) {
    die('Error de conexión a la base de datos');
}

$mensaje = '';
$error = '';

// Procesar eliminación
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar_rut'])) {
    $rut_eliminar = trim($_POST['rut_eliminar']);
    
    if (empty($rut_eliminar)) {
        $error = 'Debe especificar un RUT';
    } else {
        try {
            $conexion->begin_transaction();
            
            // Primero verificar que existe
            $stmt_verificar = $conexion->prepare("SELECT id, razon_social, fecha_registro FROM tb_inteletgroup_prospectos WHERE rut_cliente = ?");
            $stmt_verificar->bind_param("s", $rut_eliminar);
            $stmt_verificar->execute();
            
            $id_encontrado = $razon_social = $fecha_registro = null;
            $stmt_verificar->bind_result($id_encontrado, $razon_social, $fecha_registro);
            
            if ($stmt_verificar->fetch()) {
                $stmt_verificar->close();
                
                // Eliminar de bitácora primero (si existe)
                $stmt_bitacora = $conexion->prepare("DELETE FROM tb_inteletgroup_bitacora WHERE prospecto_id = ?");
                $stmt_bitacora->bind_param("i", $id_encontrado);
                $stmt_bitacora->execute();
                $bitacora_eliminados = $stmt_bitacora->affected_rows;
                $stmt_bitacora->close();
                
                // Eliminar documentos (si existen)
                $stmt_docs = $conexion->prepare("DELETE FROM tb_inteletgroup_documentos WHERE rut_cliente = ?");
                $stmt_docs->bind_param("s", $rut_eliminar);
                $stmt_docs->execute();
                $docs_eliminados = $stmt_docs->affected_rows;
                $stmt_docs->close();
                
                // Eliminar el prospecto
                $stmt_prospecto = $conexion->prepare("DELETE FROM tb_inteletgroup_prospectos WHERE rut_cliente = ?");
                $stmt_prospecto->bind_param("s", $rut_eliminar);
                $stmt_prospecto->execute();
                $prospecto_eliminado = $stmt_prospecto->affected_rows;
                $stmt_prospecto->close();
                
                $conexion->commit();
                
                $mensaje = "RUT $rut_eliminar eliminado exitosamente:<br>";
                $mensaje .= "- Prospecto: $prospecto_eliminado registro(s)<br>";
                $mensaje .= "- Bitácora: $bitacora_eliminados registro(s)<br>";
                $mensaje .= "- Documentos: $docs_eliminados registro(s)<br>";
                $mensaje .= "- Razón Social: $razon_social<br>";
                $mensaje .= "- Fecha original: $fecha_registro";
                
            } else {
                $stmt_verificar->close();
                $conexion->rollback();
                $error = "No se encontró ningún prospecto con el RUT: $rut_eliminar";
            }
            
        } catch (Exception $e) {
            $conexion->rollback();
            $error = 'Error: ' . $e->getMessage();
        }
    }
}

// Obtener información del RUT problemático
$rut_problema = '17121659-1';
$info_problema = null;

$stmt_info = $conexion->prepare("SELECT id, razon_social, nombre_ejecutivo, fecha_registro FROM tb_inteletgroup_prospectos WHERE rut_cliente = ?");
$stmt_info->bind_param("s", $rut_problema);
$stmt_info->execute();

$id_problema = $razon_social_problema = $nombre_ejecutivo_problema = $fecha_registro_problema = null;
$stmt_info->bind_result($id_problema, $razon_social_problema, $nombre_ejecutivo_problema, $fecha_registro_problema);

if ($stmt_info->fetch()) {
    $info_problema = [
        'id' => $id_problema,
        'razon_social' => $razon_social_problema,
        'nombre_ejecutivo' => $nombre_ejecutivo_problema,
        'fecha_registro' => $fecha_registro_problema
    ];
}
$stmt_info->close();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eliminar RUT Específico - InteletGroup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Eliminar RUT Específico - InteletGroup
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($mensaje): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i>
                                <?php echo $mensaje; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-circle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="alert alert-warning">
                            <strong>¡ATENCIÓN!</strong> Este script eliminará permanentemente el RUT especificado y todos sus datos relacionados.
                        </div>
                        
                        <?php if ($info_problema): ?>
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">RUT Problemático Detectado: <?php echo $rut_problema; ?></h5>
                            </div>
                            <div class="card-body">
                                <p><strong>ID:</strong> <?php echo $info_problema['id']; ?></p>
                                <p><strong>Razón Social:</strong> <?php echo htmlspecialchars($info_problema['razon_social']); ?></p>
                                <p><strong>Ejecutivo:</strong> <?php echo htmlspecialchars($info_problema['nombre_ejecutivo']); ?></p>
                                <p><strong>Fecha Registro:</strong> <?php echo $info_problema['fecha_registro']; ?></p>
                                
                                <form method="POST" onsubmit="return confirm('¿Está seguro de que desea eliminar este RUT y todos sus datos relacionados?');">
                                    <input type="hidden" name="rut_eliminar" value="<?php echo $rut_problema; ?>">
                                    <button type="submit" name="eliminar_rut" class="btn btn-danger">
                                        <i class="bi bi-trash me-2"></i>
                                        Eliminar RUT <?php echo $rut_problema; ?>
                                    </button>
                                </form>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <form method="POST" onsubmit="return confirm('¿Está seguro de que desea eliminar este RUT?');">
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="rut_eliminar" class="form-label">RUT a eliminar:</label>
                                    <input type="text" name="rut_eliminar" id="rut_eliminar" class="form-control" 
                                           placeholder="12345678-9" pattern="^\d{7,8}-[\dkK]$" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" name="eliminar_rut" class="btn btn-danger w-100">
                                        <i class="bi bi-trash me-2"></i>
                                        Eliminar
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="form_inteletgroup.php" class="btn btn-primary me-2">
                                <i class="bi bi-arrow-left me-2"></i>
                                Volver al Panel
                            </a>
                            <a href="limpiar_datos_prueba.php" class="btn btn-warning">
                                <i class="bi bi-tools me-2"></i>
                                Herramientas de Limpieza
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
