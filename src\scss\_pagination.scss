/* :: Pagination */

.page-link {
    padding: .25rem .75rem;
    font-weight: 500;
    font-size: 14px;
}

.pagination {
    &.pagination-one {

        .page-link {
            border: 1px solid $border;
            border-left: 0;
            border-right: 0;

            &:focus {
                box-shadow: none !important;
            }
        }

        .page-item:first-child .page-link {
            border-left: 1px solid $border;
        }

        .page-item:last-child .page-link {
            border-right: 1px solid $border;
        }

        .page-item.active .page-link {
            position: relative;
            z-index: 1;
            color: $white;
            background-color: transparent;

            &::after {
                position: absolute;
                width: 22px;
                height: 22px;
                content: "";
                background-color: $primary;
                z-index: -2;
                top: 50%;
                left: 50%;
                border-radius: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }

    &.pagination-two {

        .page-link {
            border: 0;

            &:focus {
                box-shadow: none !important;
            }
        }

        .page-item.active .page-link {
            position: relative;
            z-index: 1;
            color: $white;
            background-color: transparent;

            &::after {
                position: absolute;
                width: 22px;
                height: 22px;
                content: "";
                background-color: $primary;
                z-index: -2;
                top: 50%;
                left: 50%;
                border-radius: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }

    &.pagination-three {

        .page-item:not(:first-child) .page-link {
            margin-left: 0;
        }

        .page-link {
            border: 0;
            background-color: $primary;
            border-radius: 0;
            color: $white;

            &:hover,
            &:focus {
                box-shadow: none !important;
                background-color: $primary;
                border: 0;
            }
        }

        .page-item:first-child .page-link {
            border-top-left-radius: .25rem;
            border-bottom-left-radius: .25rem;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-item:last-child .page-link {
            border-top-right-radius: .25rem;
            border-bottom-right-radius: .25rem;
            border-left: 1px solid rgba(255, 255, 255, 0.2);
        }
    }
}