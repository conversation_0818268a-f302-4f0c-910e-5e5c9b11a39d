<?php
/**
 * Script para verificar los límites de subida de archivos en el servidor
 *
 * Este script muestra información sobre los límites de configuración de PHP
 * relacionados con la subida de archivos y el espacio en disco disponible.
 */

// Función para convertir bytes a un formato legible
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= (1 << (10 * $pow));

    return round($bytes, $precision) . ' ' . $units[$pow];
}

// Función para obtener el espacio en disco disponible
function getDiskSpace() {
    $diskTotal = disk_total_space(".");
    $diskFree = disk_free_space(".");
    $diskUsed = $diskTotal - $diskFree;

    return [
        'total' => formatBytes($diskTotal),
        'free' => formatBytes($diskFree),
        'used' => formatBytes($diskUsed),
        'percent_used' => round(($diskUsed / $diskTotal) * 100, 2) . '%'
    ];
}

// Obtener límites de PHP
$uploadMaxFilesize = ini_get('upload_max_filesize');
$postMaxSize = ini_get('post_max_size');
$memoryLimit = ini_get('memory_limit');
$maxExecutionTime = ini_get('max_execution_time');
$maxInputTime = ini_get('max_input_time');

// Convertir a bytes para comparación
function returnBytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;

    switch($last) {
        case 'g': $val *= 1024;
        case 'm': $val *= 1024;
        case 'k': $val *= 1024;
    }

    return $val;
}

$uploadMaxFilesizeBytes = returnBytes($uploadMaxFilesize);
$postMaxSizeBytes = returnBytes($postMaxSize);
$memoryLimitBytes = returnBytes($memoryLimit);

// Determinar el límite efectivo (el más pequeño)
$effectiveLimit = min(
    $uploadMaxFilesizeBytes,
    $postMaxSizeBytes,
    $memoryLimitBytes > 0 ? $memoryLimitBytes : PHP_INT_MAX
);

// Obtener información del espacio en disco
$diskSpace = getDiskSpace();

// Obtener información del servidor
$serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? 'Desconocido';
$phpVersion = PHP_VERSION;

// Mostrar resultados con HTML y CSS para mejor presentación
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Límites de Subida de Archivos (64M/32M)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .highlight {
            background-color: #e8f4f8;
            font-weight: bold;
        }
        .warning {
            color: #e74c3c;
            font-weight: bold;
        }
        .info {
            background-color: #d9edf7;
            border: 1px solid #bce8f1;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            color: #31708f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Análisis de Límites de Subida de Archivos (64M/32M)</h1>

        <div class="info">
            <p>Este script analiza la configuración de PHP y el espacio en disco para determinar el tamaño máximo de archivos que puede manejar este servidor.</p>
            <p><strong>Configuración actual:</strong> upload_max_filesize = 64M, post_max_size = 32M</p>
        </div>

        <h2>Información del Servidor</h2>
        <table>
            <tr>
                <th>Parámetro</th>
                <th>Valor</th>
            </tr>
            <tr>
                <td>Software del Servidor</td>
                <td><?php echo htmlspecialchars($serverSoftware); ?></td>
            </tr>
            <tr>
                <td>Versión de PHP</td>
                <td><?php echo htmlspecialchars($phpVersion); ?></td>
            </tr>
        </table>

        <h2>Límites de Configuración de PHP</h2>
        <table>
            <tr>
                <th>Parámetro</th>
                <th>Valor</th>
                <th>Descripción</th>
            </tr>
            <tr <?php echo ($effectiveLimit == $uploadMaxFilesizeBytes) ? 'class="highlight"' : ''; ?>>
                <td>upload_max_filesize</td>
                <td><?php echo $uploadMaxFilesize; ?> (<?php echo formatBytes($uploadMaxFilesizeBytes); ?>)</td>
                <td>Tamaño máximo permitido para archivos subidos</td>
            </tr>
            <tr <?php echo ($effectiveLimit == $postMaxSizeBytes) ? 'class="highlight"' : ''; ?>>
                <td>post_max_size</td>
                <td><?php echo $postMaxSize; ?> (<?php echo formatBytes($postMaxSizeBytes); ?>)</td>
                <td>Tamaño máximo de datos POST permitidos</td>
            </tr>
            <tr <?php echo ($effectiveLimit == $memoryLimitBytes && $memoryLimitBytes > 0) ? 'class="highlight"' : ''; ?>>
                <td>memory_limit</td>
                <td><?php echo $memoryLimit; ?> (<?php echo formatBytes($memoryLimitBytes); ?>)</td>
                <td>Memoria máxima que un script puede consumir</td>
            </tr>
            <tr>
                <td>max_execution_time</td>
                <td><?php echo $maxExecutionTime; ?> segundos</td>
                <td>Tiempo máximo que un script puede ejecutarse</td>
            </tr>
            <tr>
                <td>max_input_time</td>
                <td><?php echo $maxInputTime; ?> segundos</td>
                <td>Tiempo máximo que un script puede pasar analizando datos de entrada</td>
            </tr>
        </table>

        <h2>Espacio en Disco</h2>
        <table>
            <tr>
                <th>Parámetro</th>
                <th>Valor</th>
            </tr>
            <tr>
                <td>Espacio Total</td>
                <td><?php echo $diskSpace['total']; ?></td>
            </tr>
            <tr>
                <td>Espacio Libre</td>
                <td><?php echo $diskSpace['free']; ?></td>
            </tr>
            <tr>
                <td>Espacio Usado</td>
                <td><?php echo $diskSpace['used']; ?> (<?php echo $diskSpace['percent_used']; ?>)</td>
            </tr>
        </table>

        <h2>Conclusión</h2>
        <div class="info">
            <p><strong>Límite efectivo para subida de archivos: <?php echo formatBytes($effectiveLimit); ?></strong></p>
            <p>Este es el límite más restrictivo entre upload_max_filesize, post_max_size y memory_limit.</p>

            <?php if ($effectiveLimit == $uploadMaxFilesizeBytes): ?>
            <p>El límite actual está determinado por <strong>upload_max_filesize</strong>.</p>
            <?php elseif ($effectiveLimit == $postMaxSizeBytes): ?>
            <p>El límite actual está determinado por <strong>post_max_size</strong>.</p>
            <?php elseif ($effectiveLimit == $memoryLimitBytes): ?>
            <p>El límite actual está determinado por <strong>memory_limit</strong>.</p>
            <?php endif; ?>

            <?php if ($maxExecutionTime < 60): ?>
            <p class="warning">Nota: El tiempo máximo de ejecución (<?php echo $maxExecutionTime; ?> segundos) podría ser insuficiente para subir archivos grandes.</p>
            <?php endif; ?>
        </div>

        <h2>Recomendaciones</h2>
        <div class="info">
            <p>Los límites actuales configurados en el servidor son:</p>
            <pre>
upload_max_filesize = 64M
post_max_size = 32M
memory_limit = <?php echo $memoryLimit; ?>
max_execution_time = <?php echo $maxExecutionTime; ?>
max_input_time = <?php echo $maxInputTime; ?>
            </pre>
            <p>Si necesita aumentar estos límites, puede modificarlos desde la sección "Selector de PHP" o "MultiPHP INI Editor" en cPanel.</p>
        </div>
    </div>
</body>
</html>
