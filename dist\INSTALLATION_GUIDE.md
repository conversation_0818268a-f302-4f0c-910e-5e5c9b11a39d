# 📋 Guía de Instalación - Sistema de Registro de Prospectos

## 🎯 Resumen del Proyecto

Este sistema implementa un formulario modal completo para el registro de prospectos con las siguientes características:

- ✅ Formulario modal con validación completa del lado del cliente
- ✅ Backend seguro con validación de datos y manejo de errores
- ✅ Sistema de subida de archivos con validación de tipos y tamaños
- ✅ Notificaciones por email con plantillas HTML
- ✅ Integración con sistema de autenticación existente
- ✅ Gestión de usuarios con roles y proyectos
- ✅ Mejoras de seguridad en autenticación

## 🚀 Instalación Paso a Paso

### Paso 1: Actualizar Esquema de Base de Datos

```bash
# 1. Actualizar tabla de usuarios
php update_usuarios_schema.php
```

```bash
# 2. Crear usuarios de InteletGroup
php create_new_users.php
```

```bash
# 3. Actualizar tabla de prospectos
mysql -u usuario -p gestarse_experian < create_prospect_table.sql
```

```bash
# 4. Crear tablas de seguridad (opcional)
mysql -u usuario -p gestarse_experian < create_security_tables.sql
```

### Paso 2: Configurar Archivos

#### A. Configuración de Email
Editar `dist/email_config.php`:

```php
// Cambiar estas configuraciones por valores reales
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'tu-contraseña-de-aplicacion');
define('TO_EMAIL', '<EMAIL>');
```

#### B. Permisos de Directorio
```bash
# Crear y configurar directorio de uploads
mkdir -p dist/uploads/prospectos/
chmod 755 dist/uploads/prospectos/
```

### Paso 3: Integración con Sistema Existente

#### Opción A: Integración Completa
Usar el archivo `dist/prospect_integration.php` como página independiente.

#### Opción B: Integración en form_experian2.php
Agregar al final del `<head>`:

```html
<!-- CSS del formulario de prospectos -->
<link href="css/prospect-form.css" rel="stylesheet">
```

Agregar antes del cierre del `<body>`:

```html
<!-- Modal del formulario -->
<?php include 'prospect_form_modal.html'; ?>

<!-- JavaScript del formulario -->
<script src="js/prospect-form.js"></script>
<script>
    window.currentUserName = '<?php echo addslashes($_SESSION['nombre_usuario'] ?? 'Usuario'); ?>';
    window.currentUserId = <?php echo $_SESSION['usuario_id'] ?? 0; ?>;
</script>
```

Agregar botón donde sea necesario:

```html
<button type="button" class="btn btn-success" onclick="abrirModalProspecto()">
    <i class="fas fa-plus"></i> Nuevo Prospecto
</button>
```

### Paso 4: Verificación del Sistema

```bash
# Ejecutar script de pruebas
php test_prospect_system.php
```

El script debe mostrar una tasa de éxito >= 90% para considerar el sistema listo.

## 📁 Estructura de Archivos Creados

```
dist/
├── guardar_prospecto_nuevo.php      # Backend para guardar prospectos
├── prospect_form_modal.html         # Modal HTML del formulario
├── prospect_integration.php         # Página de integración completa
├── email_config.php                 # Configuración de emails
├── upload_manager.php               # Gestor de archivos
├── css/
│   └── prospect-form.css            # Estilos del formulario
├── js/
│   └── prospect-form.js             # JavaScript del formulario
├── endpoints/
│   └── get_user_info.php            # Endpoint para info de usuario
└── uploads/
    └── prospectos/                  # Directorio de archivos subidos

# Archivos de configuración
update_usuarios_schema.php           # Script actualización usuarios
update_usuarios_schema.sql          # SQL actualización usuarios
create_new_users.php                # Script creación usuarios
create_prospect_table.sql           # SQL tabla prospectos
create_security_tables.sql          # SQL tablas seguridad
ControllerGestar_Secure.php         # Controlador seguro (opcional)
security_review_report.md           # Reporte de seguridad
test_prospect_system.php            # Script de pruebas
```

## 🔧 Configuración Avanzada

### Seguridad Mejorada (Opcional)

Para implementar el sistema de autenticación mejorado:

1. Hacer backup del `ControllerGestar.php` actual
2. Reemplazar con `ControllerGestar_Secure.php`
3. Ejecutar `create_security_tables.sql`
4. Migrar contraseñas existentes a hash

### Configuración de Email con PHPMailer

Para mejor confiabilidad en emails:

```bash
# Instalar PHPMailer via Composer
composer require phpmailer/phpmailer
```

### Configuración de Logs

Crear directorio de logs:

```bash
mkdir -p logs/
chmod 755 logs/
```

## 🧪 Pruebas y Validación

### Pruebas Manuales

1. **Autenticación**: Verificar login con usuarios creados
2. **Formulario**: Abrir modal y validar campos
3. **Validación**: Probar con datos inválidos
4. **Subida**: Probar subida de archivos
5. **Email**: Verificar recepción de notificaciones
6. **Tabla**: Verificar actualización de datos

### Datos de Prueba

Usar el botón "Llenar Test" en el formulario para datos de prueba automáticos.

### Usuarios Creados

| Nombre | Email | Rol | Clave Temporal |
|--------|-------|-----|----------------|
| CATHERINE DEL CARMEN PINCHEIRA BRITO | <EMAIL> | admin | Temp2024!1330 |
| VICTOR ALFONSO PACHECO PACHECO | <EMAIL> | ejecutivos | Temp2024!1819 |
| CARLOS ANDRÉS MURILLO RESTREPO | <EMAIL> | ejecutivos | Temp2024!2592 |
| JOHANNA LISSETE RIGO ESPINOZA | <EMAIL> | ejecutivos | Temp2024!1268 |
| TANIA ELIZABETH MUÑOZ PIZARRO | <EMAIL> | ejecutivos | Temp2024!1652 |

## 🔒 Consideraciones de Seguridad

1. **Contraseñas**: Cambiar claves temporales en primer acceso
2. **Archivos**: Validar tipos y tamaños de archivos subidos
3. **Inputs**: Toda entrada está sanitizada y validada
4. **Sesiones**: Verificación de autenticación en todos los endpoints
5. **Logs**: Monitorear logs de errores y actividad

## 🐛 Solución de Problemas

### Error: "Usuario no autenticado"
- Verificar que la sesión esté iniciada
- Comprobar que `$_SESSION['usuario_id']` existe

### Error: "Tabla no existe"
- Ejecutar scripts SQL de creación de tablas
- Verificar permisos de base de datos

### Error: "No se puede subir archivo"
- Verificar permisos del directorio uploads
- Comprobar configuración PHP (upload_max_filesize)

### Error: "Email no enviado"
- Configurar credenciales SMTP correctas
- Verificar conectividad de red

## 📞 Soporte

Para problemas o dudas:

1. Revisar logs en `dist/auth_errors.log`
2. Ejecutar `test_prospect_system.php`
3. Verificar configuración de email
4. Comprobar permisos de archivos

## 🎉 ¡Sistema Listo!

Una vez completados todos los pasos, el sistema estará listo para:

- ✅ Registro de nuevos prospectos
- ✅ Validación completa de datos
- ✅ Subida de documentación
- ✅ Notificaciones automáticas
- ✅ Integración con bitácora existente
- ✅ Gestión de usuarios por proyecto
