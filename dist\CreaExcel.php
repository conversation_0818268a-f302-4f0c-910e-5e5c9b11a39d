<?php

session_start();



header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.


$inc = include("con_db.php");


$output = '';


$result = $conex->query(
    "   select Nombre_Bucket, EPS, Cod_vend, Asignado, Tecnico, Zona_trabajo
    , Franja, Orden_Trabajo, Tipo_Actividad, Subtipo, Tipo_Orden, Estado
    , Codigo_Cierre, RUT, nro_orden, gestion_tqw, OBSERVACION, id, fecha_tqw
    , gest_comercial, obs_comercial, fecha_comercial, fecha, send_email  
    FROM ANDES_AvanceTOA
    "
      
    );

    $output .= '
    <table class="table" bordered="1">  
                     <tr>  
                          <th>Orden_Trabajo</th>
                          <th>Numero_Cliente</th>
                          <th>Nombre_Bucket</th>  
                          <th>EPS</th>  
                          <th>Cod_vend</th>  
                          <th>GestionTQW</th>  
                          <th>FechaTQW</th>  
                          <th>GestionComercial</th>  
                          <th>FechaComercial</th>  
                        <th>Asignado</th>
                        <th>Tecnico</th>
                        <th>Zona_trabajo</th>
                        <th>Franja</th>                       
                        <th>Tipo_Actividad</th>
                        <th>Subtipo</th>
                        <th>Tipo_Orden</th>
                        <th>Estado</th>
                        <th>Codigo_Cierre</th>
                        
 
                     </tr> 
   ';


   
  while($row = mysqli_fetch_array($result))
  {

 
   $output .= '
    <tr>  
          <td>'.$row["Orden_Trabajo"].'</td>
          <td>'.$row["RUT"].'</td>
          <td>'.$row["Nombre_Bucket"].'</td>  
          <td>'.$row["EPS"].'</td>  
          <td>'.$row["Cod_vend"].'</td> 
          <td>'.$row["gestion_tqw"].'</td> 
          <td>'.$row["fecha_tqw"].'</td> 
          <td>'.$row["gest_comercial"].'</td> 
          <td>'.$row["fecha_comercial"].'</td>  
          <td>'.$row["Asignado"].'</td>  
          <td>'.$row["Tecnico"].'</td>
          <td>'.$row["Zona_trabajo"].'</td>
          <td>'.$row["Franja"].'</td>          
          <td>'.$row["Tipo_Actividad"].'</td>
          <td>'.$row["Subtipo"].'</td>
          <td>'.$row["Tipo_Orden"].'</td>
          <td>'.$row["Estado"].'</td>
          <td>'.$row["Codigo_Cierre"].'</td>
          


                    </tr>
   ';
  }

  $output .= '</table>';
  header('Content-Type: application/xls');
  header('Content-Disposition: attachment; filename=reiterado_andes.xls');
  echo $output;