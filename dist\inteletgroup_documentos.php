<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Aplicar headers anti-caché
no_cache_headers();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    $errorMsg = urlencode("Usuario no autenticado o sin permisos para InteletGroup.");
    header('Location: ' . version_url('login.php?error=' . $errorMsg));
    exit;
}

// Incluir conexión a la base de datos
error_log("INTELETGROUP_DOCUMENTOS: Incluyendo con_db.php");
require_once 'con_db.php';
error_log("INTELETGROUP_DOCUMENTOS: con_db.php incluido exitosamente");

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = $_SESSION['nombre_usuario'] ?? 'Usuario';
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';

error_log("INTELETGROUP_DOCUMENTOS: Usuario ID: $usuario_id, Nombre: $nombre_usuario");

// Verificar si la variable mysqli está definida
error_log("INTELETGROUP_DOCUMENTOS: Verificando variable mysqli - isset: " . (isset($mysqli) ? 'true' : 'false'));

// Usar la conexión mysqli del archivo con_db.php
if (!isset($mysqli)) {
    error_log("Error crítico: Variable mysqli no definida en con_db.php");
    die("Error de conexión a la base de datos. Variable mysqli no definida. Por favor, contacte al administrador.");
}

error_log("INTELETGROUP_DOCUMENTOS: mysqli definida, verificando connect_error");

if ($mysqli->connect_error) {
    error_log("Error crítico: Error de conexión MySQL: " . $mysqli->connect_error);
    die("Error de conexión a la base de datos. Error de conexión: " . $mysqli->connect_error);
}

$conexion = $mysqli;
error_log("INTELETGROUP_DOCUMENTOS: Conexión establecida exitosamente");

// Obtener todos los prospectos del usuario
$prospectos_usuario = [];
try {
    // Verificar que la tabla existe
    $check_table = $conexion->query("SHOW TABLES LIKE 'tb_inteletgroup_prospectos'");
    if ($check_table->num_rows == 0) {
        error_log("Tabla tb_inteletgroup_prospectos no existe");
        $prospectos_usuario = [];
    } else {
        $stmt_prospectos = $conexion->prepare("
            SELECT p.*, 
                   COALESCE((SELECT COUNT(*) FROM tb_inteletgroup_documentos d 
                            WHERE d.prospecto_id = p.id AND d.estado = 'Activo'), 0) as total_documentos
            FROM tb_inteletgroup_prospectos p 
            WHERE p.usuario_id = ? AND p.estado = 'Activo' 
            ORDER BY p.fecha_registro DESC
        ");
        
        if ($stmt_prospectos) {
            $stmt_prospectos->bind_param("i", $usuario_id);
            $stmt_prospectos->execute();
            $result_prospectos = $stmt_prospectos->get_result();

            while ($prospecto = $result_prospectos->fetch_assoc()) {
                $prospectos_usuario[] = $prospecto;
            }
            $stmt_prospectos->close();
        } else {
            error_log("Error al preparar consulta de prospectos: " . $conexion->error);
            $prospectos_usuario = [];
        }
    }
} catch (Exception $e) {
    error_log("Error obteniendo prospectos: " . $e->getMessage());
    $prospectos_usuario = [];
}

// Procesar búsqueda por RUT
$rut_busqueda = '';
$prospecto_info = null;
$documentos = [];
$mensaje = '';

if (isset($_POST['buscar_rut']) && !empty($_POST['rut_cliente'])) {
    $rut_busqueda = trim($_POST['rut_cliente']);
    
    // Buscar prospecto por RUT
    $stmt = $conexion->prepare("
        SELECT p.*, u.nombre_usuario as ejecutivo_nombre 
        FROM tb_inteletgroup_prospectos p 
        LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id 
        WHERE p.rut_cliente = ? AND p.estado = 'Activo'
    ");
    $stmt->bind_param("s", $rut_busqueda);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $prospecto_info = $result->fetch_assoc();
        
        // Obtener documentos del prospecto
        $stmt_docs = $conexion->prepare("
            SELECT * FROM tb_inteletgroup_documentos 
            WHERE rut_cliente = ? AND estado = 'Activo' 
            ORDER BY fecha_subida DESC
        ");
        $stmt_docs->bind_param("s", $rut_busqueda);
        $stmt_docs->execute();
        $result_docs = $stmt_docs->get_result();
        
        while ($doc = $result_docs->fetch_assoc()) {
            $documentos[] = $doc;
        }
    } else {
        $mensaje = "No se encontró ningún prospecto con el RUT: $rut_busqueda";
    }
}

// Procesar subida de nuevos documentos
if (isset($_POST['subir_documento']) && !empty($_POST['rut_cliente_doc'])) {
    $rut_cliente = trim($_POST['rut_cliente_doc']);
    
    // Verificar que el prospecto existe
    $stmt = $conexion->prepare("SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? AND estado = 'Activo'");
    $stmt->bind_param("s", $rut_cliente);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $prospecto = $result->fetch_assoc();
        $prospecto_id = $prospecto['id'];
        
        if (isset($_FILES['nuevo_documento']) && $_FILES['nuevo_documento']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/inteletgroup_prospectos/';
            
            // Crear directorio si no existe
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_name = $_FILES['nuevo_documento']['name'];
            $file_type = $_FILES['nuevo_documento']['type'];
            $file_size = $_FILES['nuevo_documento']['size'];
            $file_tmp = $_FILES['nuevo_documento']['tmp_name'];
            
            $allowed_types = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'image/jpeg',
                'image/jpg', 
                'image/png',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];
            
            $max_size = 5 * 1024 * 1024; // 5MB
            
            if (in_array($file_type, $allowed_types) && $file_size <= $max_size) {
                $extension = pathinfo($file_name, PATHINFO_EXTENSION);
                $unique_name = $rut_cliente . '_' . time() . '.' . $extension;
                $file_path = $upload_dir . $unique_name;
                
                if (move_uploaded_file($file_tmp, $file_path)) {
                    // Insertar documento en la base de datos
                    $stmt_insert = $conexion->prepare("
                        INSERT INTO tb_inteletgroup_documentos (
                            prospecto_id, usuario_id, rut_cliente, nombre_archivo, 
                            nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt_insert->bind_param("iissssis", 
                        $prospecto_id, $usuario_id, $rut_cliente, $unique_name,
                        $file_name, $file_type, $file_size, $file_path
                    );
                    
                    if ($stmt_insert->execute()) {
                        // Registrar en bitácora
                        $stmt_bitacora = $conexion->prepare("
                            INSERT INTO tb_inteletgroup_prospecto_bitacora (
                                prospecto_id, usuario_id, accion, descripcion, ip_address, user_agent
                            ) VALUES (?, ?, 'Subir Documento', ?, ?, ?)
                        ");
                        $descripcion = "Documento subido: $file_name";
                        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
                        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
                        
                        $stmt_bitacora->bind_param("iisss", $prospecto_id, $usuario_id, $descripcion, $ip_address, $user_agent);
                        $stmt_bitacora->execute();
                        
                        $mensaje = "Documento subido exitosamente";
                        
                        // Recargar documentos
                        $rut_busqueda = $rut_cliente;
                        $_POST['rut_cliente'] = $rut_cliente;
                        $_POST['buscar_rut'] = true;
                        
                        // Re-ejecutar búsqueda para actualizar la vista
                        $stmt = $conexion->prepare("
                            SELECT p.*, u.nombre_usuario as ejecutivo_nombre 
                            FROM tb_inteletgroup_prospectos p 
                            LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id 
                            WHERE p.rut_cliente = ? AND p.estado = 'Activo'
                        ");
                        $stmt->bind_param("s", $rut_busqueda);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        $prospecto_info = $result->fetch_assoc();
                        
                        $documentos = [];
                        $stmt_docs = $conexion->prepare("
                            SELECT * FROM tb_inteletgroup_documentos 
                            WHERE rut_cliente = ? AND estado = 'Activo' 
                            ORDER BY fecha_subida DESC
                        ");
                        $stmt_docs->bind_param("s", $rut_busqueda);
                        $stmt_docs->execute();
                        $result_docs = $stmt_docs->get_result();
                        
                        while ($doc = $result_docs->fetch_assoc()) {
                            $documentos[] = $doc;
                        }
                    } else {
                        $mensaje = "Error al guardar el documento en la base de datos";
                    }
                } else {
                    $mensaje = "Error al subir el archivo";
                }
            } else {
                $mensaje = "Tipo de archivo no permitido o archivo muy grande";
            }
        } else {
            $mensaje = "No se seleccionó ningún archivo";
        }
    } else {
        $mensaje = "No se encontró el prospecto con el RUT especificado";
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InteletGroup - Gestión de Documentos</title>
    <?php echo no_cache_meta(); ?>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <style>
        /* Variables de color mejoradas para mejor contraste y armonía */
        :root {
            /* Colores principales - Paleta cohesiva */
            --primary-blue: #2699FB;
            --primary-dark: #1e3a8a;
            --primary-medium: #3b82f6;
            --primary-light: #60a5fa;

            /* Header con gradiente profesional */
            --header-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #2563eb 100%);
            --header-overlay: rgba(30, 58, 138, 0.95);

            /* Colores de estado */
            --success-color: #10b981;
            --success-light: #34d399;
            --info-color: #06b6d4;
            --info-light: #22d3ee;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --secondary-color: #6b7280;
            --secondary-light: #9ca3af;

            /* Colores neutros */
            --light-color: #f8fafc;
            --light-gray: #f1f5f9;
            --medium-gray: #e2e8f0;
            --dark-color: #1f2937;
            --dark-medium: #374151;
            --body-bg: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

            /* Efectos y sombras */
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s ease-out;
        }
        
        /* Estilos base del body */
        body {
            background: var(--body-bg);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--dark-color);
            line-height: 1.6;
        }

        /* Header profesional con gradiente mejorado - Override existing styles */
        .simple-header, .inteletgroup-header, .inteletgroup-header-override, .site-header {
            background: var(--header-gradient) !important;
            color: white !important;
            box-shadow: var(--box-shadow-lg) !important;
            margin-bottom: 2rem !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 1000 !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            padding: 0 !important;
            backdrop-filter: blur(10px) !important;
            height: auto !important;
        }

        /* Overlay para mejorar contraste */
        .simple-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--header-overlay);
            z-index: -1;
        }

        /* Contenedor principal del header - Override existing styles */
        .header-container {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            padding: 1rem 0 !important;
            position: relative !important;
            z-index: 1 !important;
            max-width: none !important;
            margin: 0 auto !important;
            height: auto !important;
        }

        /* Logo y nombre de la empresa */
        .brand-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Logo wrapper mejorado */
        .logo-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .logo-wrapper:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }

        .logo-wrapper img {
            height: 28px;
            width: 28px;
            object-fit: contain;
            border-radius: 50%;
        }

        /* Estilos para la información del sitio */
        .site-info {
            display: flex;
            flex-direction: column;
        }

        .site-title {
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            margin: 0 !important;
            padding: 0 !important;
            color: white !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            letter-spacing: -0.025em !important;
        }

        .site-subtitle {
            font-size: 0.875rem !important;
            opacity: 0.9 !important;
            margin: 0 !important;
            padding: 0 !important;
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500 !important;
        }

        /* Usuario y acciones */
        .user-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Estilos para el nombre de usuario */
        .user-info-container {
            text-align: right;
            line-height: 1.3;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .user-name {
            font-size: 1rem;
            font-weight: 600;
            color: white;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .user-role {
            font-size: 0.8rem;
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-weight: 500;
        }

        /* Botones de navegación mejorados */
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.6rem 1rem;
            font-size: 0.9rem;
            border-radius: 25px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            backdrop-filter: blur(10px);
            gap: 0.5rem;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .nav-btn:active {
            transform: scale(0.95);
        }
        
        /* Cards modernos */
        .modern-card {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .modern-card:hover {
            box-shadow: var(--box-shadow-lg);
            transform: translateY(-2px);
        }

        .search-card {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(52, 211, 153, 0.05));
            border-left: 4px solid var(--success-color);
        }
        
        .info-card {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.05), rgba(34, 211, 238, 0.05));
            border-left: 4px solid var(--info-color);
        }
        
        /* Tabla de prospectos moderna */
        .prospects-table {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .prospects-table .table {
            margin-bottom: 0;
        }

        .prospects-table th {
            background: var(--header-gradient);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem 0.75rem;
        }

        .prospects-table td {
            padding: 0.75rem;
            border-color: var(--medium-gray);
            vertical-align: middle;
        }

        .prospects-table tbody tr:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        .badge-documents {
            background: var(--info-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
        }

        .document-item {
            background: white;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-sm);
            padding: 1rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }
        
        .document-item:hover {
            box-shadow: var(--box-shadow);
            transform: translateY(-2px);
        }
        
        .file-icon {
            font-size: 2rem;
            margin-right: 1rem;
        }
        
        .upload-area {
            border: 2px dashed var(--primary-medium);
            border-radius: var(--border-radius-sm);
            padding: 2rem;
            text-align: center;
            background: rgba(59, 130, 246, 0.05);
            transition: var(--transition);
        }

        .upload-area:hover {
            border-color: var(--primary-dark);
            background: rgba(59, 130, 246, 0.1);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .header-container {
                padding: 0.75rem 0;
            }

            .brand-section {
                gap: 0.75rem;
            }

            .site-title {
                font-size: 1.25rem;
            }

            .site-subtitle {
                font-size: 0.8rem;
            }

            .user-info-container {
                padding: 0.5rem 0.75rem;
            }

            .user-name {
                font-size: 0.9rem;
            }

            .user-role {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 576px) {
            .user-info-container {
                display: none;
            }

            .site-title {
                font-size: 1.1rem;
            }

            .site-subtitle {
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header profesional inspirado en form_inteletgroup.php -->
    <header class="simple-header inteletgroup-header-override">
        <div class="container">
            <div class="header-container">
                <!-- Logo y nombre del sitio -->
                <div class="brand-section">
                    <div class="logo-wrapper">
                        <img src="img/icons/logo_intelet.jpg" alt="Logo InteletGroup"
                             onerror="this.innerHTML='<i class=\'bi bi-building\' style=\'font-size: 1.5rem; color: white;\'></i>'">
                    </div>
                    <div class="site-info">
                        <h1 class="site-title">InteletGroup</h1>
                        <span class="site-subtitle">Gestión de Documentos</span>
                    </div>
                </div>
                
                <!-- Usuario y acciones -->
                <div class="user-section">
                    <div class="user-info-container">
                        <div class="user-name"><?php echo htmlspecialchars($nombre_usuario); ?></div>
                        <div class="user-role"><?php echo htmlspecialchars($proyecto); ?></div>
                    </div>
                    <a href="form_inteletgroup.php" class="nav-btn" title="Volver al Panel">
                        <i class="bi bi-arrow-left"></i> Volver
                    </a>
                    <a href="logout.php" class="nav-btn" title="Cerrar sesión">
                        <i class="bi bi-box-arrow-right"></i> Salir
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Mensajes -->
        <?php if (!empty($mensaje)): ?>
        <div class="alert <?php echo strpos($mensaje, 'Error') !== false || strpos($mensaje, 'No se encontró') !== false ? 'alert-danger' : 'alert-success'; ?> alert-dismissible fade show" role="alert">
            <i class="bi bi-<?php echo strpos($mensaje, 'Error') !== false || strpos($mensaje, 'No se encontró') !== false ? 'exclamation-triangle' : 'check-circle'; ?>-fill me-2"></i>
            <?php echo htmlspecialchars($mensaje); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Tabla de Prospectos del Usuario -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="prospects-table">
                    <div class="p-3">
                        <h5 class="mb-3">
                            <i class="bi bi-people-fill me-2"></i>
                            Mis Prospectos (<?php echo count($prospectos_usuario); ?>)
                        </h5>
                    </div>
                    
                    <?php if (empty($prospectos_usuario)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-person-plus text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">No tienes prospectos registrados aún</p>
                        <a href="form_inteletgroup.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            Registrar Primer Prospecto
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>RUT</th>
                                    <th>Razón Social</th>
                                    <th>Rubro</th>
                                    <th>Email</th>
                                    <th>Teléfono</th>
                                    <th>Documentos</th>
                                    <th>Fecha Registro</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($prospectos_usuario as $prospecto): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($prospecto['rut_cliente']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($prospecto['razon_social']); ?></td>
                                    <td>
                                        <small class="text-muted"><?php echo htmlspecialchars($prospecto['rubro']); ?></small>
                                    </td>
                                    <td>
                                        <a href="mailto:<?php echo htmlspecialchars($prospecto['email']); ?>" class="text-decoration-none">
                                            <small><?php echo htmlspecialchars($prospecto['email']); ?></small>
                                        </a>
                                    </td>
                                    <td>
                                        <a href="tel:<?php echo htmlspecialchars($prospecto['telefono_celular']); ?>" class="text-decoration-none">
                                            <small><?php echo htmlspecialchars($prospecto['telefono_celular']); ?></small>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge-documents">
                                            <?php echo $prospecto['total_documentos']; ?> docs
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('d/m/Y', strtotime($prospecto['fecha_registro'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="rut_cliente" value="<?php echo htmlspecialchars($prospecto['rut_cliente']); ?>">
                                            <button type="submit" name="buscar_rut" class="btn btn-sm btn-outline-primary" title="Ver Documentos">
                                                <i class="bi bi-files"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Búsqueda por RUT -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card modern-card search-card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-search me-2"></i>
                            Buscar Prospecto por RUT
                        </h5>
                        <form method="POST" class="row g-3">
                            <div class="col-md-8">
                                <label for="rut_cliente" class="form-label">RUT del Cliente</label>
                                <input type="text" class="form-control" id="rut_cliente" name="rut_cliente" 
                                       placeholder="12345678-9" value="<?php echo htmlspecialchars($rut_busqueda); ?>" required>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" name="buscar_rut" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-1"></i>
                                    Buscar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($prospecto_info): ?>
        <!-- Información del Prospecto -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card modern-card info-card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-building me-2"></i>
                            Información del Prospecto
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>RUT:</strong> <?php echo htmlspecialchars($prospecto_info['rut_cliente']); ?></p>
                                <p><strong>Razón Social:</strong> <?php echo htmlspecialchars($prospecto_info['razon_social']); ?></p>
                                <p><strong>Rubro:</strong> <?php echo htmlspecialchars($prospecto_info['rubro']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($prospecto_info['email']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Ejecutivo:</strong> <?php echo htmlspecialchars($prospecto_info['ejecutivo_nombre']); ?></p>
                                <p><strong>Teléfono:</strong> <?php echo htmlspecialchars($prospecto_info['telefono_celular']); ?></p>
                                <p><strong>Competencia:</strong> <?php echo htmlspecialchars($prospecto_info['competencia_actual']); ?></p>
                                <p><strong>Fecha Registro:</strong> <?php echo date('d/m/Y H:i', strtotime($prospecto_info['fecha_registro'])); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subir Nuevo Documento -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card modern-card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-cloud-upload me-2"></i>
                            Subir Nuevo Documento
                        </h5>
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="rut_cliente_doc" value="<?php echo htmlspecialchars($prospecto_info['rut_cliente']); ?>">
                            <div class="upload-area">
                                <i class="bi bi-cloud-upload text-primary" style="font-size: 3rem;"></i>
                                <h6 class="mt-3">Seleccionar Archivo</h6>
                                <input type="file" class="form-control mt-3" name="nuevo_documento" 
                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls" required>
                                <small class="text-muted">
                                    Formatos permitidos: PDF, DOC, DOCX, JPG, JPEG, PNG, XLSX, XLS. Máximo 5MB.
                                </small>
                                <div class="mt-3">
                                    <button type="submit" name="subir_documento" class="btn btn-success">
                                        <i class="bi bi-upload me-1"></i>
                                        Subir Documento
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Documentos -->
        <div class="row">
            <div class="col-12">
                <div class="card modern-card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-files me-2"></i>
                            Documentos (<?php echo count($documentos); ?>)
                        </h5>
                        
                        <?php if (empty($documentos)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3">No hay documentos subidos para este prospecto</p>
                        </div>
                        <?php else: ?>
                        <?php foreach ($documentos as $doc): ?>
                        <div class="document-item">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-<?php echo getFileIcon($doc['tipo_archivo']); ?> file-icon text-primary"></i>
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($doc['nombre_original']); ?></h6>
                                            <small class="text-muted">
                                                Subido el <?php echo date('d/m/Y H:i', strtotime($doc['fecha_subida'])); ?> | 
                                                <?php echo formatFileSize($doc['tamaño_archivo']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <a href="<?php echo htmlspecialchars($doc['ruta_archivo']); ?>" 
                                       class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="bi bi-download me-1"></i>
                                        Descargar
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4 bg-light">
        <div class="container text-center">
            <p class="mb-0 text-muted">
                &copy; <?php echo date('Y'); ?> Gestar servicios. Todos los derechos reservados.
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Log del proyecto para debugging
        console.log('InteletGroup Documentos - Usuario:', '<?php echo htmlspecialchars($nombre_usuario); ?>');
        console.log('Proyecto:', '<?php echo htmlspecialchars($proyecto); ?>');
        console.log('Total Prospectos:', <?php echo count($prospectos_usuario); ?>);

        // Mensaje de inicialización
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Panel de Documentos InteletGroup cargado correctamente');
        });
    </script>
</body>
</html>

<?php
// Función para obtener icono según tipo de archivo
function getFileIcon($mimeType) {
    switch ($mimeType) {
        case 'application/pdf':
            return 'pdf';
        case 'application/msword':
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            return 'word';
        case 'application/vnd.ms-excel':
        case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
            return 'excel';
        case 'image/jpeg':
        case 'image/jpg':
        case 'image/png':
            return 'image';
        default:
            return 'text';
    }
}

// Función para formatear tamaño de archivo
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
