<?php
// Script para crear las tablas de InteletGroup - Versión Web
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Headers para evitar caché
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

require_once 'con_db.php';

echo "<html><head><title>Setup InteletGroup Tables</title></head><body>";
echo "<h1>🔧 Configuración de Tablas InteletGroup</h1>";

// SQL para crear las tablas
$sql_queries = [
    // Tabla de prospectos
    "CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        nombre_ejecutivo VARCHAR(255) NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL UNIQUE,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) NOT NULL,
        direccion_comercial TEXT NOT NULL,
        telefono_celular VARCHAR(15) NOT NULL,
        email VARCHAR(255) NOT NULL,
        numero_pos VARCHAR(50),
        tipo_cuenta ENUM('Cuenta Vista', 'Cuenta Corriente') NOT NULL,
        numero_cuenta_bancaria VARCHAR(50) NOT NULL,
        dias_atencion VARCHAR(255) NOT NULL,
        horario_atencion VARCHAR(255) NOT NULL,
        contrata_boleta ENUM('Si', 'No') NOT NULL,
        competencia_actual ENUM('Transbank', 'Getnet', 'Compra Aquí (Bco Estado)', 'Klap', 'SumUp', 'Tuu', 'Ya Ganaste', 'Mercado Pago') NOT NULL,
        documentos_adjuntos TEXT,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        estado ENUM('Activo', 'Inactivo') DEFAULT 'Activo',
        FOREIGN KEY (usuario_id) REFERENCES tb_experian_usuarios(id) ON DELETE CASCADE,
        INDEX idx_rut_cliente (rut_cliente),
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_fecha_registro (fecha_registro)
    )",

    // Tabla de bitácora
    "CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospecto_bitacora (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prospecto_id INT NOT NULL,
        usuario_id INT NOT NULL,
        accion ENUM('Crear', 'Editar', 'Subir Documento', 'Eliminar Documento') NOT NULL,
        descripcion TEXT NOT NULL,
        fecha_accion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent TEXT,
        FOREIGN KEY (prospecto_id) REFERENCES tb_inteletgroup_prospectos(id) ON DELETE CASCADE,
        FOREIGN KEY (usuario_id) REFERENCES tb_experian_usuarios(id) ON DELETE CASCADE,
        INDEX idx_prospecto_id (prospecto_id),
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_fecha_accion (fecha_accion)
    )",

    // Tabla de documentos
    "CREATE TABLE IF NOT EXISTS tb_inteletgroup_documentos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prospecto_id INT NOT NULL,
        usuario_id INT NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL,
        nombre_archivo VARCHAR(255) NOT NULL,
        nombre_original VARCHAR(255) NOT NULL,
        tipo_archivo VARCHAR(100) NOT NULL,
        tamaño_archivo INT NOT NULL,
        ruta_archivo VARCHAR(500) NOT NULL,
        fecha_subida TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        estado ENUM('Activo', 'Eliminado') DEFAULT 'Activo',
        FOREIGN KEY (prospecto_id) REFERENCES tb_inteletgroup_prospectos(id) ON DELETE CASCADE,
        FOREIGN KEY (usuario_id) REFERENCES tb_experian_usuarios(id) ON DELETE CASCADE,
        INDEX idx_prospecto_id (prospecto_id),
        INDEX idx_rut_cliente (rut_cliente),
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_fecha_subida (fecha_subida)
    )"
];

$success_count = 0;
$error_count = 0;

echo "<h2>📋 Ejecutando consultas SQL...</h2>";

foreach ($sql_queries as $index => $sql) {
    echo "<p><strong>Ejecutando consulta " . ($index + 1) . "...</strong></p>";
    
    if ($conexion->query($sql)) {
        echo "<p style='color: green;'>✅ Consulta " . ($index + 1) . " ejecutada exitosamente</p>";
        $success_count++;
    } else {
        echo "<p style='color: red;'>❌ Error en consulta " . ($index + 1) . ": " . $conexion->error . "</p>";
        $error_count++;
    }
}

// Crear directorio de uploads
echo "<h2>📁 Creando directorio de uploads...</h2>";
$upload_dir = 'uploads/inteletgroup_prospectos/';
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        echo "<p style='color: green;'>✅ Directorio de uploads creado: $upload_dir</p>";
    } else {
        echo "<p style='color: red;'>❌ Error al crear directorio de uploads: $upload_dir</p>";
    }
} else {
    echo "<p style='color: green;'>✅ Directorio de uploads ya existe: $upload_dir</p>";
}

echo "<h2>📊 Resumen</h2>";
echo "<p><strong>Consultas exitosas:</strong> $success_count</p>";
echo "<p><strong>Consultas con error:</strong> $error_count</p>";

if ($error_count === 0) {
    echo "<h3 style='color: green;'>🎉 ¡Todas las tablas fueron creadas exitosamente!</h3>";
    echo "<p>El sistema de prospectos InteletGroup está listo para usar.</p>";
} else {
    echo "<h3 style='color: red;'>⚠️ Algunas consultas fallaron. Revise los errores arriba.</h3>";
}

// Verificar que las tablas existen
echo "<h2>🔍 Verificando tablas creadas</h2>";
$tables_to_check = [
    'tb_inteletgroup_prospectos',
    'tb_inteletgroup_prospecto_bitacora', 
    'tb_inteletgroup_documentos'
];

foreach ($tables_to_check as $table) {
    $result = $conexion->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Tabla $table existe</p>";
    } else {
        echo "<p style='color: red;'>❌ Tabla $table NO existe</p>";
    }
}

$conexion->close();
echo "<h2>✅ Proceso completado</h2>";
echo "<p><a href='form_inteletgroup.php'>← Volver al Panel InteletGroup</a></p>";
echo "</body></html>";
?>
