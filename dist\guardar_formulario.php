<?php
// Habilitar reporte de errores pero no mostrarlos en la salida
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Iniciar sesión para acceder a la variable de sesión del ID de usuario
session_start();

// Incluir el archivo de conexión
require_once 'con_db.php';

// Establecer header para JSON
header('Content-Type: application/json');

// Manejador de errores personalizado para capturar errores y devolverlos como JSON
function errorHandler($errno, $errstr, $errfile, $errline) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error PHP: ' . $errstr,
        'details' => "$errfile:$errline"
    ]);
    exit;
}

// Establecer manejador de errores personalizado
set_error_handler('errorHandler');

// Control de excepciones no capturadas
function exceptionHandler($exception) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Excepción no capturada: ' . $exception->getMessage(),
        'details' => $exception->getFile() . ':' . $exception->getLine()
    ]);
    exit;
}

// Establecer manejador de excepciones personalizado
set_exception_handler('exceptionHandler');

// Verificar si la solicitud es POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Método no permitido'
    ]);
    exit;
}

// Verificar si el usuario está autenticado
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Usuario no autenticado. Inicie sesión para continuar.'
    ]);
    exit;
}

// Obtener el ID del usuario desde la sesión
$id_usuario = $_SESSION['usuario_id'];

// Array para almacenar las rutas de los archivos subidos
$rutas_archivos = [];

// Nota: El límite de tamaño de archivos se ha actualizado a 32MB para coincidir con la configuración del servidor
// post_max_size = 32M, upload_max_filesize = 64M

try {
    // Debug logs iniciales
    error_log("Datos recibidos en guardar_formulario.php: " . print_r($_POST, true));
    error_log("Archivos recibidos: " . print_r($_FILES, true)); // Log de archivos
    error_log("ID de usuario de la sesión: " . $id_usuario);

    // --- Procesamiento de Archivos ---
    $directorio_destino = 'documentosExperian/';
    if (!is_dir($directorio_destino)) {
        throw new Exception("El directorio para almacenar archivos '$directorio_destino' no existe");
    }

    // Mapeo de nombres de campo de archivo a nombres de columna DB y prefijos de archivo
    $mapeo_archivos = [
        'archivo_ci' => ['columna' => 'ruta_ci', 'prefijo' => 'ci'],
        'archivo_erut' => ['columna' => 'ruta_erut', 'prefijo' => 'erut'],
        'archivo_extracto' => ['columna' => 'ruta_extracto', 'prefijo' => 'extracto'],
        'archivo_ci_frente' => ['columna' => 'ruta_ci_frente', 'prefijo' => 'cifrente'],
        'archivo_ci_detras' => ['columna' => 'ruta_ci_detras', 'prefijo' => 'cidetras'],
        'archivo_carpeta_tributaria' => ['columna' => 'ruta_carpeta_tributaria', 'prefijo' => 'carptrib'],
        'archivo_consulta_terceros' => ['columna' => 'ruta_consulta_terceros', 'prefijo' => 'consterc']
    ];

    foreach ($mapeo_archivos as $nombre_campo_archivo => $info) {
        if (isset($_FILES[$nombre_campo_archivo]) && $_FILES[$nombre_campo_archivo]['error'] === UPLOAD_ERR_OK) {
            error_log("Procesando archivo: " . $nombre_campo_archivo);
            $archivo = $_FILES[$nombre_campo_archivo];
            $nombre_archivo_original = $archivo['name'];
            $tipo_archivo = $archivo['type'];
            $tamano_archivo = $archivo['size'];
            $temp_archivo = $archivo['tmp_name'];

            // Validaciones
            $tipos_permitidos = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
            if (!in_array($tipo_archivo, $tipos_permitidos)) {
                throw new Exception("Tipo de archivo no permitido para '$nombre_campo_archivo'. Formatos aceptados: PDF, JPG, PNG");
            }
            if ($tamano_archivo > 32 * 1024 * 1024) { // 32MB
                throw new Exception("El archivo '$nombre_campo_archivo' es demasiado grande. Tamaño máximo: 32MB");
            }

            // Generar nombre único y mover
            $extension = pathinfo($nombre_archivo_original, PATHINFO_EXTENSION);
            $nombre_unico = uniqid($info['prefijo'] . '_' . $id_usuario . '_') . '.' . $extension;
            $ruta_destino = $directorio_destino . $nombre_unico;

            if (!move_uploaded_file($temp_archivo, $ruta_destino)) {
                throw new Exception("Error al subir el archivo '$nombre_campo_archivo'");
            }

            // Guardar la ruta para la DB
            $rutas_archivos[$info['columna']] = $ruta_destino;
            error_log("Archivo '$nombre_campo_archivo' guardado en: " . $ruta_destino);
        } elseif (isset($_FILES[$nombre_campo_archivo]) && $_FILES[$nombre_campo_archivo]['error'] !== UPLOAD_ERR_NO_FILE) {
            // Si hay un archivo pero con error (diferente a "no se subió archivo")
            throw new Exception("Error al subir el archivo '$nombre_campo_archivo': Código " . $_FILES[$nombre_campo_archivo]['error']);
        } else {
             error_log("No se recibió archivo para: " . $nombre_campo_archivo);
        }
    }
    // --- Fin Procesamiento de Archivos ---

    // Verificar conexión a la base de datos
    if (!isset($mysqli) || $mysqli->connect_error) {
        throw new Exception("Error de conexión a la base de datos: " . ($mysqli->connect_error ?? 'Conexión no establecida'));
    }

    // Iniciar transacción
    $mysqli->begin_transaction();

    // Construir la consulta de inserción dinámicamente
    $campos = ['id_usuario'];
    $valores = ['?'];
    $params = [$id_usuario];
    $tipos = "i";

    // Agregar rutas de archivos a la consulta
    foreach ($rutas_archivos as $columna => $ruta) {
        if ($ruta !== null) {
            $campos[] = $columna;
            $valores[] = '?';
            $tipos .= 's';
            $params[] = $ruta;
        }
    }

    // Mapeo completo de campos de formulario a DB
    $mapeo_campos = [
        // Sección 1: Identificación del Cliente
        'tipo_cliente' => 'tipo_cliente',
        'rut' => 'rut',
        'razon_social' => 'razon_social',
        'nombre_representante1' => 'nombre_representante1',
        'rut_representante1' => 'rut_representante1',
        'nombre_representante2' => 'nombre_representante2',
        'rut_representante2' => 'rut_representante2',
        'nombre_representante3' => 'nombre_representante3',
        'rut_representante3' => 'rut_representante3',
        'sistema_creacion' => 'sistema_creacion',
        'fecha_creacion' => 'fecha_creacion',
        'notaria' => 'notaria',
        'actividad_economica' => 'actividad_economica',
        'fecha_constitucion' => 'fecha_constitucion',
        'direccion' => 'direccion',
        'comuna' => 'comuna',
        'pagina_web' => 'pagina_web',
        'email' => 'email',
        'telefono' => 'telefono',
        'clasificacion_sii' => 'clasificacion_sii',

        // Sección 2: Datos de Contactos
        'contacto_nombre' => 'contacto_nombre',
        'contacto_rut' => 'contacto_rut',
        'contacto_telefono' => 'contacto_telefono',
        'contacto_email' => 'contacto_email',
        'contacto_backup_nombre' => 'contacto_backup_nombre',
        'contacto_backup_rut' => 'contacto_backup_rut',
        'contacto_backup_telefono' => 'contacto_backup_telefono',
        'contacto_backup_email' => 'contacto_backup_email',

        // Sección 3: Servicios y Transacciones
        'morosos_plan' => 'morosos_plan',
        'morosos_consultas' => 'morosos_consultas',
        'morosos_uf' => 'morosos_uf',
        'morosos_descuento' => 'morosos_descuento',
        'morosos_nuevo_valor' => 'morosos_nuevo_valor',
        'advanced_plan' => 'advanced_plan',
        'advanced_consultas' => 'advanced_consultas',
        'advanced_uf' => 'advanced_uf',
        'advanced_descuento' => 'advanced_descuento',
        'advanced_nuevo_valor' => 'advanced_nuevo_valor',

        // Campos para uso de claves
        'clave_nombre' => 'key_user_nombre',
        'clave_rut' => 'key_user_rut',
        'clave_email' => 'key_user_email',
        'clave_telefono' => 'key_user_telefono',

        // Campos para backup de claves
        'backup_clave_nombre' => 'key_user_backup_nombre',
        'backup_clave_rut' => 'key_user_backup_rut',
        'backup_clave_email' => 'key_user_backup_email',
        'backup_clave_telefono' => 'key_user_backup_telefono'
    ];

    // Procesar campos POST
    foreach ($_POST as $campo => $valor) {
        $campo_db = isset($mapeo_campos[$campo]) ? $mapeo_campos[$campo] : $campo;

        // Asegurarse de no intentar insertar campos de archivo que ya se manejaron
        if (array_key_exists($campo_db, $rutas_archivos)) {
            continue; // Saltar si es una columna de ruta de archivo
        }

        if (preg_match('/^[a-zA-Z0-9_]+$/', $campo_db)) {
            $campos[] = $campo_db;
            $valores[] = "?";

            // Determinar tipo de dato (mantener lógica existente)
            if (in_array($campo_db, ['fecha_creacion', 'fecha_constitucion']) && !empty($valor)) {
                $tipos .= "s"; $params[] = date('Y-m-d', strtotime($valor));
            } else if (in_array($campo_db, ['morosos_uf', 'morosos_nuevo_valor', 'advanced_uf', 'advanced_nuevo_valor'])) {
                $tipos .= "d"; $params[] = !empty($valor) ? floatval($valor) : null; // Permitir nulos si está vacío
            } else if (in_array($campo_db, ['morosos_descuento', 'advanced_descuento'])) {
                $tipos .= "i"; $params[] = !empty($valor) ? intval($valor) : null; // Permitir nulos si está vacío
            } else {
                $tipos .= "s"; $params[] = $valor;
            }
        }
    }

    // Verificar que haya campos para insertar
    if (count($campos) <= 1) { // Solo id_usuario
        throw new Exception("No hay datos válidos para insertar");
    }

    // Logs de depuración
    error_log("Campos a insertar: " . implode(", ", $campos));
    error_log("Tipos de parámetros: " . $tipos);
    error_log("Parámetros: " . print_r($params, true));

    // Preparar y ejecutar la consulta (mantener lógica existente)
    $sql = "INSERT INTO form_experian (" . implode(", ", $campos) . ") VALUES (" . implode(", ", $valores) . ")";
    $stmt = $mysqli->prepare($sql);

    if (!$stmt) {
        throw new Exception("Error preparando la consulta: " . $mysqli->error);
    }

    if (!empty($params)) {
        $refs = array();
        $refs[] = &$tipos;
        foreach ($params as $key => $value) {
            $refs[] = &$params[$key];
        }
        call_user_func_array(array($stmt, 'bind_param'), $refs);
    }

    if (!$stmt->execute()) {
        throw new Exception("Error ejecutando la consulta: " . $stmt->error);
    }

    $id_insertado = $mysqli->insert_id;
    $stmt->close();
    $mysqli->commit();
    
    // Obtener datos relevantes para mensaje personalizado
    $razonSocial = $_POST['razon_social'] ?? '';
    $rutCliente = $_POST['rut'] ?? '';
    
    // Incluir también campos adicionales para respuesta más detallada
    $tipoCliente = $_POST['tipo_cliente'] ?? '';
    $contactoNombre = $_POST['contacto_nombre'] ?? '';
    $fechaCreacion = date('Y-m-d H:i:s');
    $emailContacto = $_POST['contacto_email'] ?? '';
    $telefonoContacto = $_POST['contacto_telefono'] ?? '';
    
    // Respuesta exitosa con mensaje personalizado
    // Construir un mensaje dinámico y personalizado
    $mensaje = 'Formulario guardado exitosamente';
    if ($razonSocial) {
        $mensaje .= " para <strong>{$razonSocial}</strong>";
    }
    
    // Agregar información adicional al mensaje
    if ($tipoCliente) {
        $mensaje .= " (Cliente {$tipoCliente})";
    }
    
    // Obtener más información del comercio para respuesta detallada
    $giro = $_POST['giro'] ?? '';
    $direccion = $_POST['direccion'] ?? '';
    $comuna = $_POST['comuna'] ?? '';
    
    $responseData = [
        'success' => true,
        'message' => $mensaje,
        'id' => $id_insertado,
        'id_usuario' => $id_usuario,
        'archivos_subidos' => array_keys($rutas_archivos), // Lista de columnas de archivos subidos
        'timestamp' => date('Y-m-d H:i:s'),
        'fecha_procesamiento' => $fechaCreacion,
        'detalles_cliente' => [
            'razon_social' => $razonSocial,
            'rut' => $rutCliente,
            'tipo_cliente' => $tipoCliente,
            'giro' => $giro,
            'direccion' => $direccion,
            'comuna' => $comuna
        ],
        'detalles_contacto' => [
            'nombre' => $contactoNombre,
            'email' => $emailContacto,
            'telefono' => $telefonoContacto
        ]
    ];

    // Asegurarse de que no haya salida previa
    if (ob_get_length()) ob_clean();

    // Verificar que la respuesta JSON se genere correctamente
    $jsonResponse = json_encode($responseData);
    if ($jsonResponse === false) {
        error_log('Error al codificar respuesta JSON: ' . json_last_error_msg());
        // Asegurarse de limpiar buffer antes de la respuesta de fallback
        if (ob_get_length()) ob_clean();
        header('Content-Type: application/json'); // Re-asegurar header
        echo json_encode(['success' => true, 'message' => 'Formulario guardado exitosamente (respuesta simplificada)']);
        exit; // Salir después de la respuesta de fallback
    } else {
        // Asegurarse de limpiar buffer antes de la respuesta principal
        if (ob_get_length()) ob_clean();
        header('Content-Type: application/json'); // Re-asegurar header
        echo $jsonResponse;
        exit; // <--- AÑADIR ESTO AQUÍ
    }

} catch(Exception $e) {
    // Revertir transacción
    if (isset($mysqli) && $mysqli->ping()) { // Verificar si la conexión está activa
         $mysqli->rollback();
    }

    // Eliminar archivos subidos si hubo error
    foreach ($rutas_archivos as $ruta) {
        if ($ruta && file_exists($ruta)) {
            unlink($ruta);
            error_log("Archivo eliminado por error: " . $ruta);
        }
    }

    // Log y respuesta de error
    error_log("Error en guardar_formulario.php: " . $e->getMessage() . " en " . $e->getFile() . ":" . $e->getLine());
    // Asegurarse de limpiar buffer antes de la respuesta de error
    if (ob_get_length()) ob_clean();
    http_response_code(400); // Usar 400 o 500 según corresponda
    header('Content-Type: application/json'); // Re-asegurar header
    echo json_encode([
        'success' => false,
        'message' => 'Error al guardar los datos: ' . $e->getMessage()
    ]);
    exit; // Salir después de la respuesta de error

} finally {
     // Cerrar conexión si está abierta
     if (isset($mysqli) && $mysqli instanceof mysqli && $mysqli->thread_id) {
         $mysqli->close();
     }
}

?>