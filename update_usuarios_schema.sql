-- Script para actualizar la tabla tb_experian_usuarios
-- Agregar campos proyecto y rut_ejecutivo

-- Verificar si la columna proyecto ya existe
SET @column_exists_proyecto = 0;
SELECT COUNT(*) INTO @column_exists_proyecto 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' 
AND TABLE_NAME = 'tb_experian_usuarios' 
AND COLUMN_NAME = 'proyecto';

-- Si la columna proyecto no existe, agregarla
SET @sql_proyecto = IF(@column_exists_proyecto = 0, 
    'ALTER TABLE tb_experian_usuarios ADD COLUMN proyecto VARCHAR(100) DEFAULT "inteletGroup" AFTER rol', 
    'SELECT "La columna proyecto ya existe"');

PREPARE stmt FROM @sql_proyecto;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Verificar si la columna rut_ejecutivo ya existe
SET @column_exists_rut = 0;
SELECT COUNT(*) INTO @column_exists_rut 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' 
AND TABLE_NAME = 'tb_experian_usuarios' 
AND COLUMN_NAME = 'rut_ejecutivo';

-- Si la columna rut_ejecutivo no existe, agregarla
SET @sql_rut = IF(@column_exists_rut = 0, 
    'ALTER TABLE tb_experian_usuarios ADD COLUMN rut_ejecutivo VARCHAR(12) AFTER proyecto', 
    'SELECT "La columna rut_ejecutivo ya existe"');

PREPARE stmt FROM @sql_rut;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Actualizar registros existentes para establecer proyecto por defecto
UPDATE tb_experian_usuarios 
SET proyecto = 'inteletGroup' 
WHERE proyecto IS NULL OR proyecto = '';

-- Mostrar la estructura actualizada de la tabla
DESCRIBE tb_experian_usuarios;
