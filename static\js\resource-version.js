/**
 * Resource Version Handler
 * 
 * This script ensures all static resources (images, scripts, stylesheets)
 * get proper version parameters to prevent browser caching issues.
 */
(function() {
    // Obtener la versión actual de la aplicación
    const appVersion = document.querySelector('meta[name="app-version"]')?.content || Date.now();
    
    // Añadir versión a todas las imágenes, scripts y hojas de estilo
    function addVersionToElements(selector, attribute) {
        document.querySelectorAll(selector).forEach(el => {
            const attr = el.getAttribute(attribute);
            if (attr && !attr.includes('v=') && !attr.includes('data:')) {
                const separator = attr.includes('?') ? '&' : '?';
                el.setAttribute(attribute, `${attr}${separator}v=${appVersion}`);
            }
        });
    }
    
    // Aplicar a elementos comunes
    addVersionToElements('img[src]', 'src');
    addVersionToElements('script[src]', 'src');
    addVersionToElements('link[rel="stylesheet"]', 'href');
    
    // Configurar Ajax para añadir versión
    if (window.jQuery) {
        jQuery.ajaxSetup({
            cache: false,
            beforeSend: function(xhr, settings) {
                if (typeof settings.url === 'string') {
                    settings.url += (settings.url.indexOf('?') >= 0 ? '&' : '?') + `v=${appVersion}`;
                }
            }
        });
    }
    
    // Método para recargar la página cuando se detecta una nueva versión
    window.checkAppVersion = function() {
        fetch("check_version.php?v=" + Date.now())
            .then(response => response.json())
            .then(data => {
                const currentVersion = document.querySelector('meta[name="app-version"]')?.content;
                if (data.version && currentVersion && data.version !== currentVersion) {
                    console.log("Nueva versión detectada, recargando...");
                    localStorage.setItem("app_version", data.version);
                    window.location.reload(true);
                }
            })
            .catch(error => console.error("Error checking version:", error));
    };
})();