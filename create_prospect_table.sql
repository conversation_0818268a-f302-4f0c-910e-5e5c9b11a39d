-- Script para crear/actualizar la tabla tb_experian_prospecto
-- con todos los campos requeridos para el nuevo formulario

CREATE TABLE IF NOT EXISTS tb_experian_prospecto (
    id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Campos básicos del ejecutivo
    nombre_ejecutivo VARCHAR(255) NOT NULL COMMENT 'Auto-poblado desde nombre_usuario',
    rut_ejecutivo VARCHAR(12) NOT NULL COMMENT 'RUT del cliente (sin puntos ni guiones)',
    
    -- Información del cliente
    razon_social VARCHAR(255) NOT NULL COMMENT 'Razón social (mayúsculas, sin acentos)',
    rubro VARCHAR(255) NULL COMMENT 'Rubro del negocio',
    direccion_comercial TEXT NULL COMMENT 'Dirección comercial',
    
    -- Contacto
    contacto VARCHAR(255) NULL COMMENT 'Nombre de contacto (legacy field)',
    telefono VARCHAR(20) NOT NULL COMMENT 'Teléfono celular (mínimo 9 dígitos)',
    email VARCHAR(255) NULL COMMENT 'Email de contacto',
    
    -- Información bancaria y comercial
    num_pos INT NULL COMMENT 'Número de POS',
    tipo_cuenta ENUM('Cuenta Vista', 'Cuenta Corriente') NULL COMMENT 'Tipo de cuenta bancaria',
    num_cuenta_bancaria VARCHAR(50) NULL COMMENT 'Número de cuenta bancaria',
    
    -- Horarios y atención
    dias_atencion VARCHAR(255) NULL COMMENT 'Días de atención',
    horario_atencion VARCHAR(255) NULL COMMENT 'Horario de atención',
    
    -- Información comercial
    contrata_boleta ENUM('Sí', 'No') NULL COMMENT 'Contrata boleta',
    competencia_actual ENUM('Transbank', 'Getnet', 'Compra Aquí (Bco Estado)', 'Klap', 'SumUp', 'Tuu', 'Ya Ganaste', 'Mercado Pago') NULL COMMENT 'Competencia actual',
    
    -- Campos de control y estado (legacy)
    fecha DATE NULL COMMENT 'Fecha (legacy field)',
    estado VARCHAR(100) DEFAULT 'Envio información' COMMENT 'Estado del prospecto',
    observaciones TEXT NULL COMMENT 'Observaciones',
    
    -- Archivos adjuntos
    archivo_documentacion VARCHAR(500) NULL COMMENT 'Ruta del archivo de documentación',
    
    -- Campos de auditoría
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación del registro',
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización',
    usuario_id INT NOT NULL COMMENT 'ID del usuario que creó el registro',
    
    -- Índices
    INDEX idx_rut_ejecutivo (rut_ejecutivo),
    INDEX idx_usuario_id (usuario_id),
    INDEX idx_estado (estado),
    INDEX idx_fecha_registro (fecha_registro),
    INDEX idx_razon_social (razon_social),
    
    -- Clave foránea
    FOREIGN KEY (usuario_id) REFERENCES tb_experian_usuarios(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabla de prospectos con campos extendidos';

-- Verificar si necesitamos agregar nuevas columnas a la tabla existente
-- (Este script es seguro para ejecutar en una tabla existente)

-- Agregar columnas si no existen
SET @sql = '';

-- Verificar y agregar direccion_comercial
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'direccion_comercial';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN direccion_comercial TEXT NULL AFTER rubro;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar email
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'email';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN email VARCHAR(255) NULL AFTER telefono;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar num_pos
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'num_pos';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN num_pos INT NULL AFTER email;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar tipo_cuenta
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'tipo_cuenta';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN tipo_cuenta ENUM("Cuenta Vista", "Cuenta Corriente") NULL AFTER num_pos;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar num_cuenta_bancaria
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'num_cuenta_bancaria';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN num_cuenta_bancaria VARCHAR(50) NULL AFTER tipo_cuenta;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar dias_atencion
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'dias_atencion';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN dias_atencion VARCHAR(255) NULL AFTER num_cuenta_bancaria;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar horario_atencion
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'horario_atencion';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN horario_atencion VARCHAR(255) NULL AFTER dias_atencion;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar contrata_boleta
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'contrata_boleta';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN contrata_boleta ENUM("Sí", "No") NULL AFTER horario_atencion;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar competencia_actual
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'competencia_actual';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN competencia_actual ENUM("Transbank", "Getnet", "Compra Aquí (Bco Estado)", "Klap", "SumUp", "Tuu", "Ya Ganaste", "Mercado Pago") NULL AFTER contrata_boleta;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar archivo_documentacion
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'archivo_documentacion';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN archivo_documentacion VARCHAR(500) NULL AFTER observaciones;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Verificar y agregar fecha_actualizacion
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' AND TABLE_NAME = 'tb_experian_prospecto' AND COLUMN_NAME = 'fecha_actualizacion';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE tb_experian_prospecto ADD COLUMN fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER fecha_registro;', '');
IF @sql != '' THEN
    SET @stmt = @sql;
    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;
