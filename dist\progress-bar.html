<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Progress Bar</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Bootstrap Progress Bar</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Default Progress -->
          <div class="progress">
            <div class="progress-bar w-25" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">
              25%
            </div>
          </div>

          <div class="mb-3"></div>

          <!-- Success Progress -->
          <div class="progress">
            <div class="progress-bar bg-success w-50" role="progressbar" aria-valuenow="50" aria-valuemin="0"
              aria-valuemax="100">
              50%
            </div>
          </div>

          <div class="mb-3"></div>

          <!-- Danger Progress -->
          <div class="progress">
            <div class="progress-bar bg-danger w-75" role="progressbar" aria-valuenow="75" aria-valuemin="0"
              aria-valuemax="100">
              75%
            </div>
          </div>

          <div class="mb-3"></div>

          <!-- Warning Progress -->
          <div class="progress">
            <div class="progress-bar bg-warning w-100" role="progressbar" aria-valuenow="100" aria-valuemin="0"
              aria-valuemax="100">
              100%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Skill Progress Bar</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Single Skill Progress Bar -->
          <div class="skill-progress-bar d-flex align-items-center">
            <!-- Skill Icon -->
            <div class="skill-icon shadow-sm">
              <i class="bi bi-code fz-20 text-dark"></i>
            </div>

            <!-- Skill Data -->
            <div class="skill-data">
              <!-- Skill Name-->
              <div class="skill-name d-flex align-items-center justify-content-between">
                <p class="mb-1">HTML5</p>
                <small class="mb-1"><span>78</span>%</small>
              </div>
              <!-- Progress -->
              <div class="progress" style="height: 4px;">
                <div class="progress-bar" style="width: 78%;" role="progressbar" aria-valuenow="78" aria-valuemin="0"
                  aria-valuemax="100"></div>
              </div>
            </div>
          </div>

          <!-- Single Skill Progress Bar -->
          <div class="skill-progress-bar d-flex align-items-center">
            <!-- Skill Icon -->
            <div class="skill-icon shadow-sm">
              <i class="bi bi-heart fz-20 text-danger"></i>
            </div>

            <!-- Skill Data -->
            <div class="skill-data">
              <!-- Skill Name -->
              <div class="skill-name d-flex align-items-center justify-content-between">
                <p class="mb-1">PHP 8</p>
                <small class="mb-1"><span>96</span>%</small>
              </div>
              <!-- Progress -->
              <div class="progress" style="height: 4px;">
                <div class="progress-bar bg-success" style="width: 96%;" role="progressbar" aria-valuenow="96"
                  aria-valuemin="0" aria-valuemax="100"></div>
              </div>
            </div>
          </div>

          <!-- Single Skill Progress Bar -->
          <div class="skill-progress-bar d-flex align-items-center">
            <!-- Skill Icon -->
            <div class="skill-icon shadow-sm">
              <i class="bi bi-bootstrap fz-20 text-primary"></i>
            </div>

            <!-- Skill Data -->
            <div class="skill-data">
              <!-- Skill Name -->
              <div class="skill-name d-flex align-items-center justify-content-between">
                <p class="mb-1">Bootstrap 5</p><small class="mb-1"><span>88</span>%</small>
              </div>
              <!-- Progress -->
              <div class="progress" style="height: 4px;">
                <div class="progress-bar bg-info" style="width: 88%;" role="progressbar" aria-valuenow="88"
                  aria-valuemin="0" aria-valuemax="100">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Upload Data Progress</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <h6>Uploading...</h6>
          <!-- Progress -->
          <div class="progress" style="height: 6px;">
            <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 93%;" role="progressbar"
              aria-valuenow="93" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
          <!-- Progress Info -->
          <div class="progress-info d-flex align-items-center justify-content-between">
            <span>93%</span><span>17 sec remaining</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Task Progress</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Single Task Progress -->
          <div class="single-task-progress">
            <!-- Progress Info -->
            <div class="progress-info d-flex align-items-center justify-content-between">
              <h6 class="mb-1">Project Affan</h6>
              <span class="mt-0 mb-1">70% done</span>
            </div>

            <!-- Progress -->
            <div class="progress" style="height: 4px;">
              <div class="progress-bar bg-danger" style="width: 70%;" role="progressbar" aria-valuenow="70"
                aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <div class="task-member-info d-flex align-items-center justify-content-between">
              <!-- Who working -->
              <div class="who-working mt-2">
                <a href="#"><img class="shadow-sm" src="img/bg-img/7.jpg" alt=""></a>
                <a href="#"><img class="shadow-sm" src="img/bg-img/8.jpg" alt=""></a>
                <a href="#"><img class="shadow-sm" src="img/bg-img/9.jpg" alt=""></a>
              </div>
              <!-- Add New Member -->
              <div class="addnew-member mt-2">
                <a class="btn btn-sm btn-danger rounded-pill" href="#">Add New</a>
              </div>
            </div>
          </div>

          <!-- Single Task Progress -->
          <div class="single-task-progress">
            <!-- Progress Info -->
            <div class="progress-info d-flex align-items-center justify-content-between">
              <h6 class="mb-1">Project Suha</h6>
              <span class="mt-0 mb-1">93% done</span>
            </div>

            <!-- Progress -->
            <div class="progress" style="height: 4px;">
              <div class="progress-bar bg-success" style="width: 93%;" role="progressbar" aria-valuenow="93"
                aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <div class="task-member-info d-flex align-items-center justify-content-between">
              <!-- Who working -->
              <div class="who-working mt-2">
                <a href="#"><img class="shadow-sm" src="img/bg-img/7.jpg" alt=""></a>
                <a href="#"><img class="shadow-sm" src="img/bg-img/8.jpg" alt=""></a>
                <a href="#"><img class="shadow-sm" src="img/bg-img/9.jpg" alt=""></a>
                <a href="#"><img class="shadow-sm" src="img/bg-img/7.jpg" alt=""></a>
                <a href="#"><img class="shadow-sm" src="img/bg-img/8.jpg" alt=""></a>
              </div>
              <!-- Add New Member -->
              <div class="addnew-member mt-2">
                <a class="btn btn-sm btn-success rounded-pill" href="#">Add New</a>
              </div>
            </div>
          </div>

          <!-- Single Task Progress -->
          <div class="single-task-progress">
            <!-- Progress Info -->
            <div class="progress-info d-flex align-items-center justify-content-between">
              <h6 class="mb-1">Project Saasbox</h6>
              <span class="mt-0 mb-1">89% done</span>
            </div>

            <!-- Progress -->
            <div class="progress" style="height: 4px;">
              <div class="progress-bar bg-warning" style="width: 89%;" role="progressbar" aria-valuenow="89"
                aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <div class="task-member-info d-flex align-items-center justify-content-between">
              <!-- Who working -->
              <div class="who-working mt-2">
                <a href="#"><img class="shadow-sm" src="img/bg-img/7.jpg" alt=""></a>
                <a href="#"><img class="shadow-sm" src="img/bg-img/8.jpg" alt=""></a>
              </div>
              <!-- Add New Member -->
              <div class="addnew-member mt-2">
                <a class="btn btn-sm btn-warning rounded-pill" href="#">Add New</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>