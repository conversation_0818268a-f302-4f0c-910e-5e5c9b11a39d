
<?php 

    header('Content-Type: text/html; charset=UTF-8');
    //Iniciar una nueva sesión o reanudar la existente.
    session_start();

    $inc = include("con_db.php");

    
    $usuario = $_GET['usuario'];



    $KPI_2023 = $conex->query("
    select * from  tb_paso_KPI2023  
    where RutTecnicoOrig = '".$_SESSION['RUT']."'");

    $rowtkpi = mysqli_fetch_array($KPI_2023);



     $KPI_CALIDAD = $conex->query("    
     SELECT 
     COUNT(*) as HaySolicitud 
     , COALESCE(SUM(IF(HaySolicitud = 'Si' AND Calidad30TQW = 'No Cumple', 1, 0)), 0) as  <PERSON><PERSON><PERSON><PERSON>
     , COALESCE(SUM(IF(HaySolicitud ='Si' AND Calidad30TQW = 'Cumple', 1, 0)), 0) as  Aprobadas
     , COALESCE(SUM(IF(HaySolicitud ='Si' AND Calidad30TQW = 'Pendiente', 1, 0)), 0) as  Pendiente
   FROM 
     tb_py_Flujo_Calidad
        WHERE 
        DATE_FORMAT(MES_CONTABLE, '%Y%m')  = '202304'
      AND
        HaySolicitud =' Si'
      AND
        RUT_TECNICO = '".$_SESSION['RUT']."'
        ")
  ;

    $rowtCalidad = mysqli_fetch_array($KPI_CALIDAD);



    ?>



<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="APP TQW">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- T\chathe above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>TQW APP - INDICADORES</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/logo_con.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>


  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
       

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0"></h6>
        </div>
        
        <div class="navbar--toggler" id="affanNavbarToggler" data-bs-toggle="offcanvas" data-bs-target="#affanOffcanvas" aria-controls="affanOffcanvas">
          <span class="d-block"></span>
          <span class="d-block"></span>
          <span class="d-block"></span>
        </div>

        <!-- Settings -->
        <!-- <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div> -->
      </div>
    </div>
  </div>

  <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1" aria-labelledby="affanOffcanvsLabel">

    <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas" aria-label="Close"></button>

    <div class="offcanvas-body p-0">
      <div class="sidenav-wrapper">
        <!-- Sidenav Profile -->
        <div class="sidenav-profile bg-gradient">
          <div class="sidenav-style1"></div>

          <!-- User Thumbnail -->
          <div class="user-profile">
            <img src="img/bg-img/2.jpg" alt="">
          </div>

          <!-- User Info -->
          <div class="user-info">
            <h6 class="user-name mb-0"></h6>
            <span><?php echo $_SESSION['usuario'] ?> </span>
          </div>
        </div>

        
        <!-- Sidenav Nav -->
        <ul class="sidenav-nav ps-0">
          <li>
            <a href="home.php"><i class="bi bi-house-door"></i> Home</a>
          </li>
          <li>
            <a href="elements.html"><i class="bi bi-folder2-open"></i>Produccion
              <!-- <span class="badge bg-danger rounded-pill ms-2">220+</span> -->
            </a>
          </li>
          <li>
            <a href="Tecnico_calidad.php"><i class="bi bi-collection"></i> CalidadReactiva
              <span class="badge bg-success rounded-pill ms-2"></span>
            </a>
          </li>
          
          

          <li>
                <a href="#" class="nav-url"><i class="bi bi-globe"></i>ACCESOS DIRECTOS<span class="dropdown-icon"></span></a>
                <ul>
                  <li>
                <a href="https://entrenamientovirtual.cl/course/"><i class="bi bi-globe"></i>Desafio Tecnico
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
              <li>
                <a href="http://172.17.113.6/eps/index.do"><i class="bi bi-globe"></i>NDC Declaracion Material
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
              <li>
                <a href="https://forms.gle/3m3ZUDby4ie5Y5Us7"><i class="bi bi-globe"></i>Registro Serie instaladas
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
              <li>
                <a href="https://lla.cloudcheck.net/t1gui/login-page"><i class="bi bi-globe"></i>Cloudcheck
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
                </ul> 
          </li>
          
          <li>
            <div class="night-mode-nav">
              <i class="bi bi-moon"></i>Modo Oscuro
              <div class="form-check form-switch">
                <input class="form-check-input form-check-success" id="darkSwitch" type="checkbox">
              </div>
            </div>
          </li>
          <li>
            <a href="login.php"><i class="bi bi-box-arrow-right"></i> Cerrar sesión</a>
          </li>
        </ul>

        <!-- Social Info -->
        

        
      </div>
    </div>
  </div>


  <div class="page-content-wrapper py-3">
    <!-- Element Heading -->
        

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>CALIDAD REACTIVA</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body direction-rtl">
          <div class="row">
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
              <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter"><?php echo 100*round($rowtkpi['Calidad30'],3). '%';  ;?></span>
                </h4>
                <p class="mb-0 fz-12">Calidad 30</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
              <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1 text-danger">
                  <span class="counter"><?php echo $rowtkpi['NoCumple']  ;?></span>
                </h4>
                <p class="mb-0 fz-12">NO CUMPLE</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
              <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter"><?php echo $rowtkpi['TotalCasos'] ;?></span>
                </h4>
                <p class="mb-0 fz-12">TOTAL ACTIVIDAD</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter"><?php echo 100*round($rowtkpi['GeoMetaCalidad'],3). '%';  ;?></span>
                </h4>
                <p class="mb-0 fz-12">GEO META CALIDAD</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter"><?php echo 100*round($rowtkpi['KPI_CALIDAD'],3). '%';  ;?></span>
                </h4>
                <p class="mb-0 fz-12">CUMPLIMIENTO</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line-fill mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter"><?php echo 100*round($rowtkpi['CALIDAD_PERFOMANCE'],3). '%';  ;?></span>
                </h4>
                <p class="mb-0 fz-12">TRAMO CUMPLIMIENTO</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>SOLICITUD EXCEPCIONES</h6>
      </div>
    </div>


    <div class="container">
      <div class="card">
        <div class="card-body direction-rtl">
          <div class="row">
            <div class="alert custom-alert-3 alert-warning alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-circle"></i>
                    <div class="alert-text">
                      <h3>Atención!</h3>
                      <h5>El proceso de excepción de calidad reactiva para el periodo de Abril ya esta disponible</h5>
                    </div>
                    <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
                      aria-label="Close"></button>
                  </div>
            
                  <a class="btn m-1 btn-creative btn-warning" href="Tecnico_SolicitudExcep.php">Realizar Solicitud</a>
            </div>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="card">
        <div class="card-body direction-rtl">
          <div class="row">
           
          <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter"><?php echo $rowtCalidad['Aprobadas'];?></span>
                </h4>
                <p class="mb-0 fz-12">APROBADA</p>
              </div>
            </div>


            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter"><?php echo $rowtCalidad['Rechazadas'];?></span>
                </h4>
                <p class="mb-0 fz-12">RECHAZADA</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-1">
                  <span class="counter"><?php echo $rowtCalidad['Pendiente'];?></span>
                </h4>
                <p class="mb-0 fz-12">PENDIENTES</p>
              </div>
            </div>
            
            


          </div>
        </div>
      </div>
    </div>



    
  


  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.php">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          

         

        

          
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
  <script src="js/chart-active.js"></script>
  <script src="js/apexcharts.min.js"></script>
</body>

</html>