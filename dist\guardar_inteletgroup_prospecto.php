<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Headers para JSON y CORS - Bypass service worker
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Manejar preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Iniciar sesión
session_start();

// Incluir archivo de conexión a la base de datos
require_once 'con_db.php';

// Log de debug
error_log("=== INTELETGROUP PROSPECTO DEBUG ===");
error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
error_log("SESSION: " . print_r($_SESSION, true));
error_log("POST: " . print_r($_POST, true));
error_log("GET: " . print_r($_GET, true));

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    error_log("AUTENTICACIÓN FALLIDA - usuario_id: " . ($_SESSION['usuario_id'] ?? 'NO_SET') .
              ", proyecto: " . ($_SESSION['proyecto'] ?? 'NO_SET'));
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Usuario no autenticado o sin permisos para InteletGroup'
    ]);
    exit;
}

// Verificar método POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método no permitido'
    ]);
    exit;
}

// Registrar hora de inicio para depuración
$tiempo_inicio = microtime(true);
error_log("Inicio procesamiento: " . date('H:i:s'));

// Usar la conexión mysqli del archivo con_db.php
$conexion = $mysqli;

// Verificar que la conexión se estableció correctamente
if (!isset($conexion) || $conexion->connect_error) {
    error_log("Error crítico: No se pudo establecer conexión a la base de datos");
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error de conexión a la base de datos',
        'details' => isset($conexion) ? $conexion->connect_error : 'No se pudo establecer la conexión'
    ]);
    exit;
}

try {
    // Obtener datos del formulario
    $usuario_id = intval($_SESSION['usuario_id']); // Convertir explícitamente a entero
    $nombre_ejecutivo = trim($_POST['nombre_ejecutivo'] ?? '');
    $rut_cliente = trim($_POST['rut_cliente'] ?? '');
    $razon_social = strtoupper(trim($_POST['razon_social'] ?? ''));
    $rubro = trim($_POST['rubro'] ?? '');
    $direccion_comercial = trim($_POST['direccion_comercial'] ?? '');
    $telefono_celular = trim($_POST['telefono_celular'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $numero_pos = trim($_POST['numero_pos'] ?? '');
    $tipo_cuenta = trim($_POST['tipo_cuenta'] ?? '');
    $numero_cuenta_bancaria = trim($_POST['numero_cuenta_bancaria'] ?? '');
    $dias_atencion = trim($_POST['dias_atencion'] ?? '');
    $horario_atencion = trim($_POST['horario_atencion'] ?? '');
    $contrata_boleta = trim($_POST['contrata_boleta'] ?? '');
    $competencia_actual = trim($_POST['competencia_actual'] ?? '');
    
    // Verificar los datos recibidos
    error_log("DATOS FORMULARIO: usuario_id=$usuario_id, nombre=$nombre_ejecutivo, rut=$rut_cliente");

    error_log("Datos recibidos - RUT: $rut_cliente, Razón Social: $razon_social");

    // Validaciones básicas
    $errores = [];

    if (empty($nombre_ejecutivo)) $errores[] = 'Nombre de ejecutivo es requerido';
    if (empty($rut_cliente)) $errores[] = 'RUT del cliente es requerido';
    if (empty($razon_social)) $errores[] = 'Razón social es requerida';
    if (empty($rubro)) $errores[] = 'Rubro es requerido';
    if (empty($direccion_comercial)) $errores[] = 'Dirección comercial es requerida';
    if (empty($telefono_celular)) $errores[] = 'Teléfono celular es requerido';
    if (empty($email)) $errores[] = 'Email es requerido';
    if (empty($tipo_cuenta)) $errores[] = 'Tipo de cuenta es requerido';
    if (empty($numero_cuenta_bancaria)) $errores[] = 'Número de cuenta bancaria es requerido';
    if (empty($dias_atencion)) $errores[] = 'Días de atención es requerido';
    if (empty($horario_atencion)) $errores[] = 'Horario de atención es requerido';
    if (empty($contrata_boleta)) $errores[] = 'Contrata boleta es requerido';
    if (empty($competencia_actual)) $errores[] = 'Competencia actual es requerida';

    // Validar formato RUT
    if (!preg_match('/^\d{7,8}-[\dkK]$/', $rut_cliente)) {
        $errores[] = 'Formato de RUT inválido';
    }

    // Validar formato teléfono
    if (!preg_match('/^\d{9,15}$/', $telefono_celular)) {
        $errores[] = 'Formato de teléfono inválido';
    }

    // Validar email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errores[] = 'Formato de email inválido';
    }

    // Validar razón social (solo letras mayúsculas y espacios)
    if (!preg_match('/^[A-Z\s]+$/', $razon_social)) {
        $errores[] = 'Razón social debe contener solo letras mayúsculas y espacios';
    }

    if (!empty($errores)) {
        echo json_encode([
            'success' => false,
            'message' => 'Errores de validación: ' . implode(', ', $errores)
        ]);
        exit;
    }

    // Crear tabla InteletGroup si no existe
    $create_table_sql = "CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        nombre_ejecutivo VARCHAR(255) NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) NOT NULL,
        direccion_comercial TEXT NOT NULL,
        telefono_celular VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        numero_pos VARCHAR(50),
        tipo_cuenta VARCHAR(50) NOT NULL,
        numero_cuenta_bancaria VARCHAR(50) NOT NULL,
        dias_atencion VARCHAR(100) NOT NULL,
        horario_atencion VARCHAR(100) NOT NULL,
        contrata_boleta VARCHAR(10) NOT NULL,
        competencia_actual VARCHAR(100) NOT NULL,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_rut_cliente (rut_cliente),
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_fecha_registro (fecha_registro)
    )";

    error_log("Creando tabla si no existe: tb_inteletgroup_prospectos");
    error_log("SQL de creación: " . $create_table_sql);

    $result = $conexion->query($create_table_sql);
    if (!$result) {
        $error_msg = "Error al crear tabla InteletGroup: " . $conexion->error . " (Código: " . $conexion->errno . ")";
        error_log($error_msg);
        echo json_encode([
            'success' => false,
            'message' => 'Error al crear tabla de base de datos',
            'details' => $conexion->error,
            'sql_error_code' => $conexion->errno
        ]);
        exit;
    }
    error_log("Tabla verificada/creada correctamente");

    // Iniciar transacción para la inserción
    $conexion->begin_transaction();

    try {

    // Manejar archivos subidos
    $documentos_adjuntos = [];
    if (isset($_FILES['documentos']) && !empty($_FILES['documentos']['name'][0])) {
        $upload_dir = 'uploads/inteletgroup_prospectos/';
        
        // Crear directorio si no existe
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $allowed_types = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        $max_size = 5 * 1024 * 1024; // 5MB
        
        for ($i = 0; $i < count($_FILES['documentos']['name']); $i++) {
            if ($_FILES['documentos']['error'][$i] === UPLOAD_ERR_OK) {
                $file_name = $_FILES['documentos']['name'][$i];
                $file_type = $_FILES['documentos']['type'][$i];
                $file_size = $_FILES['documentos']['size'][$i];
                $file_tmp = $_FILES['documentos']['tmp_name'][$i];
                
                // Validar tipo de archivo
                if (!in_array($file_type, $allowed_types)) {
                    echo json_encode([
                        'success' => false,
                        'message' => "Tipo de archivo no permitido: $file_name"
                    ]);
                    exit;
                }
                
                // Validar tamaño
                if ($file_size > $max_size) {
                    echo json_encode([
                        'success' => false,
                        'message' => "Archivo muy grande: $file_name"
                    ]);
                    exit;
                }
                
                // Generar nombre único
                $extension = pathinfo($file_name, PATHINFO_EXTENSION);
                $unique_name = $rut_cliente . '_' . time() . '_' . $i . '.' . $extension;
                $file_path = $upload_dir . $unique_name;
                
                // Mover archivo
                if (move_uploaded_file($file_tmp, $file_path)) {
                    $documentos_adjuntos[] = [
                        'nombre_original' => $file_name,
                        'nombre_archivo' => $unique_name,
                        'tipo_archivo' => $file_type,
                        'tamaño_archivo' => $file_size,
                        'ruta_archivo' => $file_path
                    ];
                }
            }
        }
    }

    // Proceder con la inserción (duplicados permitidos)
    error_log("INICIANDO INSERCIÓN - RUT: $rut_cliente (duplicados permitidos)");

        // RUT no existe, proceder con la inserción
        $sql = "INSERT INTO tb_inteletgroup_prospectos (
            usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro,
            direccion_comercial, telefono_celular, email, numero_pos, tipo_cuenta,
            numero_cuenta_bancaria, dias_atencion, horario_atencion, contrata_boleta,
            competencia_actual
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conexion->prepare($sql);

        // Validar tipos de datos antes de bind_param
        error_log("Tipos de datos para la inserción: usuario_id:" . gettype($usuario_id) .
                 ", nombre:" . gettype($nombre_ejecutivo) .
                 ", rut:" . gettype($rut_cliente));

        try {
            $stmt->bind_param("issssssssssssss",
                $usuario_id, $nombre_ejecutivo, $rut_cliente, $razon_social, $rubro,
                $direccion_comercial, $telefono_celular, $email, $numero_pos, $tipo_cuenta,
                $numero_cuenta_bancaria, $dias_atencion, $horario_atencion, $contrata_boleta,
                $competencia_actual
            );
        } catch (Exception $bindError) {
            error_log("Error en bind_param: " . $bindError->getMessage());
            throw new Exception("Error al preparar la consulta: " . $bindError->getMessage());
        }

        if (!$stmt->execute()) {
            $error_code = $conexion->errno;
            $error_message = $stmt->error;

            error_log("Error en inserción - Código: $error_code, Mensaje: $error_message");
            throw new Exception('Error al insertar prospecto InteletGroup: ' . $error_message);
        }

        // Obtener el ID del prospecto insertado
        $prospecto_id = $conexion->insert_id;
        $affected_rows = $stmt->affected_rows;

        error_log("Resultado inserción - affected_rows: $affected_rows, insert_id: $prospecto_id");

        if ($affected_rows == 0 || $prospecto_id == 0) {
            error_log("Error: No se pudo insertar el prospecto");
            throw new Exception('Error: No se pudo insertar el prospecto');
        }

        error_log("INSERCIÓN EXITOSA - Nuevo prospecto ID: $prospecto_id para RUT: $rut_cliente");

        // NOTA: Documentos se manejarán en una versión futura

        // Crear tabla de bitácora InteletGroup si no existe
        $create_bitacora_sql = "CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospecto_bitacora (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prospecto_id INT NOT NULL,
            usuario_id INT NOT NULL,
            accion ENUM('Crear', 'Editar', 'Subir Documento', 'Eliminar Documento') NOT NULL,
            descripcion TEXT NOT NULL,
            fecha_accion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            INDEX idx_prospecto_id (prospecto_id),
            INDEX idx_usuario_id (usuario_id),
            INDEX idx_fecha_accion (fecha_accion)
        )";

        error_log("Creando tabla de bitácora si no existe");
        if (!$conexion->query($create_bitacora_sql)) {
            error_log("Error al crear tabla bitácora: " . $conexion->error);
            throw new Exception("Error al crear tabla de bitácora: " . $conexion->error);
        }
        error_log("Tabla de bitácora verificada/creada correctamente");

        // Insertar en bitácora de InteletGroup
        $sql_bitacora = "INSERT INTO tb_inteletgroup_prospecto_bitacora (
            prospecto_id, usuario_id, accion, descripcion
        ) VALUES (?, ?, 'Crear', ?)";

        $descripcion_bitacora = "Prospecto InteletGroup creado: $razon_social (RUT: $rut_cliente) - POS: $numero_pos, Cuenta: $tipo_cuenta";

        $stmt_bitacora = $conexion->prepare($sql_bitacora);
        $stmt_bitacora->bind_param("iis", $prospecto_id, $usuario_id, $descripcion_bitacora);
        $stmt_bitacora->execute();

        // Confirmar transacción
        $conexion->commit();

        // Enviar email de notificación
        enviarEmailNotificacion($rut_cliente, $razon_social, $nombre_ejecutivo, $_POST);
        
        // Registrar tiempo total para depuración
        $tiempo_total = microtime(true) - $tiempo_inicio;
        error_log("Tiempo total de procesamiento: " . round($tiempo_total, 2) . " segundos");

        // Asegurar que la respuesta se envíe correctamente
        ob_clean(); // Limpiar cualquier buffer de salida
        echo json_encode([
            'success' => true,
            'message' => 'Prospecto registrado exitosamente',
            'prospecto_id' => $prospecto_id,
            'tiempo' => round($tiempo_total, 2)
        ]);

    } catch (Exception $e) {
        // Rollback de la transacción en caso de error
        $conexion->rollback();
        error_log("Error durante la inserción: " . $e->getMessage());
        throw $e;
    }

} catch (Exception $e) {
    error_log("Error en guardar_inteletgroup_prospecto.php: " . $e->getMessage());
    error_log("Traza del error: " . $e->getTraceAsString());
    
    // Limpiar cualquier salida previa
    ob_clean();
    
    // Asegurarse de enviar código de error HTTP apropiado
    http_response_code(500);
    
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor: ' . $e->getMessage(),
        'error_type' => get_class($e)
    ]);
}

// Función para enviar email de notificación
function enviarEmailNotificacion($rut, $razon_social, $ejecutivo, $datos) {
    // Log para depuración
    error_log("Preparando envío de email para prospecto: $rut - $razon_social");
    
    $to = '<EMAIL>'; // Cambiar por email real
    $subject = "Nuevo Prospecto Registrado - RUT: $rut";
    
    $body = "
    <h2>Nuevo Prospecto Registrado</h2>
    <p><strong>Ejecutivo:</strong> $ejecutivo</p>
    <p><strong>RUT Cliente:</strong> $rut</p>
    <p><strong>Razón Social:</strong> $razon_social</p>
    <p><strong>Rubro:</strong> {$datos['rubro']}</p>
    <p><strong>Dirección:</strong> {$datos['direccion_comercial']}</p>
    <p><strong>Teléfono:</strong> {$datos['telefono_celular']}</p>
    <p><strong>Email:</strong> {$datos['email']}</p>
    <p><strong>Tipo de Cuenta:</strong> {$datos['tipo_cuenta']}</p>
    <p><strong>Competencia Actual:</strong> {$datos['competencia_actual']}</p>
    <p><strong>Fecha de Registro:</strong> " . date('Y-m-d H:i:s') . "</p>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: <EMAIL>" . "\r\n";
    
    // Enviar email (comentado para desarrollo)
    // mail($to, $subject, $body, $headers);
    
    error_log("Notificación de email para prospecto preparada: $rut");
}
?>
