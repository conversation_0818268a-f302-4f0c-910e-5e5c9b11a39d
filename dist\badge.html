<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Badge</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Badge</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <!-- Badge -->
            <span class="m-1 badge bg-primary">Primary</span>
            <span class="m-1 badge bg-secondary">Secondary</span>
            <span class="m-1 badge bg-success">Success</span>
            <span class="m-1 badge bg-danger">Danger</span>
            <span class="m-1 badge bg-warning text-dark">Warning</span>
            <span class="m-1 badge bg-info">Info</span>
            <span class="m-1 badge bg-light text-dark">Light</span>
            <span class="m-1 badge bg-dark">Dark</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Pill badge</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <!-- Pill Badge -->
            <span class="m-1 badge rounded-pill bg-primary">Primary</span>
            <span class="m-1 badge rounded-pill bg-secondary">Secondary</span>
            <span class="m-1 badge rounded-pill bg-success">Success</span>
            <span class="m-1 badge rounded-pill bg-danger">Danger</span>
            <span class="m-1 badge rounded-pill bg-warning text-dark">Warning</span>
            <span class="m-1 badge rounded-pill bg-info">Info</span>
            <span class="m-1 badge rounded-pill bg-light text-dark">Light</span>
            <span class="m-1 badge rounded-pill bg-dark">Dark</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Avater badge</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="badge-avater-wrap d-flex align-items-center mb-3">
            <!-- Badge Avater Lg -->
            <a class="me-2 badge-avater badge-avater-lg" href="#">
              <img class="img-circle" src="img/bg-img/user1.png" alt="">
              <span class="status bg-success"></span>
            </a>

            <!-- Badge Avater -->
            <a class="me-2 badge-avater" href="#">
              <img class="img-circle" src="img/bg-img/user2.png" alt="">
              <span class="status bg-success"></span>
            </a>

            <!-- Badge Avater Sm -->
            <a class="me-2 badge-avater badge-avater-sm" href="#">
              <img class="img-circle" src="img/bg-img/user3.png" alt="">
              <span class="status bg-success"></span>
            </a>

            <!-- Badge Avater Lg -->
            <a class="me-2 badge-avater badge-avater-lg bg-primary" href="#">A
              <span class="status bg-success"></span>
            </a>

            <!-- Badge Avater -->
            <a class="me-2 badge-avater bg-primary" href="#">B
              <span class="status bg-success"></span>
            </a>

            <!-- Badge Avater Sm -->
            <a class="me-2 badge-avater badge-avater-sm bg-primary" href="#">C
              <span class="status bg-success"></span>
            </a>
          </div>

          <!-- Badge Avater Group -->
          <div class="badge-avater-group">
            <!-- Badge Avater Lg -->
            <a class="badge-avater badge-avater-lg" href="#">
              <img class="img-circle" src="img/bg-img/user1.png" alt="">
            </a>

            <!-- Badge Avater Lg -->
            <a class="badge-avater badge-avater-lg" href="#">
              <img class="img-circle" src="img/bg-img/user2.png" alt="">
            </a>

            <!-- Badge Avater Lg -->
            <a class="badge-avater badge-avater-lg" href="#">
              <img class="img-circle" src="img/bg-img/user3.png" alt="">
            </a>

            <!-- Badge Avater Lg -->
            <a class="badge-avater badge-avater-lg" href="#">
              <img class="img-circle" src="img/bg-img/user4.png" alt="">
            </a>

            <!-- Badge Avater Lg -->
            <a class="badge-avater badge-avater-lg bg-primary" href="#">+3</a>
          </div>

          <div class="mb-3"> </div>

          <!-- Badge Avater Group -->
          <div class="badge-avater-group">
            <!-- Badge Avater -->
            <a class="badge-avater" href="#">
              <img class="img-circle" src="img/bg-img/user1.png" alt="">
            </a>

            <!-- Badge Avater -->
            <a class="badge-avater" href="#">
              <img class="img-circle" src="img/bg-img/user2.png" alt="">
            </a>

            <!-- Badge Avater -->
            <a class="badge-avater" href="#">
              <img class="img-circle" src="img/bg-img/user3.png" alt="">
            </a>

            <!-- Badge Avater -->
            <a class="badge-avater" href="#">
              <img class="img-circle" src="img/bg-img/user4.png" alt="">
            </a>

            <!-- Badge Avater -->
            <a class="badge-avater bg-primary" href="#">+3</a>
          </div>

          <div class="mb-3"></div>
          <!-- Badge Avater Group -->
          <div class="badge-avater-group">
            <!-- Badge Avater -->
            <a class="badge-avater badge-avater-sm" href="#">
              <img class="img-circle" src="img/bg-img/user1.png" alt="">
            </a>

            <!-- Badge Avater -->
            <a class="badge-avater badge-avater-sm" href="#">
              <img class="img-circle" src="img/bg-img/user2.png" alt="">
            </a>

            <!-- Badge Avater -->
            <a class="badge-avater badge-avater-sm" href="#">
              <img class="img-circle" src="img/bg-img/user3.png" alt="">
            </a>

            <!-- Badge Avater -->
            <a class="badge-avater badge-avater-sm" href="#">
              <img class="img-circle" src="img/bg-img/user4.png" alt="">
            </a>

            <!-- Badge Avater -->
            <a class="badge-avater badge-avater-sm bg-primary" href="#">+3</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Badge with button</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <a class="btn btn-primary m-1" href="#">Notification<span class="ms-1 badge bg-success">1</span></a>
            <a class="btn btn-warning m-1" href="#">Cart<span class="ms-1 badge rounded-pill bg-dark">9</span></a>
            <a class="btn btn-danger m-1" href="#">Profile<span class="ms-1 badge rounded-pill bg-primary">3</span></a>
            <a class="btn btn-info m-1" href="#">Sales<span class="ms-1 badge rounded-pill bg-primary">1280</span></a>
            <a class="btn btn-dark m-1" href="#">Revenue<span class="ms-1 badge bg-primary">$63</span></a>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Badge Notification</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <button class="btn btn-success position-relative" type="button">Inbox
              <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-info">26</span>
            </button>
            <button class="ms-3 btn btn-primary position-relative" type="button">Spam
              <span class="position-absolute top-0 start-100 translate-middle badge rounded-circle bg-danger p-2">
                <span class="visually-hidden">unread messages</span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Badge with heading</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <h4>Hello Affan!<span class="badge bg-primary ms-2">badge</span></h4>
          <h5>Hello Affan!<span class="badge bg-primary ms-2">badge</span></h5>
          <h6 class="mb-0">Hello Affan!
            <span class="badge bg-primary ms-2">badge</span>
          </h6>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>