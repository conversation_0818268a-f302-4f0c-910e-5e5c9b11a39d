/* :: Button */

.btn {
    font-size: 14px;
    font-weight: 600;
    padding: 6px 14px;
    border-radius: .375rem;
}

.btn-group-lg>.btn,
.btn-lg {
    font-size: 1rem;
    padding: 10px 22px;
    border-radius: .425rem;
}

.btn-group-sm>.btn,
.btn-sm {
    font-size: 12px;
    padding: .25rem .625rem;
}

.btn-group-lg>.btn.rounded-pill,
.btn-lg.rounded-pill {
    padding: 11px 22px;
}

.btn-group-sm>.btn.rounded-pill,
.btn-sm.rounded-pill {
    padding: .25rem .75rem;
}

.btn-creative {
    position: relative;
    z-index: 1;
    border: 0;
    overflow: hidden;

    &:hover,
    &:focus {
        box-shadow: none;
    }

    &.btn-primary::after {
        background-color: $dark;
    }

    &.btn-secondary::after {
        background-color: $heading;
    }

    &.btn-success::after {
        background-color: $dark;
    }

    &.btn-danger::after {
        background-color: $dark;
    }

    &.btn-warning::after {
        background-color: $gray;
    }

    &.btn-info::after {
        background-color: $dark;
    }

    &.btn-light::after {
        background-color: $warning;
    }

    &.btn-dark::after {
        background-color: $primary;
    }

    &::after {
        transition-duration: 800ms;
        position: absolute;
        width: 200%;
        height: 200%;
        content: "";
        top: 110%;
        left: 50%;
        transform: translateX(-50%);
        z-index: -2;
        border-radius: 50%;
    }

    &:hover::after,
    &:focus::after {
        top: -40%;
    }
}

.btn-circle {
    border: 0;
    padding: 0;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    >i {
        font-size: 1.25rem;
        margin-right: 0;
        line-height: 1;
    }
}

.btn-facebook {
    background-color: #1778f2;
    border-color: #1778f2;

    &:hover,
    &:focus {
        background-color: #1778f2;
        border-color: #1778f2;
    }
}

.btn-google {
    background-color: #EA4335;
    border-color: #EA4335;

    &:hover,
    &:focus {
        background-color: #EA4335;
        border-color: #EA4335;
    }
}

.btn-twitter {
    background-color: #1da1f2;
    border-color: #1da1f2;

    &:hover,
    &:focus {
        background-color: #1da1f2;
        border-color: #1da1f2;
    }
}