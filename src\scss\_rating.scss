/* :: Rating */

.rating-card-one {
    position: relative;
    z-index: 1;

    a {
        display: inline-block;
        margin-right: 4px;
        line-height: 1;
        font-size: 1.25rem;
        color: $warning;

        &:last-child {
            margin-right: 0;
        }
    }

    span {
        font-size: 14px;
        font-weight: 500;
    }

    >div {
        background-color: $white;
    }
}

.rating-card-two {
    position: relative;
    z-index: 1;

    a {
        display: inline-block;
        margin-right: 4px;
        line-height: 1;
        font-size: 1.25rem;
        color: $warning;

        &:last-child {
            margin-right: 0;
        }
    }

    span {
        font-size: 12px;
        font-weight: 500;
    }
}

.rating-card-three {
    .stars {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        flex-direction: row-reverse;
        justify-content: center;

        .star-icon {
            stroke: $warning;
            stroke-width: 4px;
            fill: transparent;
            transition: .2s all;
        }

        .stars-star {
            width: 1.5rem;
            height: 1.5rem;
            position: relative;
            cursor: pointer;
            margin: 0 0.25rem;
        }

        .stars-checkbox {
            position: absolute;
            top: -9999rem;
            opacity: 0 !important;
            visibility: hidden;
            width: 0;
            height: 0;
        }
    }

    .stars-star:hover>.star-icon {
        fill: $warning;
    }

    .stars-star:hover~.stars-star>.star-icon {
        fill: $warning;
    }

    .stars-checkbox:checked+.stars-star>.star-icon {
        fill: $warning;
    }

    .stars-checkbox:checked~.stars-star>.star-icon {
        fill: $warning;
    }
}

.rating-detail {
    span {
        &:first-child {
            margin-right: 0.25rem;
            flex: 0 0 2.5rem;
            width: 2.5rem;
        }

        &:last-child {
            margin-left: auto;
            color: $text;
        }
    }

    .progress-bar-wrapper {
        width: 70%;
        border: 1px solid $border;
        padding: 3px 6px;
        border-radius: 2rem;

        .progress {
            height: 0.25rem;
        }
    }
}