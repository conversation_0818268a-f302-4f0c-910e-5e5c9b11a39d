const Ion<PERSON><PERSON><PERSON><PERSON>lider=function(e,t={}){let i=e,n=t,o=0,s=0,a=0,r=0,_=null,l=null,m=!1,f=!1,p=!1,d=!0,u=!1,c=!1,g=!0,h=!1,b=!1,v=!1,x=!1,y="base";const w={win:window,body:document.body,input:i,cont:null,rs:null,min:null,max:null,from:null,to:null,single:null,bar:null,line:null,s_single:null,s_from:null,s_to:null,shad_single:null,shad_from:null,shad_to:null,edge:null,grid:null,grid_labels:[]},k={x_gap:0,x_pointer:0,w_rs:0,w_rs_old:0,w_handle:0,p_gap:0,p_gap_left:0,p_gap_right:0,p_step:0,p_pointer:0,p_handle:0,p_single_fake:0,p_single_real:0,p_from_fake:0,p_from_real:0,p_to_fake:0,p_to_real:0,p_bar_x:0,p_bar_w:0,grid_gap:0,big_num:0,big:[],big_w:[],big_p:[],big_x:[]},L={w_min:0,w_max:0,w_from:0,w_to:0,w_single:0,p_min:0,p_max:0,p_from_fake:0,p_from_left:0,p_to_fake:0,p_to_left:0,p_single_fake:0,p_single_left:0},E={skin:"flat",type:"single",min:10,max:100,from:null,to:null,step:1,min_interval:0,max_interval:0,drag_interval:!1,values:[],p_values:[],from_fixed:!1,from_min:null,from_max:null,from_shadow:!1,to_fixed:!1,to_min:null,to_max:null,to_shadow:!1,prettify_enabled:!0,prettify_separator:" ",prettify:null,force_edges:!1,keyboard:!0,grid:!1,grid_margin:!0,grid_num:4,grid_snap:!1,hide_min_max:!1,hide_from_to:!1,prefix:"",postfix:"",max_postfix:"",decorate_both:!0,values_separator:" — ",input_values_separator:";",disable:!1,block:!1,extra_classes:"",scope:null,onStart:null,onChange:null,onFinish:null,onUpdate:null};"INPUT"!==i.nodeName&&console&&console.warn&&console.warn("Base element should be <input>!",i);const S={skin:i.dataset.skin,type:i.dataset.type,min:i.dataset.min,max:i.dataset.max,from:i.dataset.from,to:i.dataset.to,step:i.dataset.step,min_interval:i.dataset.minInterval,max_interval:i.dataset.maxInterval,drag_interval:i.dataset.dragInterval,values:i.dataset.values,from_fixed:i.dataset.fromFixed,from_min:i.dataset.fromMin,from_max:i.dataset.fromMax,from_shadow:i.dataset.fromShadow,to_fixed:i.dataset.toFixed,to_min:i.dataset.toMin,to_max:i.dataset.toMax,to_shadow:i.dataset.toShadow,prettify_enabled:i.dataset.prettifyEnabled,prettify_separator:i.dataset.prettifySeparator,force_edges:i.dataset.forceEdges,keyboard:i.dataset.keyboard,grid:i.dataset.grid,grid_margin:i.dataset.gridMargin,grid_num:i.dataset.gridNum,grid_snap:i.dataset.gridSnap,hide_min_max:i.dataset.hideMinMax,hide_from_to:i.dataset.hideFromTo,prefix:i.dataset.prefix,postfix:i.dataset.postfix,max_postfix:i.dataset.maxPostfix,decorate_both:i.dataset.decorateBoth,values_separator:i.dataset.valuesSeparator,input_values_separator:i.dataset.inputValuesSeparator,disable:i.dataset.disable,block:i.dataset.block,extra_classes:i.dataset.extraClasses};S.values=S.values&&S.values.split(",");for(let e in S)S.hasOwnProperty(e)&&(void 0!==S[e]&&""!==S[e]||delete S[e]);let M=i.value;void 0!==M&&""!==M&&((M=M.split(S.input_values_separator||n.input_values_separator||";"))[0]&&M[0]==+M[0]&&(M[0]=+M[0]),M[1]&&M[1]==+M[1]&&(M[1]=+M[1]),n&&n.values&&n.values.length?(E.from=M[0]&&n.values.indexOf(M[0]),E.to=M[1]&&n.values.indexOf(M[1])):(E.from=M[0]&&+M[0],E.to=M[1]&&+M[1])),Object.assign(E,n),Object.assign(E,S),n=E;let N={};const T={input:w.input,slider:null,min:n.min,max:n.max,from:n.from,from_percent:0,from_value:null,to:n.to,to_percent:0,to_value:null},q=function(e){p=!1,k.p_step=pe(n.step,!0),y="base",fe(),F(),K(),e?(f=!0,z(!0),me()):(f=!0,z(!0),re()),ie()},F=function(){const e='<span class="irs irs--'+n.skin+" "+n.extra_classes+'"></span>';w.input.insertAdjacentHTML("beforebegin",e),w.input.setAttribute("readonly","true"),w.cont=w.input.previousElementSibling,T.slider=w.cont,w.cont.innerHTML='<span class="irs"><span class="irs-line" tabindex="0"></span><span class="irs-min">0</span><span class="irs-max">1</span><span class="irs-from">0</span><span class="irs-to">0</span><span class="irs-single">0</span></span><span class="irs-grid">1</span>',w.rs=w.cont.querySelector(".irs"),w.min=w.cont.querySelector(".irs-min"),w.max=w.cont.querySelector(".irs-max"),w.from=w.cont.querySelector(".irs-from"),w.to=w.cont.querySelector(".irs-to"),w.single=w.cont.querySelector(".irs-single"),w.line=w.cont.querySelector(".irs-line"),w.grid=w.cont.querySelector(".irs-grid"),"single"===n.type?(w.cont.insertAdjacentHTML("beforeend",'<span class="irs-bar irs-bar--single"></span><span class="irs-shadow shadow-single"></span><span class="irs-handle single"><i></i><i></i><i></i></span>'),w.bar=w.cont.querySelector(".irs-bar"),w.edge=w.cont.querySelector(".irs-bar--single"),w.s_single=w.cont.querySelector(".single"),w.from.style.visibility="hidden",w.to.style.visibility="hidden",w.shad_single=w.cont.querySelector(".shadow-single")):(w.cont.insertAdjacentHTML("beforeend",'<span class="irs-bar"></span><span class="irs-shadow shadow-from"></span><span class="irs-shadow shadow-to"></span><span class="irs-handle from"><i></i><i></i><i></i></span><span class="irs-handle to"><i></i><i></i><i></i></span>'),w.bar=w.cont.querySelector(".irs-bar"),w.s_from=w.cont.querySelector(".from"),w.s_to=w.cont.querySelector(".to"),w.shad_from=w.cont.querySelector(".shadow-from"),w.shad_to=w.cont.querySelector(".shadow-to"),H()),n.hide_from_to&&(w.from.style.display="none",w.to.style.display="none",w.single.style.display="none"),Ee(),n.disable?(A(),w.input.disabled=!0):(w.input.disabled=!1,W(),C()),n.disable||(n.block?A():W()),n.drag_interval&&(w.bar.style.cursor="ew-resize")},H=function(){const e=n.min,t=n.max,i=n.from,o=n.to;i>e&&o===t?w.s_from.classList.add("type_last"):o<t&&w.s_to.classList.add("type_last")},A=function(){w.cont.insertAdjacentHTML("beforeend",'<span class="irs-disable-mask"></span>'),w.cont.classList.add("irs-disabled")},W=function(){w.cont.classList.remove(".irs-disable-mask"),w.cont.classList.remove("irs-disabled")},I=function(){w.cont.remove(),w.cont=null,w.win.removeEventListener("keydown",U.bind(this,"keyboard")),w.body.removeEventListener("touchmove",O.bind(this)),w.body.removeEventListener("mousemove",O.bind(this)),w.win.removeEventListener("touchend",R.bind(this)),w.win.removeEventListener("mouseup",R.bind(this)),w.grid_labels=[],k.big=[],k.big_w=[],k.big_p=[],k.big_x=[],cancelAnimationFrame(l)},C=function(){p||(w.body.addEventListener("touchmove",O.bind(this)),w.body.addEventListener("mousemove",O.bind(this)),w.win.addEventListener("touchend",R.bind(this)),w.win.addEventListener("mouseup",R.bind(this)),w.line.addEventListener("touchstart",B.bind(this,"click"),{passive:!0}),w.line.addEventListener("mousedown",B.bind(this,"click")),w.line.addEventListener("focus",j.bind(this)),n.drag_interval&&"double"===n.type?(w.bar.addEventListener("touchstart",X.bind(this,"both"),{passive:!0}),w.bar.addEventListener("mousedown",X.bind(this,"both"))):(w.bar.addEventListener("touchstart",B.bind(this,"click"),{passive:!0}),w.bar.addEventListener("mousedown",B.bind(this,"click"))),"single"===n.type?(w.single.addEventListener("touchstart",X.bind(this,"single"),{passive:!0}),w.s_single.addEventListener("touchstart",X.bind(this,"single"),{passive:!0}),w.shad_single.addEventListener("touchstart",B.bind(this,"click"),{passive:!0}),w.single.addEventListener("mousedown",X.bind(this,"single")),w.s_single.addEventListener("mousedown",X.bind(this,"single")),w.edge.addEventListener("mousedown",B.bind(this,"click")),w.shad_single.addEventListener("touchstart",B.bind(this,"click"),{passive:!0})):(w.single.addEventListener("touchstart",X.bind(this,null),{passive:!0}),w.single.addEventListener("mousedown",X.bind(this,null)),w.from.addEventListener("touchstart",X.bind(this,"from"),{passive:!0}),w.s_from.addEventListener("touchstart",X.bind(this,"from"),{passive:!0}),w.to.addEventListener("touchstart",X.bind(this,"to"),{passive:!0}),w.s_to.addEventListener("touchstart",X.bind(this,"to"),{passive:!0}),w.shad_from.addEventListener("touchstart",B.bind(this,"click"),{passive:!0}),w.shad_to.addEventListener("touchstart",B.bind(this,"click"),{passive:!0}),w.from.addEventListener("mousedown",X.bind(this,"from")),w.s_from.addEventListener("mousedown",X.bind(this,"from")),w.to.addEventListener("mousedown",X.bind(this,"to")),w.s_to.addEventListener("mousedown",X.bind(this,"to")),w.shad_from.addEventListener("mousedown",B.bind(this,"click")),w.shad_to.addEventListener("mousedown",B.bind(this,"click"))),n.keyboard&&w.line.addEventListener("keydown",U.bind(this,"keyboard")))},j=function(e){if(y)w.line.focus();else{let e,t;e=(t="single"===n.type?w.single:w.from).getBoundingClientRect().left,e+=t.getBoundingClientRect().width/2-1,B("single",{preventDefault:function(){},pageX:e})}},O=function(e){if(!m)return;const t=e.pageX||e.originalEvent.touches&&e.originalEvent.touches[0].pageX;k.x_pointer=t-k.x_gap,z()},R=function(e){if(!b)return;b=!1;const t=w.cont.querySelector(".state_hover");t&&t.classList.remove("state_hover"),f=!0,ie(),V(),(w.cont.contains(e.target)||m)&&le(),m=!1},X=function(e,t){t.preventDefault();const i=t.pageX||t.originalEvent.touches&&t.originalEvent.touches[0].pageX;2!==t.button&&("both"===e&&P(),e||(e=y||"from"),y=e,b=!0,m=!0,k.x_gap=w.rs.getBoundingClientRect().left,k.x_pointer=i-k.x_gap,Y(),function(e){switch(e){case"single":k.p_gap=be(k.p_pointer-k.p_single_fake),w.s_single.classList.add("state_hover");break;case"from":k.p_gap=be(k.p_pointer-k.p_from_fake),w.s_from.classList.add("state_hover","type_last"),w.s_to.classList.remove("type_last");break;case"to":k.p_gap=be(k.p_pointer-k.p_to_fake),w.s_to.classList.add("state_hover","type_last"),w.s_from.classList.remove("type_last");break;case"both":k.p_gap_left=be(k.p_pointer-k.p_from_fake),k.p_gap_right=be(k.p_to_fake-k.p_pointer),w.s_to.classList.remove("type_last"),w.s_from.classList.remove("type_last")}}(e),w.line.dispatchEvent(new Event("focus")),ie())},B=function(e,t){t.preventDefault();const i=t.pageX||t.originalEvent.touches&&t.originalEvent.touches[0].pageX;2!==t.button&&(y=e,x=!0,k.x_gap=w.rs.getBoundingClientRect().left,k.x_pointer=+(i-k.x_gap).toFixed(),f=!0,z(),w.line.dispatchEvent(new Event("focus")))},U=function(e,t){if(!(t.altKey||t.ctrlKey||t.shiftKey||t.metaKey))switch(t.which){case 83:case 65:case 40:case 37:t.preventDefault(),D(!1);break;case 87:case 68:case 38:case 39:t.preventDefault(),D(!0)}},D=function(e){let t=k.p_pointer;const i=n.step/((n.max-n.min)/100);e?t+=i:t-=i,k.x_pointer=be(k.w_rs/100*t),u=!0,z()},K=function(){if(n){if(n.hide_min_max)return w.min.style.display="none",void(w.max.style.display="none");if(n.values.length)w.min.innerHTML=ke(n.p_values[n.min]),w.max.innerHTML=ke(n.p_values[n.max]);else{const e=ve(n.min),t=ve(n.max);T.min_pretty=e,T.max_pretty=t,w.min.innerHTML=ke(e,n.min),w.max.innerHTML=ke(t,n.max)}L.w_min=w.min.offsetWidth,L.w_max=w.max.offsetWidth}},P=function(){const e=T.to-T.from;null===_&&(_=n.min_interval),n.min_interval=e},V=function(){null!==_&&(n.min_interval=_,_=null)},z=function(e){if(!n)return;if((10===++o||e)&&(o=0,k.w_rs=w.rs.offsetWidth,Q()),!k.w_rs)return;Y();let t=J();switch("both"===y&&(k.p_gap=0,t=J()),"click"===y&&(k.p_gap=k.p_handle/2,t=J(),y=n.drag_interval?"both_one":Z(t)),y){case"base":const e=(n.max-n.min)/100,i=(T.from-n.min)/e,o=(T.to-n.min)/e;k.p_single_real=be(i),k.p_from_real=be(i),k.p_to_real=be(o),k.p_single_real=he(k.p_single_real,n.from_min,n.from_max),k.p_from_real=he(k.p_from_real,n.from_min,n.from_max),k.p_to_real=he(k.p_to_real,n.to_min,n.to_max),k.p_single_fake=G(k.p_single_real),k.p_from_fake=G(k.p_from_real),k.p_to_fake=G(k.p_to_real),y=null;break;case"single":if(n.from_fixed)break;k.p_single_real=$(t),k.p_single_real=ue(k.p_single_real),k.p_single_real=he(k.p_single_real,n.from_min,n.from_max),k.p_single_fake=G(k.p_single_real);break;case"from":if(n.from_fixed)break;k.p_from_real=$(t),k.p_from_real=ue(k.p_from_real),k.p_from_real>k.p_to_real&&(k.p_from_real=k.p_to_real),k.p_from_real=he(k.p_from_real,n.from_min,n.from_max),k.p_from_real=ce(k.p_from_real,k.p_to_real,"from"),k.p_from_real=ge(k.p_from_real,k.p_to_real,"from"),k.p_from_fake=G(k.p_from_real);break;case"to":if(n.to_fixed)break;k.p_to_real=$(t),k.p_to_real=ue(k.p_to_real),k.p_to_real<k.p_from_real&&(k.p_to_real=k.p_from_real),k.p_to_real=he(k.p_to_real,n.to_min,n.to_max),k.p_to_real=ce(k.p_to_real,k.p_from_real,"to"),k.p_to_real=ge(k.p_to_real,k.p_from_real,"to"),k.p_to_fake=G(k.p_to_real);break;case"both":if(n.from_fixed||n.to_fixed)break;t=be(t+.001*k.p_handle),k.p_from_real=$(t)-k.p_gap_left,k.p_from_real=ue(k.p_from_real),k.p_from_real=he(k.p_from_real,n.from_min,n.from_max),k.p_from_real=ce(k.p_from_real,k.p_to_real,"from"),k.p_from_fake=G(k.p_from_real),k.p_to_real=$(t)+k.p_gap_right,k.p_to_real=ue(k.p_to_real),k.p_to_real=he(k.p_to_real,n.to_min,n.to_max),k.p_to_real=ce(k.p_to_real,k.p_from_real,"to"),k.p_to_fake=G(k.p_to_real);break;case"both_one":if(n.from_fixed||n.to_fixed)break;const s=$(t),a=T.from_percent,r=T.to_percent-a,_=r/2;let l=s-_,m=s+_;l<0&&(m=(l=0)+r),m>100&&(l=(m=100)-r),k.p_from_real=ue(l),k.p_from_real=he(k.p_from_real,n.from_min,n.from_max),k.p_from_fake=G(k.p_from_real),k.p_to_real=ue(m),k.p_to_real=he(k.p_to_real,n.to_min,n.to_max),k.p_to_fake=G(k.p_to_real)}"single"===n.type?(k.p_bar_x=k.p_handle/2,k.p_bar_w=k.p_single_fake,T.from_percent=k.p_single_real,T.from=de(k.p_single_real),T.from_pretty=ve(T.from),n.values.length&&(T.from_value=n.values[T.from])):(k.p_bar_x=be(k.p_from_fake+k.p_handle/2),k.p_bar_w=be(k.p_to_fake-k.p_from_fake),T.from_percent=k.p_from_real,T.from=de(k.p_from_real),T.from_pretty=ve(T.from),T.to_percent=k.p_to_real,T.to=de(k.p_to_real),T.to_pretty=ve(T.to),n.values.length&&(T.from_value=n.values[T.from],T.to_value=n.values[T.to])),ee(),te()},Y=function(){k.w_rs?(k.x_pointer<0||isNaN(k.x_pointer)?k.x_pointer=0:k.x_pointer>k.w_rs&&(k.x_pointer=k.w_rs),k.p_pointer=be(k.x_pointer/k.w_rs*100)):k.p_pointer=0},$=function(e){return e/(100-k.p_handle)*100},G=function(e){return e/100*(100-k.p_handle)},J=function(){const e=100-k.p_handle;let t=be(k.p_pointer-k.p_gap);return t<0?t=0:t>e&&(t=e),t},Q=function(){"single"===n.type?k.w_handle=w.s_single.offsetWidth:k.w_handle=w.s_from.offsetWidth,k.p_handle=be(k.w_handle/k.w_rs*100)},Z=function(e){if("single"===n.type)return"single";return e>=k.p_from_real+(k.p_to_real-k.p_from_real)/2?n.to_fixed?"from":"to":n.from_fixed?"to":"from"},ee=function(){k.w_rs&&(L.p_min=L.w_min/k.w_rs*100,L.p_max=L.w_max/k.w_rs*100)},te=function(){k.w_rs&&!n.hide_from_to&&("single"===n.type?(L.w_single=w.single.offsetWidth,L.p_single_fake=L.w_single/k.w_rs*100,L.p_single_left=k.p_single_fake+k.p_handle/2-L.p_single_fake/2,L.p_single_left=ye(L.p_single_left,L.p_single_fake)):(L.w_from=w.from.offsetWidth,L.p_from_fake=L.w_from/k.w_rs*100,L.p_from_left=k.p_from_fake+k.p_handle/2-L.p_from_fake/2,L.p_from_left=be(L.p_from_left),L.p_from_left=ye(L.p_from_left,L.p_from_fake),L.w_to=w.to.offsetWidth,L.p_to_fake=L.w_to/k.w_rs*100,L.p_to_left=k.p_to_fake+k.p_handle/2-L.p_to_fake/2,L.p_to_left=be(L.p_to_left),L.p_to_left=ye(L.p_to_left,L.p_to_fake),L.w_single=w.single.offsetWidth,L.p_single_fake=L.w_single/k.w_rs*100,L.p_single_left=(L.p_from_left+L.p_to_left+L.p_to_fake)/2-L.p_single_fake/2,L.p_single_left=be(L.p_single_left),L.p_single_left=ye(L.p_single_left,L.p_single_fake)))},ie=function(){l&&(cancelAnimationFrame(l),l=null),clearTimeout(s),s=null,n&&(ne(),b?l=requestAnimationFrame(ie):s=setTimeout(ie,300))},ne=function(){k.w_rs=w.rs.offsetWidth,k.w_rs&&(k.w_rs!==k.w_rs_old&&(y="base",v=!0),(k.w_rs!==k.w_rs_old||f)&&(K(),z(!0),oe(),n.grid&&(Te(),Me()),f=!0,k.w_rs_old=k.w_rs,se()),k.w_rs&&(m||f||u)&&((a!==T.from||r!==T.to||f||u)&&(oe(),w.bar.style.left=k.p_bar_x+"%",w.bar.style.width=k.p_bar_w+"%","single"===n.type?(w.bar.style.left="0",w.bar.style.width=k.p_bar_w+k.p_bar_x+"%",w.s_single.style.left=k.p_single_fake+"%",w.single.style.left=L.p_single_left+"%"):(w.s_from.style.left=k.p_from_fake+"%",w.s_to.style.left=k.p_to_fake+"%",(a!==T.from||f)&&(w.from.style.left=L.p_from_left+"%"),(r!==T.to||f)&&(w.to.style.left=L.p_to_left+"%"),w.single.style.left=L.p_single_left+"%"),ae(),a===T.from&&r===T.to||g||(w.input.dispatchEvent(new Event("change")),w.input.dispatchEvent(new Event("input"))),a=T.from,r=T.to,v||c||g||h||_e(),(u||x)&&(u=!1,x=!1,le()),c=!1,v=!1,h=!1),g=!1,u=!1,x=!1,f=!1))},oe=function(){if(!n)return;const e=n.values.length,t=n.p_values;let i,o,s,a,r;if(!n.hide_from_to)if("single"===n.type)e?(i=ke(t[T.from]),w.single.innerHTML=i):(a=ve(T.from),i=ke(a,T.from),w.single.innerHTML=i),te(),L.p_single_left<L.p_min+1?w.min.style.visibility="hidden":w.min.style.visibility="visible",L.p_single_left+L.p_single_fake>100-L.p_max-1?w.max.style.visibility="hidden":w.max.style.visibility="visible";else{e?(n.decorate_both?(i=ke(t[T.from]),i+=n.values_separator,i+=ke(t[T.to])):i=ke(t[T.from]+n.values_separator+t[T.to]),o=ke(t[T.from]),s=ke(t[T.to]),w.single.innerHTML=i,w.from.innerHTML=o,w.to.innerHTML=s):(a=ve(T.from),r=ve(T.to),n.decorate_both?(i=ke(a,T.from),i+=n.values_separator,i+=ke(r,T.to)):i=ke(a+n.values_separator+r,T.to),o=ke(a,T.from),s=ke(r,T.to),w.single.innerHTML=i,w.from.innerHTML=o,w.to.innerHTML=s),te();const _=Math.min(L.p_single_left,L.p_from_left),l=L.p_single_left+L.p_single_fake,m=L.p_to_left+L.p_to_fake;let f=Math.max(l,m);L.p_from_left+L.p_from_fake>=L.p_to_left?(w.from.style.visibility="hidden",w.to.style.visibility="hidden",w.single.style.visibility="visible",T.from===T.to?("from"===y?w.from.style.visibility="visible":"to"===y?w.to.style.visibility="visible":y||(w.from.style.visibility="visible"),w.single.style.visibility="hidden",f=m):(w.from.style.visibility="hidden",w.to.style.visibility="hidden",w.single.style.visibility="visible",f=Math.max(l,m))):(w.from.style.visibility="visible",w.to.style.visibility="visible",w.single.style.visibility="hidden"),_<L.p_min+1?w.min.style.visibility="hidden":w.min.style.visibility="visible",f>100-L.p_max-1?w.max.style.visibility="hidden":w.max.style.visibility="visible"}},se=function(){const e=n,t=w,i="number"==typeof e.from_min&&!isNaN(e.from_min),o="number"==typeof e.from_max&&!isNaN(e.from_max),s="number"==typeof e.to_min&&!isNaN(e.to_min),a="number"==typeof e.to_max&&!isNaN(e.to_max);let r,_,l,m;"single"===e.type?e.from_shadow&&(i||o)?(r=pe(i?e.from_min:e.min),_=pe(o?e.from_max:e.max)-r,r=be(r-k.p_handle/100*r),_=be(_-k.p_handle/100*_),r+=k.p_handle/2,t.shad_single.style.display="block",t.shad_single.style.left=r+"%",t.shad_single.style.width=_+"%"):t.shad_single.style.display="none":(e.from_shadow&&(i||o)?(r=pe(i?e.from_min:e.min),_=pe(o?e.from_max:e.max)-r,r=be(r-k.p_handle/100*r),_=be(_-k.p_handle/100*_),r+=k.p_handle/2,t.shad_from.style.display="block",t.shad_from.style.left=r+"%",t.shad_from.style.width=_+"%"):t.shad_from.style.display="none",e.to_shadow&&(s||a)?(l=pe(s?e.to_min:e.min),m=pe(a?e.to_max:e.max)-l,l=be(l-k.p_handle/100*l),m=be(m-k.p_handle/100*m),l+=k.p_handle/2,t.shad_to.style.display="block",t.shad_to.style.left=l+"%",t.shad_to.style.width=m+"%"):t.shad_to.style.display="none")},ae=function(){"single"===n.type?(n.values.length?w.input.setAttribute("value",T.from_value):w.input.setAttribute("value",T.from),w.input.dataset.from=T.from):(n.values.length?w.input.setAttribute("value",T.from_value+n.input_values_separator+T.to_value):w.input.setAttribute("value",T.from+n.input_values_separator+T.to),w.input.dataset.from=T.from,w.input.dataset.to=T.to)},re=function(){ae(),n.onStart&&"function"==typeof n.onStart&&(n.scope?n.onStart.call(n.scope,T):n.onStart(T))},_e=function(){ae(),n.onChange&&"function"==typeof n.onChange&&(n.scope?n.onChange.call(n.scope,T):n.onChange(T))},le=function(){ae(),n.onFinish&&"function"==typeof n.onFinish&&(n.scope?n.onFinish.call(n.scope,T):n.onFinish(T))},me=function(){ae(),n.onUpdate&&"function"==typeof n.onUpdate&&(n.scope?n.onUpdate.call(n.scope,T):n.onUpdate(T))},fe=function(){w.input.classList.toggle("irs-hidden-input"),d?w.input.setAttribute("tabindex","-1"):w.input.removeAttribute("tabindex"),d=!d},pe=function(e,t){let i,o,s=n.max-n.min,a=s/100;return s?(i=t?e:e-n.min,be(o=i/a)):(p=!0,0)},de=function(e){let t,i,o=n.min,s=n.max,a=o.toString().split(".")[1],r=s.toString().split(".")[1],_=0,l=0;if(0===e)return n.min;if(100===e)return n.max;a&&(_=t=a.length),r&&(_=i=r.length),t&&i&&(_=t>=i?t:i),o<0&&(o=+(o+(l=Math.abs(o))).toFixed(_),s=+(s+l).toFixed(_));let m,f=(s-o)/100*e+o,p=n.step.toString().split(".")[1];return p?f=+f.toFixed(p.length):(f/=n.step,f=+(f*=n.step).toFixed(0)),l&&(f-=l),(m=p?+f.toFixed(p.length):be(f))<n.min?m=n.min:m>n.max&&(m=n.max),m},ue=function(e){let t=Math.round(e/k.p_step)*k.p_step;return t>100&&(t=100),100===e&&(t=100),be(t)},ce=function(e,t,i){let o,s,a=n;return a.min_interval?(o=de(e),s=de(t),"from"===i?s-o<a.min_interval&&(o=s-a.min_interval):o-s<a.min_interval&&(o=s+a.min_interval),pe(o)):e},ge=function(e,t,i){let o,s,a=n;return a.max_interval?(o=de(e),s=de(t),"from"===i?s-o>a.max_interval&&(o=s-a.max_interval):o-s>a.max_interval&&(o=s+a.max_interval),pe(o)):e},he=function(e,t,i){let o=de(e);return"number"!=typeof t&&(t=n.min),"number"!=typeof i&&(i=n.max),o<t&&(o=t),o>i&&(o=i),pe(o)},be=function(e){return+(e=e.toFixed(20))},ve=function(e){return n.prettify_enabled?n.prettify&&"function"==typeof n.prettify?n.prettify(e):xe(e):e},xe=function(e){return e.toString().replace(/(\d{1,3}(?=(?:\d\d\d)+(?!\d)))/g,"$1"+n.prettify_separator)},ye=function(e,t){return n.force_edges?(e<0?e=0:e>100-t&&(e=100-t),be(e)):be(e)},we=function(){let e,t,i=n,o=T,s=i.values,a=s.length;if("string"==typeof i.min&&(i.min=+i.min),"string"==typeof i.max&&(i.max=+i.max),"string"==typeof i.from&&(i.from=+i.from),"string"==typeof i.to&&(i.to=+i.to),"string"==typeof i.step&&(i.step=+i.step),"string"==typeof i.from_min&&(i.from_min=+i.from_min),"string"==typeof i.from_max&&(i.from_max=+i.from_max),"string"==typeof i.to_min&&(i.to_min=+i.to_min),"string"==typeof i.to_max&&(i.to_max=+i.to_max),"string"==typeof i.grid_num&&(i.grid_num=+i.grid_num),i.max<i.min&&(i.max=i.min),a)for(i.p_values=[],i.min=0,i.max=a-1,i.step=1,i.grid_num=i.max,i.grid_snap=!0,t=0;t<a;t++)e=+s[t],isNaN(e)?e=s[t]:(s[t]=e,e=ve(e)),i.p_values.push(e);("number"!=typeof i.from||isNaN(i.from))&&(i.from=i.min),("number"!=typeof i.to||isNaN(i.to))&&(i.to=i.max),"single"===i.type?(i.from<i.min&&(i.from=i.min),i.from>i.max&&(i.from=i.max)):(i.from<i.min&&(i.from=i.min),i.from>i.max&&(i.from=i.max),i.to<i.min&&(i.to=i.min),i.to>i.max&&(i.to=i.max),N.from&&(N.from!==i.from&&i.from>i.to&&(i.from=i.to),N.to!==i.to&&i.to<i.from&&(i.to=i.from)),i.from>i.to&&(i.from=i.to),i.to<i.from&&(i.to=i.from)),("number"!=typeof i.step||isNaN(i.step)||!i.step||i.step<0)&&(i.step=1),"number"==typeof i.from_min&&i.from<i.from_min&&(i.from=i.from_min),"number"==typeof i.from_max&&i.from>i.from_max&&(i.from=i.from_max),"number"==typeof i.to_min&&i.to<i.to_min&&(i.to=i.to_min),"number"==typeof i.to_max&&i.from>i.to_max&&(i.to=i.to_max),o&&(o.min!==i.min&&(o.min=i.min),o.max!==i.max&&(o.max=i.max),(o.from<o.min||o.from>o.max)&&(o.from=i.from),(o.to<o.min||o.to>o.max)&&(o.to=i.to)),("number"!=typeof i.min_interval||isNaN(i.min_interval)||!i.min_interval||i.min_interval<0)&&(i.min_interval=0),("number"!=typeof i.max_interval||isNaN(i.max_interval)||!i.max_interval||i.max_interval<0)&&(i.max_interval=0),i.min_interval&&i.min_interval>i.max-i.min&&(i.min_interval=i.max-i.min),i.max_interval&&i.max_interval>i.max-i.min&&(i.max_interval=i.max-i.min)},ke=function(e,t){let i="",o=n;return o.prefix&&(i+=o.prefix),i+=e,o.max_postfix&&(o.values.length&&e===o.p_values[o.max]?(i+=o.max_postfix,o.postfix&&(i+=" ")):t===o.max&&(i+=o.max_postfix,o.postfix&&(i+=" "))),o.postfix&&(i+=o.postfix),i},Le=function(){T.min=n.min,T.max=n.max,T.from=n.from,T.from_percent=pe(T.from),T.from_pretty=ve(T.from),n.values&&(T.from_value=n.values[T.from]),T.to=n.to,T.to_percent=pe(T.to),T.to_pretty=ve(T.to),n.values&&(T.to_value=n.values[T.to])},Ee=function(){if(!n.grid)return;let e,t,i,o,s,a=n,r=a.max-a.min,_=a.grid_num,l=0,m=0,f=4,p=0,d="";for(Te(),a.grid_snap&&(_=r/a.step),_>50&&(_=50),l=be(100/_),_>4&&(f=3),_>7&&(f=2),_>14&&(f=1),_>28&&(f=0),e=0;e<_+1;e++){for(i=f,(m=be(l*e))>100&&(m=100),k.big[e]=m,o=(m-l*(e-1))/(i+1),t=1;t<=i&&0!==m;t++)d+='<span class="irs-grid-pol small" style="left: '+(p=be(m-o*t))+'%"></span>';d+='<span class="irs-grid-pol" style="left: '+m+'%"></span>',s=de(m),d+='<span class="irs-grid-text js-grid-text-'+e+'" style="left: '+m+'%">'+(s=a.values.length?a.p_values[s]:ve(s))+"</span>"}k.big_num=Math.ceil(_+1),w.cont.classList.add("irs-with-grid"),w.grid.innerHTML=d,Se()},Se=function(){for(let e=0;e<k.big_num;e++)w.grid_labels.push(w.grid.querySelector(".js-grid-text-"+e));Me()},Me=function(){const e=[],t=[],i=k.big_num;for(let n=0;n<i;n++)k.big_w[n]=w.grid_labels[n].offsetWidth,k.big_p[n]=be(k.big_w[n]/k.w_rs*100),k.big_x[n]=be(k.big_p[n]/2),e[n]=be(k.big[n]-k.big_x[n]),t[n]=be(e[n]+k.big_p[n]);n.force_edges&&(e[0]<-k.grid_gap&&(e[0]=-k.grid_gap,t[0]=be(e[0]+k.big_p[0]),k.big_x[0]=k.grid_gap),t[i-1]>100+k.grid_gap&&(t[i-1]=100+k.grid_gap,e[i-1]=be(t[i-1]-k.big_p[i-1]),k.big_x[i-1]=be(k.big_p[i-1]-k.grid_gap))),Ne(2,e,t),Ne(4,e,t);for(let e=0;e<i;e++){const t=w.grid_labels[e];k.big_x[e]!==Number.POSITIVE_INFINITY&&(t.style.marginLeft=-k.big_x[e]+"%")}},Ne=function(e,t,i){const n=k.big_num;for(let o=0;o<n;o+=e){let s=o+e/2;if(s>=n)break;const a=w.grid_labels[s];i[o]<=t[s]?a.style.visibility="visible":a.style.visibility="hidden"}},Te=function(){n.grid_margin&&(k.w_rs=w.rs.offsetWidth,k.w_rs&&("single"===n.type?k.w_handle=w.s_single.offsetWidth:k.w_handle=w.s_from.offsetWidth,k.p_handle=be(k.w_handle/k.w_rs*100),k.grid_gap=be(k.p_handle/2-.1),w.grid.style.width=be(100-k.p_handle)+"%",w.grid.style.left=k.grid_gap+"%"))},qe=function(e){i&&(c=!0,n.from=T.from,n.to=T.to,N.from=T.from,N.to=T.to,n=Object.assign(n,e),we(),Le(),fe(),I(),q(!0))};return{update:function(e){qe(e)},reset:function(){i&&(Le(),qe())},destroy:function(){i&&(fe(),i.removeAttribute("readonly"),I(),i=null,n=null)},init:function(){return we(),q(),this}}};function ionRangeSlider(e,t=null){return"string"==typeof e&&(e=document.querySelector(e)),new IonRangeSlider(e,t).init()}
