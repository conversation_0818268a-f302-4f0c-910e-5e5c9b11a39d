<!DOCTYPE html>
<html lang="es" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulador de Tarifas</title>
    <link rel="stylesheet" href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css">
    <link rel="stylesheet" href="css/styles.css">


    <style>

.table th {
    background-color: var(--bs-success);
    color: var(--bs-light);
}

.form-control:read-only {
    background-color: var(--bs-secondary-bg);
}

.table td, .table th {
    padding: 1rem;
}

.positive-diff {
    color: var(--bs-success);
}

.negative-diff {
    color: var(--bs-danger);
}

    </style>
</head>
<body>
    <div class="container py-4">
        <h1 class="mb-4">Simulador de Tarifas</h1>
        
        <div class="card">
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <label for="montoTicket" class="form-label">Monto Ticket</label>
                        <input type="number" class="form-control" id="montoTicket" value="7000">
                    </div>
                    <div class="col-md-3">
                        <label for="txsMes" class="form-label">Transacciones por Mes</label>
                        <input type="number" class="form-control" id="txsMes" value="400">
                    </div>
                    <div class="col-md-3">
                        <label for="ventaMes" class="form-label">Venta Mensual</label>
                        <input type="text" class="form-control" id="ventaMes" readonly>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Período</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="period" id="periodMonthly" checked>
                            <label class="btn btn-outline-secondary" for="periodMonthly">Mensual</label>
                            <input type="radio" class="btn-check" name="period" id="periodYearly">
                            <label class="btn btn-outline-secondary" for="periodYearly">Anual</label>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Empresa</th>
                                <th>Tarifa</th>
                                <th>Costo <span id="periodLabel">Mensual</span></th>
                                <th>Diferencia</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>BCI</td>
                                <td>1.55%</td>
                                <td id="bciFee">-</td>
                                <td id="bciDiff">-</td>
                            </tr>
                            <tr>
                                <td>Redelcom</td>
                                <td>2.29%</td>
                                <td id="redelcomFee">-</td>
                                <td id="redelcomDiff">-</td>
                            </tr>
                            <tr>
                                <td>GetNet</td>
                                <td>0.63% + $76</td>
                                <td id="getnetFee">-</td>
                                <td id="getnetDiff">-</td>
                            </tr>
                            <tr>
                                <td>Transbank</td>
                                <td>1.1% + $85</td>
                                <td id="transbankFee">-</td>
                                <td id="transbankDiff">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4">
                    <div id="visualization" class="d-flex justify-content-around align-items-end" style="height: 300px;">
                        <!-- Visual bars will be dynamically added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>

console.log('Calculator.js loading...');

// Fee constants
const FEES = {
    bci: { percentage: 0.0155, fixed: 0 },
    redelcom: { percentage: 0.0229, fixed: 0 },
    getnet: { percentage: 0.0063, fixed: 76 },
    transbank: { percentage: 0.011, fixed: 85 }
};

// Format number as Chilean currency
function formatCurrency(value) {
    try {
        return new Intl.NumberFormat('es-CL', {
            style: 'currency',
            currency: 'CLP',
            maximumFractionDigits: 0
        }).format(value);
    } catch (error) {
        console.error('Error formatting currency:', error);
        return '$ 0';
    }
}

// Update visual representation
function updateVisualization(fees) {
    const visualization = document.getElementById('visualization');
    visualization.innerHTML = ''; // Clear existing bars
    
    const maxFee = Math.max(...Object.values(fees));
    const height = 300; // Max height in pixels
    
    Object.entries(fees).forEach(([processor, fee]) => {
        const bar = document.createElement('div');
        const barHeight = (fee / maxFee) * height;
        
        bar.className = 'd-flex flex-column align-items-center';
        bar.innerHTML = `
            <div class="text-center mb-2">${formatCurrency(fee)}</div>
            <div class="bg-${processor === 'bci' ? 'success' : 'secondary'}" 
                 style="width: 50px; height: ${barHeight}px; transition: height 0.3s ease">
            </div>
            <div class="mt-2">${processor.toUpperCase()}</div>
        `;
        
        visualization.appendChild(bar);
    });
}

// Calculate fees for all processors
function calculateFees() {
    try {
        console.log('calculateFees function called');
        
        const montoTicketElement = document.getElementById('montoTicket');
        const txsMesElement = document.getElementById('txsMes');
        const ventaMesElement = document.getElementById('ventaMes');
        const periodYearlyElement = document.getElementById('periodYearly');
        
        if (!montoTicketElement || !txsMesElement || !ventaMesElement) {
            console.error('Required input elements not found');
            return;
        }

        const montoTicket = parseFloat(montoTicketElement.value) || 0;
        const txsMes = parseFloat(txsMesElement.value) || 0;
        console.log('Input values:', { montoTicket, txsMes });
        
        const ventaMes = montoTicket * txsMes;
        console.log('Calculated ventaMes:', ventaMes);

        // Update venta mes field
        ventaMesElement.value = formatCurrency(ventaMes);

        // Calculate fees for each processor
        const fees = {};
        const multiplier = periodYearlyElement.checked ? 12 : 1;
        
        for (const [processor, fee] of Object.entries(FEES)) {
            const monthlyFee = (ventaMes * fee.percentage) + (fee.fixed * txsMes);
            fees[processor] = monthlyFee * multiplier;
        }

        console.log('Calculated fees:', fees);

        // Get BCI fee as reference
        const bciFee = fees.bci;

        // Update UI with fees and differences
        Object.entries(fees).forEach(([processor, fee]) => {
            const feeElement = document.getElementById(`${processor}Fee`);
            const diffElement = document.getElementById(`${processor}Diff`);
            
            if (feeElement && diffElement) {
                feeElement.textContent = formatCurrency(fee);
                
                // Only calculate difference for non-BCI processors
                if (processor === 'bci') {
                    diffElement.textContent = '-';
                    diffElement.className = '';
                } else {
                    const diff = fee - bciFee;
                    diffElement.textContent = formatCurrency(diff);
                    diffElement.className = diff > 0 ? 'negative-diff' : 'positive-diff';
                }
            } else {
                console.error(`Elements for processor ${processor} not found`);
            }
        });

        // Update the visualization
        updateVisualization(fees);
    } catch (error) {
        console.error('Error in calculateFees:', error);
    }
}

// Update period label when toggle changes
function updatePeriodLabel() {
    const periodLabel = document.getElementById('periodLabel');
    const periodYearly = document.getElementById('periodYearly');
    if (periodLabel) {
        periodLabel.textContent = periodYearly.checked ? 'Anual' : 'Mensual';
    }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOMContentLoaded event fired');
    try {
        // Add listeners for input changes
        const inputs = ['montoTicket', 'txsMes'];
        inputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', calculateFees);
            } else {
                console.error(`Element with id ${id} not found`);
            }
        });

        // Add listeners for period toggle
        const periodMonthly = document.getElementById('periodMonthly');
        const periodYearly = document.getElementById('periodYearly');
        if (periodMonthly && periodYearly) {
            periodMonthly.addEventListener('change', () => {
                updatePeriodLabel();
                calculateFees();
            });
            periodYearly.addEventListener('change', () => {
                updatePeriodLabel();
                calculateFees();
            });
        }

        calculateFees(); // Initial calculation
    } catch (error) {
        console.error('Error in initialization:', error);
    }
});


    </script>
</body>
</html>
