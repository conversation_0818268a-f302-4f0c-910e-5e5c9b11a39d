-- Verificar si la columna ya existe
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'gestarse_experian' 
AND TABLE_NAME = 'tb_experian_usuarios' 
AND COLUMN_NAME = 'nombre_usuario';

-- Si la columna no existe, agregarla
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE tb_experian_usuarios ADD COLUMN nombre_usuario VARCHAR(255) AFTER correo', 
    'SELECT "La columna nombre_usuario ya existe"');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Actualizar los registros existentes para establecer nombre_usuario igual a correo
UPDATE tb_experian_usuarios SET nombre_usuario = SUBSTRING_INDEX(correo, '@', 1) WHERE nombre_usuario IS NULL OR nombre_usuario = '';
