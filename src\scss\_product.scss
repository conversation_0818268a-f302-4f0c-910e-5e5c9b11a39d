/* :: Product */

.product-details-card {
    position: relative;
    z-index: 1;

    .product-badge {
        top: 2.5rem;
        left: 2.5rem;
        z-index: 100;
    }

    .product-gallery {
        >a {
            cursor: zoom-in;
        }
    }
}

.single-product-card {
    position: relative;
    z-index: 1;

    .product-thumbnail {
        position: relative;
        z-index: 1;

        img {
            border-radius: .375rem;
        }

        .badge {
            position: absolute;
            right: 1rem;
            bottom: 1rem;
            z-index: 10;
        }
    }

    .product-title {
        font-size: 1rem;
        color: $heading;
        margin-top: .75rem;
        font-weight: 500;
        margin-bottom: .25rem;
    }

    .sale-price {
        font-size: 1rem;
        color: $primary;
        font-weight: 500;

        span {
            font-size: 1rem;
            margin-left: 0.25rem;
            text-decoration: line-through;
            color: $danger;
        }
    }
}

.product-list-wrap {
    .single-product-card .product-thumbnail img {
        max-height: 7rem;
    }
}

.shop-pagination {
    small {
        border-left: 3px solid $primary;
        padding-left: .5rem;
        line-height: 1;
    }
}

.product-gallery-wrapper {
    position: relative;

    .tns-nav {
        position: absolute;
        width: 100%;
        z-index: 10;
        bottom: 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        button {
            border: 0 !important;
            margin: 0 4px;
            width: 24px;
            height: 4px;
            border-radius: 50px;
            background-color: $white;

            &.tns-nav-active {
                background-color: $danger;
            }
        }
    }
}