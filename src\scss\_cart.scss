/* :: Cart */

.cart-table {
    position: relative;
    z-index: 1;

    table {
        max-width: 100%;

        > :not(:last-child)> :last-child>* {
            border-bottom-color: $border;
        }

        .remove-product {
            color: $heading;
            width: 28px;
            height: 28px;
            background-color: $gray;
            display: inline-block;
            text-align: center;
            border-radius: 50%;
            font-size: 12px;

            i {
                line-height: 28px;
            }

            &:hover,
            &:focus {
                color: $white;
                background-color: $heading;
            }
        }
    }

    .table tbody td,
    .table tbody th {
        vertical-align: middle;
        color: $text;
        font-size: 12px;
        padding: .75rem .25rem;
        border: 0;

        h6 {
            font-size: 14px;
        }

        thead {
            th {
                padding: .75rem .25rem;
            }
        }
    }

    img {
        max-height: 2.75rem;
    }

    .qty-text {
        border: 2px solid $border;
        width: 3rem;
        text-align: center;
        height: 2rem;
        border-radius: .25rem;
        transition-duration: 300ms;
        font-weight: 500;
    }
}