/* :: Badge */

.badge {
    padding: .25rem .5rem;
    border-radius: .25rem;
}

.badge-avater {
    position: relative;
    z-index: 1;
    display: block;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    text-align: center;
    color: $white;
    line-height: 2rem;
    font-weight: 700;
    font-size: 14px;

    &:hover,
    &:focus {
        color: $white;
    }

    img {
        display: block;
    }

    .status {
        width: 0.75rem;
        height: 0.75rem;
        border-radius: 50%;
        position: absolute;
        z-index: 10;
        bottom: 0;
        right: 0;
        border: 2px solid $white;
    }

    &.badge-avater-sm {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 10px;
        line-height: 1.5rem;

        .status {
            width: 0.5rem;
            height: 0.5rem;
            bottom: -2px;
            right: -1px;
            border: 1px solid $white;
        }
    }

    &.badge-avater-lg {
        width: 3rem;
        height: 3rem;
        font-size: 16px;
        line-height: 3rem;

        .status {
            width: 1rem;
            height: 1rem;
            bottom: 0;
            right: 0;
        }
    }
}

.badge-avater-group {
    display: flex;
    align-items: center;

    .badge-avater {
        margin-right: -.75rem;

        &.badge-avater-sm {
            margin-right: -.5rem;
        }
    }
}