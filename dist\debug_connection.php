<?php
/**
 * Script de diagnóstico para verificar la conexión y estructura
 */

// Mostrar errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Diagnóstico del Sistema</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
</style>";

echo "<h2>1. Información del Servidor</h2>";
echo "<p class='info'>📍 <strong>Directorio actual:</strong> " . getcwd() . "</p>";
echo "<p class='info'>🐘 <strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p class='info'>📅 <strong>Fecha/Hora:</strong> " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>2. Archivos en el Directorio</h2>";
echo "<ul>";
$files = scandir('.');
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        $size = is_file($file) ? filesize($file) : 'DIR';
        echo "<li>$file ($size bytes)</li>";
    }
}
echo "</ul>";

echo "<h2>3. Búsqueda de con_db.php</h2>";
$possible_paths = [
    "con_db.php",
    "../con_db.php", 
    "../../con_db.php",
    "../../../con_db.php",
    "dist/con_db.php",
    "./con_db.php"
];

$found_db = false;
foreach ($possible_paths as $path) {
    if (file_exists($path)) {
        echo "<p class='success'>✅ Encontrado: $path</p>";
        $found_db = $path;
        break;
    } else {
        echo "<p class='error'>❌ No existe: $path</p>";
    }
}

if ($found_db) {
    echo "<h2>4. Prueba de Conexión</h2>";
    try {
        require_once($found_db);
        
        if (isset($mysqli)) {
            if ($mysqli->connect_error) {
                echo "<p class='error'>❌ Error de conexión: " . $mysqli->connect_error . "</p>";
            } else {
                echo "<p class='success'>✅ Conexión MySQL exitosa</p>";
                
                // Verificar base de datos
                $result = $mysqli->query("SELECT DATABASE() as db_name");
                if ($result) {
                    $row = $result->fetch_assoc();
                    echo "<p class='info'>🗄️ <strong>Base de datos:</strong> " . $row['db_name'] . "</p>";
                }
                
                // Verificar tabla usuarios
                $result = $mysqli->query("SHOW TABLES LIKE 'tb_experian_usuarios'");
                if ($result && $result->num_rows > 0) {
                    echo "<p class='success'>✅ Tabla tb_experian_usuarios existe</p>";
                    
                    // Mostrar estructura
                    $result = $mysqli->query("DESCRIBE tb_experian_usuarios");
                    echo "<h3>Estructura actual de tb_experian_usuarios:</h3>";
                    echo "<table border='1' style='border-collapse: collapse;'>";
                    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Por defecto</th></tr>";
                    while ($row = $result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . $row['Field'] . "</td>";
                        echo "<td>" . $row['Type'] . "</td>";
                        echo "<td>" . $row['Null'] . "</td>";
                        echo "<td>" . $row['Key'] . "</td>";
                        echo "<td>" . $row['Default'] . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p class='error'>❌ Tabla tb_experian_usuarios no existe</p>";
                }
                
                $mysqli->close();
            }
        } else {
            echo "<p class='error'>❌ Variable \$mysqli no está definida en con_db.php</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error al incluir con_db.php: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
} else {
    echo "<p class='error'>❌ No se encontró con_db.php en ninguna ubicación</p>";
}

echo "<h2>5. Extensiones PHP</h2>";
$required_extensions = ['mysqli', 'pdo', 'json'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p class='success'>✅ $ext: Disponible</p>";
    } else {
        echo "<p class='error'>❌ $ext: No disponible</p>";
    }
}

echo "<h2>6. Configuración PHP Relevante</h2>";
echo "<p><strong>upload_max_filesize:</strong> " . ini_get('upload_max_filesize') . "</p>";
echo "<p><strong>post_max_size:</strong> " . ini_get('post_max_size') . "</p>";
echo "<p><strong>max_execution_time:</strong> " . ini_get('max_execution_time') . "</p>";
echo "<p><strong>memory_limit:</strong> " . ini_get('memory_limit') . "</p>";

echo "<hr>";
echo "<p class='info'>💡 <strong>Siguiente paso:</strong> Una vez que este script funcione correctamente, podremos proceder con la actualización del esquema.</p>";
?>
