<?php
/**
 * Verifica la autenticación de usuario
 * Este archivo debe ser incluido al principio de todas las páginas protegidas
 */

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    // Configurar parámetros de sesión mejorados
    ini_set('session.cookie_httponly', 1); // Protección XSS
    ini_set('session.use_only_cookies', 1); // Prevenir session fixation
    ini_set('session.cookie_secure', 1);    // Solo enviar cookies por HTTPS
    ini_set('session.cookie_samesite', 'Lax'); // Protección CSRF
    ini_set('session.gc_maxlifetime', 28800); // 8 horas (en segundos)
    
    // Establecer el dominio de la cookie
    $domain = $_SERVER['HTTP_HOST'];
    if (strpos($domain, 'www.') === 0) {
        $domain = substr($domain, 4); // quitar 'www.'
    }
    ini_set('session.cookie_domain', $domain);
    
    // Iniciar sesión
    session_start();
}

// Cargar utilidades de cache y sesión
require_once 'cache_utils.php';

// Aplicar headers para prevenir cache
no_cache_headers();

/**
 * Función para verificar autenticación
 */
function check_auth() {
    // Si no hay usuario en la sesión o la sesión ha expirado
    if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
        // Registrar intento fallido
        $error_message = "[" . date('Y-m-d H:i:s') . "] Error de autenticación - ";
        $error_message .= "Usuario no identificado - IP: " . $_SERVER['REMOTE_ADDR'];
        $error_message .= " - User Agent: " . $_SERVER['HTTP_USER_AGENT'];
        error_log($error_message);
        
        // Guardar URL actual para redireccionar después del login
        if (isset($_SERVER['REQUEST_URI'])) {
            $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        }
        
        // Destruir sesión actual
        session_unset();
        session_destroy();
        
        // Redireccionar a login con mensaje de error
        $error = "Usuario no autenticado. Inicie sesión para continuar.";
        $redirect = "login.php?error=" . urlencode($error);
        
        // Usar encabezados de redirección o JavaScript como respaldo
        if (!headers_sent()) {
            header("Location: $redirect");
            exit;
        } else {
            echo '<script>window.location.href="' . $redirect . '";</script>';
            echo '<noscript><meta http-equiv="refresh" content="0;url=' . $redirect . '"></noscript>';
            exit('Redireccioando a página de login...');
        }
    }
    
    // Verificar tiempo de actividad (expiración de sesión por inactividad)
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 28800)) {
        // La sesión ha expirado (después de 8 horas de inactividad)
        session_unset();
        session_destroy();
        
        // Redireccionar con mensaje específico de sesión expirada
        $error = "Su sesión ha expirado por inactividad. Por favor, inicie sesión nuevamente.";
        $redirect = "login.php?error=" . urlencode($error);
        
        if (!headers_sent()) {
            header("Location: $redirect");
            exit;
        } else {
            echo '<script>window.location.href="' . $redirect . '";</script>';
            exit;
        }
    }
    
    // Renovar tiempo de actividad
    $_SESSION['last_activity'] = time();
    
    // Regenerar ID de sesión periódicamente para mayor seguridad
    if (!isset($_SESSION['last_regeneration']) || (time() - $_SESSION['last_regeneration'] > 1800)) {
        // Regenerar ID cada 30 minutos
        regenerate_session();
        $_SESSION['last_regeneration'] = time();
    }
    
    return true;
}

// Ejecutar verificación
check_auth();