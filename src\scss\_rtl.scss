/* RTL */

[view-mode="rtl"] {
    body {
        .rtl-mode-switching {
            svg {
                color: $primary;
            }

            .rtl-mode-text {
                display: block;
            }

            .ltr-mode-text {
                display: none;
            }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p,
        span {
            direction: rtl;
        }

        .elements-heading {
            flex-direction: row-reverse;

            .heading-text {
                text-align: right;
            }
        }

        .single-setting-panel a {

            .icon-wrapper {
                margin-right: 0;
                margin-left: 0.5rem;
            }
        }

        .header-content,
        .setting-heading,
        .footer-nav ul,
        .range-with-value,
        .single-plan-check,
        .breadcrumb,
        .standard-tab .nav,
        .colorful-tab .nav,
        .minimal-tab .nav,
        .nav.nav-tabs {
            flex-direction: row-reverse;
        }

        .affan-page-item {
            flex-direction: row-reverse;

            .icon-wrapper {
                margin-right: 0;
                margin-left: .5rem;
            }

            >i {
                margin-left: 0;
                margin-right: auto;
                transform: rotate(180deg);
            }
        }

        .element-heading-wrapper {
            flex-direction: row-reverse;

            >i {
                margin-right: 0;
                margin-left: 1rem;
            }
        }

        a.affan-element-item {
            flex-direction: row-reverse;

            >i {
                margin-left: 0;
                margin-right: auto;
                transform: rotate(180deg);
            }
        }

        .back-button a {
            transform: rotate(180deg);
        }

        .element-heading {
            flex-direction: row-reverse;

            h6 {
                text-align: right;
            }
        }

        .preview-iframe-wrapper .preview-hero-area .demo-desc li i {
            margin-right: 0;
            margin-left: 0.5rem;
        }

        .preview-iframe-wrapper .preview-hero-area .qr-code-wrapper {
            display: block;
        }

        .badge.ms-1,
        .badge.ms-2 {
            margin-left: 0 !important;
            margin-right: 0.25rem !important;
        }

        .sidenav-nav li a {
            flex-direction: row-reverse;

            i {
                margin-right: 0;
                margin-left: 1rem;
            }
        }

        .nav-url .dropdown-icon {
            margin-left: 0;
            margin-right: auto;

            i {
                margin-left: 0;
            }
        }

        .sidenav-nav li .night-mode-nav {
            flex-direction: row-reverse;

            .form-check {
                margin-left: 0;
                margin-right: auto;
            }

            .form-switch {
                padding-left: 0;
            }

            i {
                margin-right: 0;
                margin-left: 1rem;
            }
        }

        .sidenav-nav li ul {
            padding-left: 0;
            padding-right: 2rem;
        }

        .alert,
        .form-group,
        .btn.d-flex,
        .list-group,
        .input-group,
        .direction-rtl,
        .badge-avater-wrap,
        .badge-avater-group,
        .circle-btn-wrapper,
        .rating-card-three,
        .cta-card,
        .progress,
        .single-task-progress,
        .scrollspy-indicatiors,
        .vertical-scrollspy,
        .table,
        .top-products-area,
        .single-product-card {
            direction: rtl;
        }

        .alert-dismissible .btn-close {
            margin-left: 0 !important;
            margin-right: auto;
        }

        .custom-alert-1 {
            padding-left: 1rem;
            padding-right: 27px;

            i {
                margin-right: 0;
                margin-left: .5rem;
            }

            &::after {
                right: 12px;
                left: auto;
            }
        }

        .custom-alert-2 i {
            margin-right: 0;
            margin-left: 0.5rem;
        }

        .custom-alert-3 i {
            margin-right: 0;
            margin-left: 0.75rem;
        }

        .toast {
            direction: rtl;

            .toast-header .btn-close {
                margin-right: .375rem;
                margin-left: 0;
            }

            .toast-header strong {
                margin-left: auto !important;
                margin-right: .25rem !important;
            }
        }

        .custom-toast-1 .btn-close {
            left: 0.625rem;
            right: auto;
        }

        .btn svg.me-2,
        .btn i.me-2,
        .form-file-button.btn.d-flex svg {
            margin-right: 0 !important;
            margin-left: .5rem !important;
        }

        .internet-connection-status {
            direction: rtl;
        }

        .badge-avater-group .badge-avater {
            margin-right: 0;
            margin-left: -.75rem;
        }

        .offcanvas-start .btn-close {
            right: auto;
            left: 1rem;
        }

        .offcanvas-top .btn-close,
        .offcanvas-bottom .btn-close {
            right: auto;
            left: 1rem;
        }

        .form-select {
            background-position: left .75rem center;
        }

        .input-group> :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .input-group:not(.has-validation)>.dropdown-toggle:nth-last-child(n+3),
        .input-group:not(.has-validation)> :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu) {
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .form-check {
            direction: rtl;

            .form-check-input {
                float: right;
                margin-left: .5rem;
            }
        }

        #rangevalue {
            margin-left: 0 !important;
            margin-right: 1rem;
        }

        .accordion-item {
            direction: rtl;

            .accordion-button::after {
                margin-left: 0;
                margin-right: auto;
            }
        }

        .accordion-style-two .accordion-item h6 {
            i {
                margin-left: 0.5rem;
                margin-right: 0;
            }
        }

        .list-group-item {
            .form-check-input {
                margin-right: 0 !important;
                margin-left: .5rem;
            }

            &.active::after {
                left: auto;
                right: 0;
            }

            &.disabled::after {
                left: auto;
                right: 0;
            }
        }

        .breadcrumb-item+.breadcrumb-item::before {
            float: right;
            padding-right: 0;
            padding-left: .5rem;
        }

        .breadcrumb-item+.breadcrumb-item {
            padding-left: 0;
            padding-right: .5rem;
        }

        .timeline-card {
            direction: rtl;

            &::after {
                right: auto;
                left: 1.5rem;
            }
        }

        .card-badge {
            left: auto;
            right: 1.5rem;
        }

        .card-img-wrap {
            margin-right: 0;
            margin-left: 1rem;
        }

        .image-gallery-card .row {
            flex-direction: row-reverse;

            .text-end {
                text-align: left !important;
            }
        }

        .hero-block-content {
            text-align: right;
        }

        .rating-card-one,
        .rating-card-two {
            direction: rtl;

            .rating a {
                margin-right: 0;
                margin-left: 4px;
            }

            span {
                direction: ltr;
            }
        }

        .rating-detail {
            span {
                margin-right: 0;
                margin-left: 4px;
            }

            span:last-child {
                margin-left: 0;
                margin-right: auto;
            }
        }

        .testimonial-style1 {
            .single-testimonial-slide {
                flex-direction: row-reverse;

                .image-wrapper {
                    margin-right: 0;
                    margin-left: 1.25rem;
                }

                i {
                    left: auto;
                    right: -8px;
                }
            }
        }

        .testimonial-style3 {
            .single-testimonial-slide {
                text-align: right;
            }
        }

        .modal-header {
            flex-direction: row-reverse;

            .btn-close {
                margin: -.5rem auto -.5rem 0 !important;
            }
        }

        .modal-body {
            text-align: right;
        }

        .pagination {
            .page-item:first-child .page-link {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                border-top-right-radius: .25rem;
                border-bottom-right-radius: .25rem;

                svg {
                    transform: rotate(180deg);
                }
            }

            .page-item:last-child .page-link {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
                border-top-left-radius: .25rem;
                border-bottom-left-radius: .25rem;

                svg {
                    transform: rotate(180deg);
                }
            }

            &.pagination-one .page-item:first-child .page-link {
                border-left: 0;
                border-right: 1px solid $border;
            }

            &.pagination-one .page-item:last-child .page-link {
                border-right: 0;
                border-left: 1px solid $border;
            }
        }

        .skill-progress-bar {
            direction: rtl;

            .skill-icon {
                margin-right: 0;
                margin-left: 1rem;
            }
        }

        .progress-info {
            span {
                direction: ltr;
            }
        }

        .list-unstyled ul {
            padding-right: 2rem;
            padding-left: 0;
        }

        .price-table-one {
            .single-price-content {
                direction: rtl;

                .pricing-desc ul li i {
                    margin-right: 0 !important;
                    margin-left: .5rem;
                }
            }
        }

        .price-table-two .single-price-table .form-check .form-check-input {
            float: left;
        }

        .chat-user-list {
            direction: rtl;

            .chat-user-thumbnail {
                margin-right: 0 !important;
                margin-left: 1rem !important;
            }
        }

        .chat-user--info {
            direction: rtl;

            .info.ms-1 {
                margin-left: 0 !important;
                margin-right: 0.25rem !important;
            }
        }

        .chat-user--info .user-thumbnail-name {
            margin-left: 0;
            margin-right: 0.375rem;
        }

        .shop-pagination {
            direction: rtl;

            select {
                padding-right: 0.5rem !important;
                padding-left: 1.5rem !important;
            }

            small {
                border-left: 0;
                padding-left: 0;
                margin-left: 0;
                border-right: 3px solid $primary;
                padding-right: .5rem;
                margin-right: .25rem;
            }
        }

        .product-details-card .product-badge {
            left: auto;
            right: 2.5rem;
        }

        .notification-area .alert-text {
            text-align: right;
        }

        .modal-footer {
            flex-direction: row-reverse;
        }

        .blog-list-card {
            direction: rtl;

            .card-blog-img {
                border-radius: 0 .5rem .5rem 0;
            }
        }

        .blog-description {
            direction: rtl;

            span {
                margin-right: .5rem;
            }
        }

        .rating-and-review-wrapper {
            direction: rtl;

            .single-user-review {
                .user-thumbnail {
                    margin-left: .5rem;
                    margin-right: 0;
                }
            }
        }

        .service-card {
            direction: rtl;

            .service-img {
                text-align: left;
                padding-left: 0;
                padding-right: 1.5rem;
            }
        }

        .user-info-card {
            direction: rtl;

            .user-profile.me-3 {
                margin-right: 0 !important;
                margin-left: 1rem !important;
            }
        }

        .login-meta-data p a {
            margin-left: 0;
        }

        .otp-form select {
            text-align: right;
        }

        .demo-container {
            &.direction-rtl {
                .text-end {
                    text-align: left !important;
                }

                .btn.btn-lg.ms-3 {
                    margin-left: 0 !important;
                    margin-right: 1rem !important;
                }
            }
        }

        .nav-url ul {
            padding-left: 0;
            padding-right: 1.5rem;
        }

        .home-page-toast {
            right: auto;
            left: 15px;
        }

        .testimonial-slide-three-wrapper .tns-nav {
            justify-content: flex-end;
        }

        .scrollspy-indicatiors ul li:last-child .nav-link {
            margin-right: .5rem;
        }

        .vertical-scrollspy .scrollspy-indicatiors .nav-link {
            margin-right: 0;
            margin-left: .5rem;
        }

        .vertical-scrollspy .scrollspy-indicatiors ul li:last-child .nav-link {
            margin-right: 0;
        }

        .tiny-slider-one-wrapper .tns-controls {
            right: auto;
            left: 1.25rem;
        }

        .tiny-slider-one-wrapper .tns-nav {
            left: auto;
            right: 1.25rem;
        }

        .tiny-slider-two-wrapper {
            text-align: right;
        }

        .tiny-slider-two-wrapper .tns-nav {
            right: auto;
            left: 2rem;
        }

        .tiny-slider-two-wrapper #totaltnsDotsCount {
            right: auto;
            left: 3.625rem;
        }

        .tiny-slider-three-wrapper {
            text-align: right;
        }

        .dataTable-container .dataTable-table {
            direction: rtl;
        }

        .dataTable-sorter::after,
        .dataTable-sorter::before {
            right: auto;
            left: 0;
        }

        .dataTable-table th a {
            text-align: right;
        }

        .dataTable-bottom>div:last-child,
        .dataTable-top>div:last-child {
            float: left;
        }

        .dataTable-bottom>div:first-child,
        .dataTable-top>div:first-child {
            float: right;
        }

        .countdown1 {
            flex-direction: row-reverse;
        }

        .countdown1>div {
            margin-left: 0.5rem;
            margin-right: 0;
            direction: rtl;
        }

        .countdown1>div:last-child {
            margin-right: 0;
            margin-left: 0;
        }

        .coming-soon-card .countdown3 {
            flex-direction: row-reverse;
        }

        .coming-soon-card .countdown3>div {
            margin-right: 0;
            margin-left: 0.25rem;
            direction: rtl;
        }

        .coming-soon-card .countdown3>div span.word {
            margin-left: 0;
            margin-right: 2px;
        }

        .coming-soon-card .countdown3>div:last-child {
            margin-right: 0;
            margin-left: 0;
        }

        .countdown2 {
            flex-direction: row-reverse;
        }

        .countdown2>div {
            margin-left: 0.5rem;
            direction: rtl;
        }

        .countdown2>div:first-child {
            margin-left: 0.5rem;
        }

        .countdown2>div:last-child {
            margin-left: 0;
        }

        .single-counter-wrap .solid-line.ms-0 {
            margin-left: auto !important;
            margin-right: 0;
        }

        #password-visibility {
            right: auto;
            left: 0.625rem;
        }

        #pswmeter {
            direction: rtl;
        }

        .chat-user-list li .chat-options-btn .dropdown-menu a i {
            margin-left: 0.25rem;
            margin-right: 0;
        }

        .chat-footer form {
            flex-direction: row-reverse;

            .dropup.me-2 {
                margin-left: .5rem;
                margin-right: 0 !important;
            }

            .form-control {
                direction: rtl;
            }
        }

        .video-call-screen .call-btn-group {
            flex-direction: row-reverse;
        }

        .gallery-img {
            direction: ltr;
        }

        .language-lists .form-check {
            text-align: right;
            padding-left: 0;
        }

        .cs-newsletter-form .btn-close {
            right: auto;
            left: 1rem;
        }
    }
}