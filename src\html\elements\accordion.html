<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <!-- Setting Panel -->
        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <!-- Setting Panel -->
        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <!-- Setting Panel -->
        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>
        
        <!-- Setting Panel -->
        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html"><i class="bi bi-arrow-left-short"></i></a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Accordion</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Accordion with Image</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="accordion accordion-style-six" id="accordionStyle6">
            <!-- Single Accordion -->
            <div class="accordion-item" style="background-image: url('img/bg-img/1.jpg')">
              <div class="accordion-header" id="accordionSix1">
                <h6 data-bs-toggle="collapse" data-bs-target="#accordionStyleSix1" aria-expanded="true"
                  aria-controls="accordionStyleSix1">What is refund policy?
                </h6>
                <div class="accordion-collapse collapse show" id="accordionStyleSix1" aria-labelledby="accordionSix1"
                  data-bs-parent="#accordionStyle6">
                  <p class="mb-0 mt-2 text-white">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum,
                    velit?</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item" style="background-image: url('img/bg-img/2.jpg')">
              <div class="accordion-header" id="accordionSix2">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleSix2"
                  aria-expanded="false" aria-controls="accordionStyleSix2">Can it accept Paypal?
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleSix2" aria-labelledby="accordionSix2"
                data-bs-parent="#accordionStyle6">
                <p class="mb-0 mt-2 text-white">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a
                  cupiditate.</p>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item" style="background-image: url('img/bg-img/3.jpg')">
              <div class="accordion-header" id="accordionSix3">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleSix3"
                  aria-expanded="false" aria-controls="accordionStyleSix3">What is PWA ready?
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleSix3" aria-labelledby="accordionSix3"
                data-bs-parent="#accordionStyle6">
                <p class="mb-0 mt-2 text-white">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a
                  cupiditate.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Bordered Accordion</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="accordion accordion-style-five" id="accordionStyle5">
            <!-- Single Accordion -->
            <div class="accordion-item accordion-bg-primary">
              <div class="accordion-header" id="accordionFive1">
                <h6 data-bs-toggle="collapse" data-bs-target="#accordionStyleFive1" aria-expanded="true"
                  aria-controls="accordionStyleFive1">
                  <i class="bi bi-plus-lg"></i>What is refund policy?
                </h6>
                <div class="accordion-collapse collapse show" id="accordionStyleFive1" aria-labelledby="accordionFive1"
                  data-bs-parent="#accordionStyle5">
                  <p class="mb-0 mt-2">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item accordion-bg-warning">
              <div class="accordion-header" id="accordionFive2">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleFive2"
                  aria-expanded="false" aria-controls="accordionStyleFive2">
                  <i class="bi bi-plus-lg"></i>Can it accept
                  Paypal?
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleFive2" aria-labelledby="accordionFive2"
                data-bs-parent="#accordionStyle5">
                <p class="mb-0 mt-2">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.
                </p>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item accordion-bg-success">
              <div class="accordion-header" id="accordionFive3">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleFive3"
                  aria-expanded="false" aria-controls="accordionStyleFive3">
                  <i class="bi bi-plus-lg"></i>What is PWA ready?
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleFive3" aria-labelledby="accordionFive3"
                data-bs-parent="#accordionStyle5">
                <p class="mb-0 mt-2">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.
                </p>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item accordion-bg-info">
              <div class="accordion-header" id="accordionFive4">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleFive4"
                  aria-expanded="false" aria-controls="accordionStyleFive4">
                  <i class="bi bi-plus-lg"></i>What is the single license?
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleFive4" aria-labelledby="accordionFive4"
                data-bs-parent="#accordionStyle5">
                <p class="mb-0 mt-2">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.
                </p>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item accordion-bg-danger">
              <div class="accordion-header" id="accordionFive5">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleFive5"
                  aria-expanded="false" aria-controls="accordionStyleFive5">
                  <i class="bi bi-plus-lg"></i>What's new in 2.0?
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleFive5" aria-labelledby="accordionFive5"
                data-bs-parent="#accordionStyle5">
                <p class="mb-0 mt-2">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Cozy Accordion</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="accordion accordion-flush accordion-style-one" id="accordionStyle1">
            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="accordionOne">
                <h6 data-bs-toggle="collapse" data-bs-target="#accordionStyleOne" aria-expanded="true"
                  aria-controls="accordionStyleOne">What is refund policy?<i class="bi bi-chevron-down"></i></h6>
              </div>
              <div class="accordion-collapse collapse show" id="accordionStyleOne" aria-labelledby="accordionOne"
                data-bs-parent="#accordionStyle1">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="accordionTwo">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleTwo"
                  aria-expanded="false" aria-controls="accordionStyleTwo">Can it accept Paypal?<i
                    class="bi bi-chevron-down"></i></h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleTwo" aria-labelledby="accordionTwo"
                data-bs-parent="#accordionStyle1">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="accordionThree">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleThree"
                  aria-expanded="false" aria-controls="accordionStyleThree">What is PWA ready?<i
                    class="bi bi-chevron-down"></i></h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleThree" aria-labelledby="accordionThree"
                data-bs-parent="#accordionStyle1">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Accordion with Plus Sign</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="accordion accordion-flush accordion-style-two" id="accordionStyle2">
            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="accordionFour">
                <h6 data-bs-toggle="collapse" data-bs-target="#accordionStyleFour" aria-expanded="true"
                  aria-controls="accordionStyleFour"><i class="bi bi-plus-lg"></i>What is refund policy?</h6>
              </div>
              <div class="accordion-collapse collapse show" id="accordionStyleFour" aria-labelledby="accordionFour"
                data-bs-parent="#accordionStyle2">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="accordionFive">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleFive"
                  aria-expanded="false" aria-controls="accordionStyleFive"><i class="bi bi-plus-lg"></i>Can it accept
                  Paypal?</h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleFive" aria-labelledby="accordionFive"
                data-bs-parent="#accordionStyle2">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="accordionSix">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyleSix"
                  aria-expanded="false" aria-controls="accordionStyleSix"><i class="bi bi-plus-lg"></i>What is PWA
                  ready?</h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleSix" aria-labelledby="accordionSix"
                data-bs-parent="#accordionStyle2">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Dark Accordion</h6>
      </div>
    </div>

    <div class="container">
      <div class="card bg-dark">
        <div class="card-body">
          <div class="accordion accordion-style-four" id="accordionStyle4">
            <!-- Single Accordion -->
            <div class="accordion-item bg-transparent">
              <div class="accordion-header" id="accordionTen">
                <h6 data-bs-toggle="collapse" data-bs-target="#accordionStyleTen" aria-expanded="true"
                  aria-controls="accordionStyleTen"># What is refund policy?
                  <i class="bi bi-caret-down"></i>
                </h6>
              </div>
              <div class="accordion-collapse collapse show" id="accordionStyleTen" aria-labelledby="accordionTen"
                data-bs-parent="#accordionStyle4">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item bg-transparent">
              <div class="accordion-header" id="accordion11">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyle11" aria-expanded="false"
                  aria-controls="accordionStyle11"># Can it accept Paypal?
                  <i class="bi bi-caret-down"></i>
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyle11" aria-labelledby="accordion11"
                data-bs-parent="#accordionStyle4">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item bg-transparent">
              <div class="accordion-header" id="accordion12">
                <h6 class="collapsed" data-bs-toggle="collapse" data-bs-target="#accordionStyle12" aria-expanded="false"
                  aria-controls="accordionStyle12"># What is PWA ready?
                  <i class="bi bi-caret-down"></i>
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyle12" aria-labelledby="accordion12"
                data-bs-parent="#accordionStyle4">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Compact Accordion</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="accordion accordion-style-three" id="accordionStyle3">
            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="accordionSeven">
                <h6 class="shadow-sm rounded border" data-bs-toggle="collapse" data-bs-target="#accordionStyleSeven"
                  aria-expanded="true" aria-controls="accordionStyleSeven">What is refund policy?
                  <i class="bi bi-caret-down"></i>
                </h6>
              </div>
              <div class="accordion-collapse collapse show" id="accordionStyleSeven" aria-labelledby="accordionSeven"
                data-bs-parent="#accordionStyle3">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Rerum, velit?</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="accordionEight">
                <h6 class="shadow-sm rounded collapsed border" data-bs-toggle="collapse"
                  data-bs-target="#accordionStyleEight" aria-expanded="false" aria-controls="accordionStyleEight">Can it
                  accept Paypal?
                  <i class="bi bi-caret-down"></i>
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleEight" aria-labelledby="accordionEight"
                data-bs-parent="#accordionStyle3">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="accordionNine">
                <h6 class="shadow-sm rounded collapsed border" data-bs-toggle="collapse"
                  data-bs-target="#accordionStyleNine" aria-expanded="false" aria-controls="accordionStyleNine">What is
                  PWA ready?
                  <i class="bi bi-caret-down"></i>
                </h6>
              </div>
              <div class="accordion-collapse collapse" id="accordionStyleNine" aria-labelledby="accordionNine"
                data-bs-parent="#accordionStyle3">
                <div class="accordion-body">
                  <p class="mb-0">Lorem, ipsum dolor sit amet consectetur adipisicing elit. Quisquam, a cupiditate.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Bootstrap Accordion</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="accordion" id="basicaccordion">
            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="headingOne">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne"
                  aria-expanded="true" aria-controls="collapseOne">What is PWA ready?</button>
              </div>
              <div class="accordion-collapse collapse show" id="collapseOne" aria-labelledby="headingOne"
                data-bs-parent="#basicaccordion">
                <div class="accordion-body">
                  <p class="mb-0">Hello, I am bootstrap 5 accordion. I am number one.</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                  data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">What is refund
                  policy?</button>
              </div>
              <div class="accordion-collapse collapse" id="collapseTwo" aria-labelledby="headingTwo"
                data-bs-parent="#basicaccordion">
                <div class="accordion-body">
                  <p class="mb-0">Hello, I am bootstrap 5 accordion. I am number two.</p>
                </div>
              </div>
            </div>

            <!-- Single Accordion -->
            <div class="accordion-item">
              <div class="accordion-header" id="headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                  data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">Can it accept
                  Paypal?</button>
              </div>
              <div class="accordion-collapse collapse" id="collapseThree" aria-labelledby="headingThree"
                data-bs-parent="#basicaccordion">
                <div class="accordion-body">
                  <p class="mb-0">Hello, I am bootstrap 5 accordion. I am number three.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>