<?php
// Habilitar visualización de errores para depuración
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Mostrar información de la página
echo '<h1>Prueba de conexión a la base de datos</h1>';

// Verificar si el archivo de conexión existe
if (!file_exists('con_db.php')) {
    die('<div style="background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 4px; font-size: 18px;">
         <strong>Error:</strong> El archivo "con_db.php" no existe en el directorio actual. 
         Por favor, asegúrate de que el archivo esté en la misma carpeta que este script.
         </div>');
}

// Intentar incluir el archivo de conexión
try {
    require_once 'con_db.php';
    echo '<div style="background-color: #fff3cd; color: #856404; padding: 15px; margin: 20px; border-radius: 4px; font-size: 16px;">
         Archivo "con_db.php" cargado correctamente.
         </div>';
} catch (Exception $e) {
    die('<div style="background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 4px; font-size: 18px;">
         <strong>Error al cargar el archivo de conexión:</strong> ' . $e->getMessage() . '
         </div>');
}

// Verificar si la variable $conn existe
if (!isset($conn)) {
    die('<div style="background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 4px; font-size: 18px;">
         <strong>Error:</strong> La variable $conn no está definida en el archivo de conexión.
         Asegúrate de que con_db.php crea una conexión PDO almacenada en la variable $conn.
         </div>');
}

// Verificar la conexión a la base de datos
try {
    // Intentar una consulta simple para verificar la conexión
    $stmt = $conn->query("SELECT 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo '<div style="background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 4px; font-size: 18px; border: 1px solid #c3e6cb;">
          <strong>¡Éxito!</strong> Conexión a la base de datos establecida correctamente
          </div>';
    
    // Mostrar información de la conexión
    echo '<div style="background-color: #e2e3e5; color: #383d41; padding: 15px; margin: 20px; border-radius: 4px; font-size: 16px;">
          <h3>Información de la conexión:</h3>
          <ul>
            <li>Driver PDO: ' . $conn->getAttribute(PDO::ATTR_DRIVER_NAME) . '</li>
            <li>Versión del servidor: ' . $conn->getAttribute(PDO::ATTR_SERVER_VERSION) . '</li>
            <li>Estado de la conexión: ' . ($conn->getAttribute(PDO::ATTR_CONNECTION_STATUS) ?? 'Desconocido') . '</li>
          </ul>
          </div>';
    
} catch(PDOException $e) {
    echo '<div style="background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 4px; font-size: 18px; border: 1px solid #f5c6cb;">
          <strong>Error de conexión a la base de datos:</strong> ' . $e->getMessage() . '
          </div>';
    
    // Mostrar información adicional para depuración
    echo '<div style="background-color: #e2e3e5; color: #383d41; padding: 15px; margin: 20px; border-radius: 4px; font-size: 16px;">
          <h3>Información de depuración:</h3>
          <p>Código de error: ' . $e->getCode() . '</p>
          <p>Archivo: ' . $e->getFile() . ' (línea ' . $e->getLine() . ')</p>
          </div>';
}
?>

<div style="margin: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 4px;">
    <h2>Pasos siguientes</h2>
    <ol>
        <li>Si ves un error de conexión, verifica la configuración en el archivo 'con_db.php'</li>
        <li>Asegúrate de que los datos de conexión (servidor, usuario, contraseña, base de datos) sean correctos</li>
        <li>Confirma que la base de datos existe y está accesible</li>
        <li>Verifica que el usuario tenga permisos suficientes para conectarse a la base de datos</li>
    </ol>
</div>