/* Dividers */

.divider {
    width: 100%;
    height: 2px;
    border-top: 2px solid $heading;
    position: relative;
    z-index: 1;
    margin: 1rem 0;
    clear: both;

    &.divider-dotted {
        border-top-style: dotted;
    }

    &.divider-dotted {
        border-top-style: dotted;
    }

    &.divider-dashed {
        border-top-style: dashed;
    }

    &.divider-center-icon {
        margin: 2rem 0;

        >i {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background-color: #212729;
            color: $white;
            text-align: center;
            line-height: 2rem;
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 10;
            transform: translate(-50%, -50%);
        }

        &.border-primary {
            i {
                background-color: $primary;
            }
        }

        &.border-secondary {
            i {
                background-color: $secondary;
            }
        }

        &.border-success {
            i {
                background-color: $success;
            }
        }

        &.border-danger {
            i {
                background-color: $danger;
            }
        }

        &.border-warning {
            i {
                background-color: $warning;
            }
        }

        &.border-info {
            i {
                background-color: $info;
            }
        }

        &.border-light {
            i {
                background-color: $gray;
                color: $heading;
                border: 1px solid $border;
                line-height: 30px;
            }
        }

        &.border-dark {
            i {
                background-color: $dark;
            }
        }

        &.border-white {
            i {
                background-color: $white;
                color: $heading;
                border: 1px solid $border;
                line-height: 30px;
            }
        }
    }
}