<!-- Modal para Registro de Prospectos InteletGroup -->
<div class="modal fade" id="inteletGroupProspectModal" tabindex="-1" aria-labelledby="inteletGroupProspectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="inteletGroupProspectModalLabel">
                    <i class="bi bi-person-plus-fill me-2"></i>
                    Registro de Nuevo Prospecto - InteletGroup
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Mensajes de estado -->
                <div id="inteletgroup-message-container" class="mb-3" style="display: none;">
                    <div id="inteletgroup-success-message" class="alert alert-success d-flex align-items-center" style="display: none;">
                        <div class="me-3"><i class="bi bi-check-circle-fill" style="font-size: 1.5rem;"></i></div>
                        <div>
                            <h6 class="alert-heading mb-1">¡Operación exitosa!</h6>
                            <span class="message-text"></span>
                        </div>
                    </div>
                    <div id="inteletgroup-error-message" class="alert alert-danger d-flex align-items-center" style="display: none;">
                        <div class="me-3"><i class="bi bi-exclamation-triangle-fill" style="font-size: 1.5rem;"></i></div>
                        <div>
                            <h6 class="alert-heading mb-1">Error</h6>
                            <span class="message-text"></span>
                        </div>
                    </div>
                    <div id="inteletgroup-loading-message" class="alert alert-info d-flex align-items-center" style="display: none;">
                        <div class="me-3">
                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                        </div>
                        <div>
                            <span class="message-text">Procesando la solicitud...</span>
                        </div>
                    </div>
                </div>

                <!-- Formulario -->
                <form id="inteletGroupProspectForm" enctype="multipart/form-data">
                    <div class="row">
                        <!-- Información del Ejecutivo -->
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-person-badge me-2"></i>
                                Información del Ejecutivo
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="nombre_ejecutivo" class="form-label">
                                <i class="bi bi-person me-1"></i>
                                Nombre de Ejecutivo <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="nombre_ejecutivo" name="nombre_ejecutivo" 
                                   readonly style="background-color: #f8f9fa;">
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Información del Cliente -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-building me-2"></i>
                                Información del Cliente
                            </h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="rut_cliente" class="form-label">
                                <i class="bi bi-card-text me-1"></i>
                                RUT Cliente <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="rut_cliente" name="rut_cliente"
                                       placeholder="********-9" maxlength="12" required>
                                <button type="button" class="btn btn-outline-secondary" onclick="generarRutAleatorio()" title="Generar RUT aleatorio para pruebas">
                                    <i class="bi bi-shuffle"></i>
                                </button>
                            </div>
                            <div class="form-text">Formato: sin puntos, con guión (********-9)</div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="razon_social" class="form-label">
                                <i class="bi bi-building me-1"></i>
                                Razón Social <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="razon_social" name="razon_social" 
                                   placeholder="EMPRESA EJEMPLO LTDA" required>
                            <div class="form-text">Solo letras mayúsculas, sin acentos ni caracteres especiales</div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="rubro" class="form-label">
                                <i class="bi bi-briefcase me-1"></i>
                                Rubro <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="rubro" name="rubro" 
                                   placeholder="COMERCIO AL POR MENOR" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="direccion_comercial" class="form-label">
                                <i class="bi bi-geo-alt me-1"></i>
                                Dirección Comercial <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="direccion_comercial" name="direccion_comercial" 
                                      rows="2" placeholder="Av. Ejemplo 123, Comuna, Ciudad" required></textarea>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Información de Contacto -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-telephone me-2"></i>
                                Información de Contacto
                            </h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="telefono_celular" class="form-label">
                                <i class="bi bi-phone me-1"></i>
                                Teléfono Celular <span class="text-danger">*</span>
                            </label>
                            <input type="tel" class="form-control" id="telefono_celular" name="telefono_celular" 
                                   placeholder="987654321" minlength="9" maxlength="15" required>
                            <div class="form-text">Solo números, mínimo 9 dígitos</div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope me-1"></i>
                                Email <span class="text-danger">*</span>
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="<EMAIL>" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Información Bancaria -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-credit-card me-2"></i>
                                Información Bancaria y POS
                            </h6>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="numero_pos" class="form-label">
                                <i class="bi bi-credit-card-2-front me-1"></i>
                                N° de POS
                            </label>
                            <input type="text" class="form-control" id="numero_pos" name="numero_pos" 
                                   placeholder="POS123456">
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="tipo_cuenta" class="form-label">
                                <i class="bi bi-bank me-1"></i>
                                Tipo de Cuenta <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="tipo_cuenta" name="tipo_cuenta" required>
                                <option value="">Seleccionar...</option>
                                <option value="Cuenta Vista">Cuenta Vista</option>
                                <option value="Cuenta Corriente">Cuenta Corriente</option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="numero_cuenta_bancaria" class="form-label">
                                <i class="bi bi-bank2 me-1"></i>
                                N° de Cuenta Bancaria <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="numero_cuenta_bancaria" name="numero_cuenta_bancaria" 
                                   placeholder="********" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Información Operacional -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-clock me-2"></i>
                                Información Operacional
                            </h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="dias_atencion" class="form-label">
                                <i class="bi bi-calendar-week me-1"></i>
                                Días de Atención <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="dias_atencion" name="dias_atencion" 
                                   placeholder="Lunes a Viernes" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="horario_atencion" class="form-label">
                                <i class="bi bi-clock me-1"></i>
                                Horario de Atención <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="horario_atencion" name="horario_atencion" 
                                   placeholder="09:00 - 18:00" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="contrata_boleta" class="form-label">
                                <i class="bi bi-receipt me-1"></i>
                                Contrata Boleta <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="contrata_boleta" name="contrata_boleta" required>
                                <option value="">Seleccionar...</option>
                                <option value="Si">Sí</option>
                                <option value="No">No</option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="competencia_actual" class="form-label">
                                <i class="bi bi-building me-1"></i>
                                Competencia Actual <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="competencia_actual" name="competencia_actual" required>
                                <option value="">Seleccionar...</option>
                                <option value="Transbank">Transbank</option>
                                <option value="Getnet">Getnet</option>
                                <option value="Compra Aquí (Bco Estado)">Compra Aquí (Bco Estado)</option>
                                <option value="Klap">Klap</option>
                                <option value="SumUp">SumUp</option>
                                <option value="Tuu">Tuu</option>
                                <option value="Ya Ganaste">Ya Ganaste</option>
                                <option value="Mercado Pago">Mercado Pago</option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Documentación -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-file-earmark-arrow-up me-2"></i>
                                Documentación de Respaldo
                            </h6>
                        </div>

                        <div class="col-12 mb-3">
                            <label for="documentos" class="form-label">
                                <i class="bi bi-paperclip me-1"></i>
                                Adjuntar Documentos
                            </label>
                            <input type="file" class="form-control" id="documentos" name="documentos[]" 
                                   multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls">
                            <div class="form-text">
                                Formatos permitidos: PDF, DOC, DOCX, JPG, JPEG, PNG, XLSX, XLS. Máximo 5MB por archivo.
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    Cancelar
                </button>
                <button type="button" class="btn btn-warning" id="fillTestDataBtn">
                    <i class="bi bi-lightning me-1"></i>
                    Llenar Test
                </button>
                <button type="button" class="btn btn-primary position-relative" id="saveInteletGroupProspectBtn">
                    <span class="btn-text">
                        <i class="bi bi-save me-1"></i>
                        Guardar Prospecto
                    </span>
                    <span class="btn-loading" style="display: none;">
                        <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                        Guardando...
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>
