<?php
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Usuario no autenticado']);
    exit;
}

// Verificar que el usuario sea administrador (ID 4)
if ($_SESSION['usuario_id'] != 4) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'No tiene permisos para exportar registros']);
    exit;
}

// Incluir archivo de conexión
require_once '../con_db.php';

try {
    // Verificar que la función existe antes de usarla
    if (function_exists('createPDOConnection')) {
        $connection = createPDOConnection();
    } else {
        // Alternativa si la función no está disponible
        $connection = new PDO(
            "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
            'gestarse_ncornejo7_experian',
            'N1c0l7as17',
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
    }

    if (!$connection) {
        throw new Exception('No se pudo establecer conexión con la base de datos');
    }

    // Consulta para obtener todos los registros de clientes (usando nombres reales de columnas)
    $query = "SELECT
                id,
                tipo_cliente,
                rut,
                razon_social,
                nombre_representante1,
                rut_representante1,
                nombre_representante2,
                rut_representante2,
                nombre_representante3,
                rut_representante3,
                sistema_creacion,
                fecha_creacion,
                notaria,
                actividad_economica,
                fecha_constitucion,
                direccion,
                comuna,
                pagina_web,
                email,
                telefono,
                clasificacion_sii,
                contacto_nombre,
                contacto_rut,
                contacto_telefono,
                contacto_email,
                contacto_backup_nombre,
                contacto_backup_rut,
                contacto_backup_telefono,
                contacto_backup_email,
                morosos_plan,
                morosos_consultas,
                morosos_uf,
                morosos_descuento,
                morosos_nuevo_valor,
                advanced_plan,
                advanced_consultas,
                advanced_uf,
                advanced_descuento,
                advanced_nuevo_valor,
                key_user_nombre,
                key_user_rut,
                key_user_email,
                key_user_telefono,
                key_user_backup_nombre,
                key_user_backup_rut,
                key_user_backup_email,
                key_user_backup_telefono,
                ruta_ci,
                ruta_erut,
                ruta_extracto,
                ruta_ci_frente,
                ruta_ci_detras,
                ruta_carpeta_tributaria,
                ruta_consulta_terceros,
                fecha_creacion_registro,
                id_usuario
              FROM form_experian
              ORDER BY id DESC";

    $stmt = $connection->prepare($query);
    $stmt->execute();
    $clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Formatear los datos para la exportación
    $datosExportacion = [];
    foreach ($clientes as $cliente) {
        $datosExportacion[] = [
            'ID' => $cliente['id'],
            'Tipo Cliente' => $cliente['tipo_cliente'],
            'RUT' => $cliente['rut'],
            'Razón Social' => $cliente['razon_social'],
            'Nombre Representante 1' => $cliente['nombre_representante1'],
            'RUT Representante 1' => $cliente['rut_representante1'],
            'Nombre Representante 2' => $cliente['nombre_representante2'],
            'RUT Representante 2' => $cliente['rut_representante2'],
            'Nombre Representante 3' => $cliente['nombre_representante3'],
            'RUT Representante 3' => $cliente['rut_representante3'],
            'Sistema Creación' => $cliente['sistema_creacion'],
            'Fecha Creación' => $cliente['fecha_creacion'],
            'Notaría' => $cliente['notaria'],
            'Actividad Económica' => $cliente['actividad_economica'],
            'Fecha Constitución' => $cliente['fecha_constitucion'],
            'Dirección' => $cliente['direccion'],
            'Comuna' => $cliente['comuna'],
            'Página Web' => $cliente['pagina_web'],
            'Email' => $cliente['email'],
            'Teléfono' => $cliente['telefono'],
            'Clasificación SII' => $cliente['clasificacion_sii'],
            'Contacto Nombre' => $cliente['contacto_nombre'],
            'Contacto RUT' => $cliente['contacto_rut'],
            'Contacto Teléfono' => $cliente['contacto_telefono'],
            'Contacto Email' => $cliente['contacto_email'],
            'Contacto Backup Nombre' => $cliente['contacto_backup_nombre'],
            'Contacto Backup RUT' => $cliente['contacto_backup_rut'],
            'Contacto Backup Teléfono' => $cliente['contacto_backup_telefono'],
            'Contacto Backup Email' => $cliente['contacto_backup_email'],
            'Morosos Plan' => $cliente['morosos_plan'],
            'Morosos Consultas' => $cliente['morosos_consultas'],
            'Morosos UF' => $cliente['morosos_uf'],
            'Morosos Descuento' => $cliente['morosos_descuento'],
            'Morosos Nuevo Valor' => $cliente['morosos_nuevo_valor'],
            'Advanced Plan' => $cliente['advanced_plan'],
            'Advanced Consultas' => $cliente['advanced_consultas'],
            'Advanced UF' => $cliente['advanced_uf'],
            'Advanced Descuento' => $cliente['advanced_descuento'],
            'Advanced Nuevo Valor' => $cliente['advanced_nuevo_valor'],
            'Key User Nombre' => $cliente['key_user_nombre'],
            'Key User RUT' => $cliente['key_user_rut'],
            'Key User Email' => $cliente['key_user_email'],
            'Key User Teléfono' => $cliente['key_user_telefono'],
            'Key User Backup Nombre' => $cliente['key_user_backup_nombre'],
            'Key User Backup RUT' => $cliente['key_user_backup_rut'],
            'Key User Backup Email' => $cliente['key_user_backup_email'],
            'Key User Backup Teléfono' => $cliente['key_user_backup_telefono'],
            'Ruta CI' => $cliente['ruta_ci'],
            'Ruta ERUT' => $cliente['ruta_erut'],
            'Ruta Extracto' => $cliente['ruta_extracto'],
            'Ruta CI Frente' => $cliente['ruta_ci_frente'],
            'Ruta CI Detrás' => $cliente['ruta_ci_detras'],
            'Ruta Carpeta Tributaria' => $cliente['ruta_carpeta_tributaria'],
            'Ruta Consulta Terceros' => $cliente['ruta_consulta_terceros'],
            'Fecha Creación Registro' => $cliente['fecha_creacion_registro'],
            'ID Usuario' => $cliente['id_usuario']
        ];
    }

    // Log para depuración
    error_log("Exportar clientes: Se encontraron " . count($datosExportacion) . " registros");

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'data' => $datosExportacion,
        'total' => count($datosExportacion),
        'message' => 'Datos exportados correctamente'
    ]);

} catch (Exception $e) {
    error_log("Error en exportar_clientes.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al exportar los datos: ' . $e->getMessage()
    ]);
}
?>
