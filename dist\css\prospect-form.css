/* Estilos para el formulario de prospectos */

/* Modal base */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow-y: auto;
}

.prospect-modal .modal-content {
    background-color: #fefefe;
    margin: 2% auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    width: 90%;
    max-width: 1000px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header del modal */
.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #f1f1f1;
}

/* Body del modal */
.modal-body {
    padding: 30px;
    max-height: 70vh;
    overflow-y: auto;
}

/* Secciones del formulario */
.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.form-section h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Filas del formulario */
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.form-group {
    margin-bottom: 20px;
    padding: 0 10px;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

/* Etiquetas */
label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

/* Campos de entrada */
.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.error {
    border-color: #dc3545;
    background-color: #fff5f5;
}

.form-control.success {
    border-color: #28a745;
    background-color: #f8fff8;
}

/* Texto de ayuda */
.form-text {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: #6c757d;
}

/* Mensajes de error */
.error-message {
    color: #dc3545;
    font-size: 0.8rem;
    margin-top: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

/* Resumen de errores */
.error-summary {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    color: #721c24;
}

.error-summary h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.error-summary ul {
    margin: 0;
    padding-left: 20px;
}

/* Indicador de carga */
.loading-indicator {
    text-align: center;
    padding: 40px;
    background: rgba(255, 255, 255, 0.9);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1001;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Botones de acción */
.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
    text-align: right;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

/* Responsive */
@media (max-width: 768px) {
    .prospect-modal .modal-content {
        width: 95%;
        margin: 1% auto;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .col-md-4,
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .form-actions {
        text-align: center;
    }
    
    .btn {
        margin: 5px;
        width: 100%;
        max-width: 200px;
    }
}

/* Animaciones adicionales */
.form-control:valid {
    animation: fieldSuccess 0.3s ease;
}

@keyframes fieldSuccess {
    0% { background-color: #f8fff8; }
    100% { background-color: white; }
}

/* Estilos para campos requeridos */
label[for*="ejecutivo"]:after,
label[for*="rut_ejecutivo"]:after,
label[for*="razon_social"]:after,
label[for*="telefono"]:after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* Mejoras visuales */
.form-section:hover {
    background: #f1f3f4;
    transition: background-color 0.3s ease;
}

.form-control:hover {
    border-color: #b8c6db;
}

/* Estilos para archivos */
input[type="file"] {
    padding: 8px;
    background: white;
}

input[type="file"]::-webkit-file-upload-button {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

input[type="file"]::-webkit-file-upload-button:hover {
    background: #5a6fd8;
}
