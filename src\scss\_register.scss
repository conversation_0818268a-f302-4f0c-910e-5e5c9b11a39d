/* :: Register */

.login-wrapper {
    position: relative;
    width: 100%;
    min-height: 100vh;
    z-index: 10;
    overflow-y: auto;
    padding-top: 2rem;
    padding-bottom: 2rem;
    overflow-x: hidden;
    background-color: $white;

    .brand-logo {
        max-height: 26px;
    }

    .login-intro-img {
        max-height: 16rem;
    }
}

.login-back-button {
    position: fixed;
    top: 2rem;
    left: 28px;
    z-index: 100;

    a {
        display: block;
        line-height: 1;

        i {
            font-size: 2rem;
            line-height: 1;
        }
    }
}

.register-form {
    .progress {
        width: 100%;
        height: 5px;
        margin-top: 1rem;
        border-radius: 1rem;
        margin-bottom: 0.25rem;
    }

    .password-score,
    .password-recommendation {
        display: none !important;
    }

    #password-recommendation-heading {
        font-weight: 700;
        color: $success;
        font-size: 16px;
    }
}

#password-visibility {
    top: 50%;
    right: 1rem;
    z-index: 10;
    background-color: transparent;
    cursor: pointer;
    transform: translateY(-50%);

    i {
        color: $heading;
        font-size: 1.125rem;
    }

    i.bi-eye-slash {
        display: none;
    }

    &.active {
        i.bi-eye-slash {
            display: block;
        }

        i.bi-eye {
            display: none;
        }
    }
}

.login-meta-data {
    a {
        font-size: 14px;
        color: $heading;
        font-weight: 500;

        &:hover,
        &:focus {
            color: $primary;
        }
    }
}

.otp-form {
    position: relative;
    z-index: 1;

    select {
        flex: 0 0 90px;
        max-width: 90px;
        width: 90px;
        text-align: left;
    }
}

.otp-verify-form {
    position: relative;
    z-index: 1;
    text-align: center;

    .form-control {
        font-weight: 700;
        text-align: center;
    }
}

.otp-sec {
    color: $heading;
    font-weight: 700;
}