<?php 
session_start();



header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.


$inc = include("con_db.php");

$usuarioForm2 = $_GET['usuario'];


?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Affan - PWA Mobile HTML Template">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

    <meta name="theme-color" content="#0134d4">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">

    <!-- Title -->
    <title>APP TQW</title>

    <!-- Favicon -->
    <link rel="icon" href="img/core-img/logo_con.ico">
    <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
    <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
    <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

    <!-- Style CSS -->
    <link rel="stylesheet" href="style.css">

    <!-- Web App Manifest -->
    <link rel="manifest" href="manifest.json">
</head>

<body>
    <!-- Preloader -->
    <div id="preloader">
        <div class="spinner-grow text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Internet Connection Status -->
    <div class="internet-connection-status" id="internetStatus"></div>

    <!-- Dark mode switching -->
    <div class="dark-mode-switching">
        <div class="d-flex w-100 h-100 align-items-center justify-content-center">
            <div class="dark-mode-text text-center">
                <i class="bi bi-moon"></i>
                <p class="mb-0">Switching to dark mode</p>
            </div>
            <div class="light-mode-text text-center">
                <i class="bi bi-brightness-high"></i>
                <p class="mb-0">Switching to light mode</p>
            </div>
        </div>
    </div>

    <!-- RTL mode switching -->
    <div class="rtl-mode-switching">
        <div class="d-flex w-100 h-100 align-items-center justify-content-center">
            <div class="rtl-mode-text text-center">
                <i class="bi bi-text-right"></i>
                <p class="mb-0">Switching to RTL mode</p>
            </div>
            <div class="ltr-mode-text text-center">
                <i class="bi bi-text-left"></i>
                <p class="mb-0">Switching to default mode</p>
            </div>
        </div>
    </div>

    <!-- Setting Popup Overlay -->
    <div id="setting-popup-overlay"></div>

    <!-- Setting Popup Card -->
    <div class="card setting-popup-card shadow-lg" id="settingCard">
        <div class="card-body">
            <div class="container">
                <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
                    <p class="mb-0">Settings</p>
                    <div class="btn-close" id="settingCardClose"></div>
                </div>

                <div class="single-setting-panel">
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
                        <label class="form-check-label" for="availabilityStatus">Availability status</label>
                    </div>
                </div>

                <div class="single-setting-panel">
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
                        <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
                    </div>
                </div>

                <div class="single-setting-panel">
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input" type="checkbox" id="darkSwitch">
                        <label class="form-check-label" for="darkSwitch">Dark mode</label>
                    </div>
                </div>

                <div class="single-setting-panel">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="rtlSwitch">
                        <label class="form-check-label" for="rtlSwitch">RTL mode</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Header Area -->
    <div class="header-area" id="headerArea">
        <div class="container">
            <!-- Header Content -->
            <div class="header-content position-relative d-flex align-items-center justify-content-between">
                <!-- Back Button -->
                <div class="back-button">
                <a href="SoporteCalidad.php?usuario=<?php echo $usuarioForm2; ?>"> 
                        <i class="bi bi-arrow-left-short"></i>
                    </a>
                </div>

                


                <!-- Page Title -->
                <div class="page-heading">
                    <h6 class="mb-0">FORMULARIO CIERRE ASEGURADO</h6>
                </div>

                <!-- Settings -->
                <!-- <div class="setting-wrapper">
                    <div class="setting-trigger-btn" id="settingTriggerBtn">
                        <i class="bi bi-gear"></i>
                        <span></span>
                    </div>
                </div> -->
            </div>
        </div>
    </div>

    <div class="page-content-wrapper py-3">
        <!-- Element Heading -->
        <div class="container">
            <div class="element-heading">
                </div>
        </div>

        <div class="container">
            <div class="card">
                <div class="card-body">
                    <form action="Action_SoporteCierreAseg.php" method="POST">
                        
                        
                    <div class="form-group">
                        <!-- <label class="form-label" for="exampleInputText">EJECUTIVO</label> -->
                        <input class="form-control" id="usuario" name="usuario" type="hidden"  value=<?php echo $usuarioForm2 ;?> >
                    </div>

                        <div class="form-group">
                            <label class="form-label" for="exampleInputText">Orden</label>
                            <input class="form-control" id="orden" name="orden" type="text">
                        </div>



                        <div class="form-group">
                            <label class="form-label" for="defaultSelectLg">¿Su requerimiento fue solucionado?</label>
                            <select class="form-select form-select-lg" id="reque" name="reque"
                                aria-label="Default select example">



                                <option value="Si">Si</option>
                                <option value="No">No</option>
                                <option value="Sin Contacto">Sin Contacto</option>
                                <option value="Tercero">Quien contesta no recibió al tecnico</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="exampleInputpassword">Describa el motivo</label>
                            <input class="form-control" id="motivo" name="motivo" type="text">
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="exampleInputnumber">¿En que categoria clasificaría la
                                disconformidad del cliente</label>
                            <input class="form-control" id="segmento" name="segmento" type="text">
                        </div>



                        <div class="container">
                            <!-- Rating Card -->
                            <div class="card">
                                <div class="card-body py-5">
                                    <div class="rating-card-three text-center">
                                        <h6 class="mb-3">¿Como calificaría la atención entregada por el técnico?</h6>

                                        <div class="stars">
                                            <input class="stars-checkbox" id="first-star" type="radio" name="star" value="1">
                                            <label class="stars-star" for="first-star">
                                                <svg class="star-icon" id="star1" version="1.1"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                                    viewbox="0 0 53.867 53.867"
                                                    style="enable-background:new 0 0 53.867 53.867;"
                                                    xml:space="preserve">
                                                    <polygon
                                                        points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                                                    </polygon>
                                                </svg>
                                            </label>

                                            <input class="stars-checkbox" id="second-star" type="radio" name="star" value="2">
                                            <label class="stars-star" for="second-star">
                                                <svg class="star-icon" id="star2" version="1.1"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                                    viewbox="0 0 53.867 53.867"
                                                    style="enable-background:new 0 0 53.867 53.867;"
                                                    xml:space="preserve">
                                                    <polygon
                                                        points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                                                    </polygon>
                                                </svg>
                                            </label>

                                            <input class="stars-checkbox" id="third-star" type="radio" name="star" value="3">
                                            <label class="stars-star" for="third-star">
                                                <svg class="star-icon" id="star3" version="1.1"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                                    viewbox="0 0 53.867 53.867"
                                                    style="enable-background:new 0 0 53.867 53.867;"
                                                    xml:space="preserve">
                                                    <polygon
                                                        points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                                                    </polygon>
                                                </svg>
                                            </label>

                                            <input class="stars-checkbox" id="fourth-star" type="radio" name="star" value="4">
                                            <label class="stars-star" for="fourth-star">
                                                <svg class="star-icon" id="star4" version="1.1"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                                    viewbox="0 0 53.867 53.867"
                                                    style="enable-background:new 0 0 53.867 53.867;"
                                                    xml:space="preserve">
                                                    <polygon
                                                        points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                                                    </polygon>
                                                </svg>
                                            </label>

                                            <input class="stars-checkbox" id="fifth-star" type="radio" name="star" value="5">
                                            <label class="stars-star" for="fifth-star">
                                                <svg class="star-icon" id="star5" version="1.1"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                                    viewbox="0 0 53.867 53.867"
                                                    style="enable-background:new 0 0 53.867 53.867;"
                                                    xml:space="preserve">
                                                    <polygon
                                                        points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                                                    </polygon>
                                                </svg>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>




                        <input type="submit"
                            class="btn btn-primary w-100 d-flex align-items-center justify-content-center">

                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Nav -->
    <div class="footer-nav-area" id="footerNav">
        <div class="container px-0">
            <!-- Footer Content -->
            <div class="footer-nav position-relative">
                <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
                <li>
        <a href="SoporteCalidad.php?usuario=<?php echo $usuarioForm2; ?>">      
              <i class="bi bi-collection"></i>
              <span>Rev Calidad React</span>
            </a>
          </li>


                </ul>
            </div>
        </div>
    </div>

    <!-- All JavaScript Files -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/slideToggle.min.js"></script>
    <script src="js/internet-status.js"></script>
    <script src="js/tiny-slider.js"></script>
    <script src="js/venobox.min.js"></script>
    <script src="js/countdown.js"></script>
    <script src="js/rangeslider.min.js"></script>
    <script src="js/vanilla-dataTables.min.js"></script>
    <script src="js/index.js"></script>
    <script src="js/imagesloaded.pkgd.min.js"></script>
    <script src="js/isotope.pkgd.min.js"></script>
    <script src="js/dark-rtl.js"></script>
    <script src="js/active.js"></script>
    <script src="js/pwa.js"></script>
</body>

</html>