<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Configurar cabeceras para respuesta JSON
header('Content-Type: application/json');

// Incluir el archivo de conexión
require_once 'con_db.php';

// Verificar si se recibió el parámetro del plan
if (!isset($_POST['plan']) || empty($_POST['plan'])) {
    echo json_encode([
        'success' => false,
        'message' => 'No se especificó un plan'
    ]);
    exit;
}

// Obtener el plan seleccionado
$plan = $_POST['plan'];

try {
    // Consultar los detalles del plan en la base de datos
    $stmt = $mysqli->prepare("SELECT uf_transaccion, uf_mensual FROM tb_experian_informe_advanced_sme WHERE plan = ?");
    $stmt->bind_param("i", $plan);
    $stmt->execute();
    
    // En lugar de usar get_result(), usamos bind_result() para obtener los valores
    $stmt->bind_result($uf_transaccion, $uf_mensual);
    
    if ($stmt->fetch()) {
        echo json_encode([
            'success' => true,
            'data' => [
                'uf_transaccion' => $uf_transaccion,
                'uf_mensual' => $uf_mensual
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No se encontraron datos para el plan seleccionado'
        ]);
    }
    
    $stmt->close();
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error al consultar los datos: ' . $e->getMessage()
    ]);
}

// Cerrar la conexión
$mysqli->close();
?>
