<?php
// Script de prueba para verificar el comportamiento de RUTs
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    die('Acceso denegado. Solo usuarios de InteletGroup pueden ejecutar este script.');
}

require_once 'con_db.php';
$conexion = $mysqli;

if (!isset($conexion) || $conexion->connect_error) {
    die('Error de conexión a la base de datos');
}

// Obtener todos los RUTs existentes
$sql = "SELECT id, rut_cliente, razon_social, fecha_registro FROM tb_inteletgroup_prospectos ORDER BY fecha_registro DESC LIMIT 20";
$result = $conexion->query($sql);

echo "<h2>RUTs existentes en la base de datos:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>RUT</th><th>Razón Social</th><th>Fecha Registro</th></tr>";

$ruts_existentes = [];
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($row['id']) . "</td>";
    echo "<td>" . htmlspecialchars($row['rut_cliente']) . "</td>";
    echo "<td>" . htmlspecialchars($row['razon_social']) . "</td>";
    echo "<td>" . htmlspecialchars($row['fecha_registro']) . "</td>";
    echo "</tr>";
    $ruts_existentes[] = $row['rut_cliente'];
}
echo "</table>";

// Función para generar RUT aleatorio
function generarRutAleatorio() {
    $numeroBase = rand(10000000, 99999999);
    $dv = calcularDigitoVerificador($numeroBase);
    return $numeroBase . '-' . $dv;
}

function calcularDigitoVerificador($rut) {
    $suma = 0;
    $multiplicador = 2;
    
    $rutStr = strval($rut);
    for ($i = strlen($rutStr) - 1; $i >= 0; $i--) {
        $suma += intval($rutStr[$i]) * $multiplicador;
        $multiplicador = $multiplicador === 7 ? 2 : $multiplicador + 1;
    }
    
    $resto = $suma % 11;
    $dv = 11 - $resto;
    
    if ($dv === 11) return '0';
    if ($dv === 10) return 'K';
    return strval($dv);
}

// Generar algunos RUTs de prueba
echo "<h2>RUTs de prueba generados:</h2>";
echo "<ul>";
for ($i = 0; $i < 5; $i++) {
    $rutPrueba = generarRutAleatorio();
    $existe = in_array($rutPrueba, $ruts_existentes);
    echo "<li>$rutPrueba " . ($existe ? "<strong style='color: red;'>(YA EXISTE)</strong>" : "<span style='color: green;'>(DISPONIBLE)</span>") . "</li>";
}
echo "</ul>";

// Verificar un RUT específico si se proporciona
if (isset($_GET['rut'])) {
    $rutVerificar = $_GET['rut'];
    echo "<h2>Verificación de RUT: $rutVerificar</h2>";
    
    $sql_verificar = "SELECT id, fecha_registro, rut_cliente FROM tb_inteletgroup_prospectos WHERE rut_cliente = ?";
    $stmt = $conexion->prepare($sql_verificar);
    $stmt->bind_param("s", $rutVerificar);
    $stmt->execute();
    
    $existing_id = null;
    $fecha_registro = null;
    $rut_encontrado = null;
    $stmt->bind_result($existing_id, $fecha_registro, $rut_encontrado);
    $stmt->fetch();
    $stmt->close();
    
    if ($existing_id !== null) {
        echo "<p style='color: red;'><strong>RUT ENCONTRADO:</strong> ID $existing_id, registrado el $fecha_registro</p>";
    } else {
        echo "<p style='color: green;'><strong>RUT NO ENCONTRADO:</strong> Disponible para registro</p>";
    }
}

echo "<hr>";
echo "<h3>Herramientas:</h3>";
echo "<p><a href='?rut=" . generarRutAleatorio() . "'>Verificar RUT aleatorio</a></p>";
echo "<p><a href='limpiar_datos_prueba.php'>Limpiar datos de prueba</a></p>";
echo "<p><a href='form_inteletgroup.php'>Volver al panel</a></p>";
?>
