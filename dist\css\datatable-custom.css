/* Estilos personalizados para DataTables */
.dt-buttons {
    margin-bottom: 15px;
}

.export-button {
    background-color: #4CAF50 !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    margin-right: 5px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-weight: bold !important;
}

.export-button:hover {
    background-color: #45a049 !important;
}

.dataTables_wrapper .dataTables_filter {
    margin-bottom: 15px;
}

.dataTables_wrapper .dataTables_filter input {
    padding: 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Estilos para los iconos de ordenamiento */
table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc {
    cursor: pointer;
    position: relative;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after {
    position: absolute;
    bottom: 8px;
    right: 8px;
    opacity: 0.5;
}

/* Mejorar la visibilidad en tablas con muchas columnas */
.dataTables_scrollHead,
.dataTables_scrollBody {
    width: 100% !important;
}

/* Aumentar contraste en las filas alternas */
table.dataTable tbody tr.odd {
    background-color: #f9f9f9;
}

/* Estilos para los headers ordenables */
.sortable {
    cursor: pointer;
    position: relative;
    padding-right: 20px !important;
}

.sortable::after {
    content: '↕';
    position: absolute;
    right: 5px;
    color: #999;
}

.sortable.asc::after {
    content: '↑';
    color: #333;
}

.sortable.desc::after {
    content: '↓';
    color: #333;
}

/* Efecto hover para los headers ordenables */
.sortable:hover {
    background-color: #f5f5f5;
}
