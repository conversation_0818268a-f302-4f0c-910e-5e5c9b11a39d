<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Rating</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Rating 01</h6>
      </div>
    </div>

    <div class="container">
      <!-- Rating Card -->
      <div class="card">
        <div class="card-body">
          <div class="rating-card-one">
            <div class="d-flex align-items-center justify-content-between">
              <div class="rating">
                <a href="#"><i class="bi bi-star-fill"></i></a>
                <a href="#"><i class="bi bi-star-fill"></i></a>
                <a href="#"><i class="bi bi-star-fill"></i></a>
                <a href="#"><i class="bi bi-star-fill"></i></a>
                <a href="#"><i class="bi bi-star-half"></i></a>
              </div>
              <span>4.65 out of 5</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Rating 02</h6>
      </div>
    </div>

    <div class="container">
      <!-- Rating Card -->
      <div class="card">
        <div class="card-body">
          <div class="rating-card-two">
            <!-- Rating result -->
            <div class="d-flex align-items-center justify-content-between mb-3 border-bottom pb-2">
              <div class="rating">
                <a href="#"><i class="bi bi-star-fill"></i></a>
                <a href="#"><i class="bi bi-star-fill"></i></a>
                <a href="#"><i class="bi bi-star-fill"></i></a>
                <a href="#"><i class="bi bi-star-fill"></i></a>
                <a href="#"><i class="bi bi-star-half"></i></a>
              </div>
              <span>4.44 out of 5 ratings</span>
            </div>

            <!-- Rating Details -->
            <div class="rating-detail">
              <!-- Single Rating Details -->
              <div class="d-flex align-items-center mt-2">
                <span>5 star</span>
                <div class="progress-bar-wrapper">
                  <div class="progress">
                    <div class="progress-bar bg-warning" style="width: 78%;" role="progressbar" aria-valuenow="78"
                      aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                </div>
                <span>78%</span>
              </div>

              <!-- Single Rating Details -->
              <div class="d-flex align-items-center mt-2">
                <span>4 star</span>
                <div class="progress-bar-wrapper">
                  <div class="progress">
                    <div class="progress-bar bg-warning" style="width: 14%;" role="progressbar" aria-valuenow="14"
                      aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                </div>
                <span>14%</span>
              </div>

              <!-- Single Rating Details -->
              <div class="d-flex align-items-center mt-2">
                <span>3 star</span>
                <div class="progress-bar-wrapper">
                  <div class="progress">
                    <div class="progress-bar bg-warning" style="width: 8%;" role="progressbar" aria-valuenow="8"
                      aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                </div>
                <span>8%</span>
              </div>

              <!-- Single Rating Details -->
              <div class="d-flex align-items-center mt-2">
                <span>2 star</span>
                <div class="progress-bar-wrapper">
                  <div class="progress">
                    <div class="progress-bar bg-warning" style="width: 0%;" role="progressbar" aria-valuenow="0"
                      aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                </div>
                <span>0%</span>
              </div>

              <!-- Single Rating Details -->
              <div class="d-flex align-items-center mt-2">
                <span>1 star</span>
                <div class="progress-bar-wrapper">
                  <div class="progress">
                    <div class="progress-bar bg-warning" style="width: 0%;" role="progressbar" aria-valuenow="0"
                      aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                </div>
                <span>0%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Rating 03</h6>
      </div>
    </div>

    <div class="container">
      <!-- Rating Card -->
      <div class="card">
        <div class="card-body py-5">
          <div class="rating-card-three text-center">
            <h6 class="mb-3">How was your experience with us?</h6>

            <div class="stars">
              <input class="stars-checkbox" id="first-star" type="radio" name="star">
              <label class="stars-star" for="first-star">
                <svg class="star-icon" id="star1" version="1.1" xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewbox="0 0 53.867 53.867"
                  style="enable-background:new 0 0 53.867 53.867;" xml:space="preserve">
                  <polygon
                    points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                  </polygon>
                </svg>
              </label>

              <input class="stars-checkbox" id="second-star" type="radio" name="star">
              <label class="stars-star" for="second-star">
                <svg class="star-icon" id="star2" version="1.1" xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewbox="0 0 53.867 53.867"
                  style="enable-background:new 0 0 53.867 53.867;" xml:space="preserve">
                  <polygon
                    points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                  </polygon>
                </svg>
              </label>

              <input class="stars-checkbox" id="third-star" type="radio" name="star">
              <label class="stars-star" for="third-star">
                <svg class="star-icon" id="star3" version="1.1" xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewbox="0 0 53.867 53.867"
                  style="enable-background:new 0 0 53.867 53.867;" xml:space="preserve">
                  <polygon
                    points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                  </polygon>
                </svg>
              </label>

              <input class="stars-checkbox" id="fourth-star" type="radio" name="star">
              <label class="stars-star" for="fourth-star">
                <svg class="star-icon" id="star4" version="1.1" xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewbox="0 0 53.867 53.867"
                  style="enable-background:new 0 0 53.867 53.867;" xml:space="preserve">
                  <polygon
                    points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                  </polygon>
                </svg>
              </label>

              <input class="stars-checkbox" id="fifth-star" type="radio" name="star">
              <label class="stars-star" for="fifth-star">
                <svg class="star-icon" id="star5" version="1.1" xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewbox="0 0 53.867 53.867"
                  style="enable-background:new 0 0 53.867 53.867;" xml:space="preserve">
                  <polygon
                    points="26.934,1.318 35.256,18.182 53.867,20.887 40.4,34.013 43.579,52.549 26.934,43.798 10.288,52.549 13.467,34.013 0,20.887 18.611,18.182">
                  </polygon>
                </svg>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>