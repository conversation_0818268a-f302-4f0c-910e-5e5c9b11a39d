<?php
// Conexión a la base de datos


$inc = include("con_db.php");
session_start();

// Obtener los datos del formulario
$orden = $_POST['Orden'];
$categoria = $_POST['Categoria'];
$Obse = $_POST['Obse'];
$usuario = $_POST['Usuario'];
$aplica = $_POST['Aplica'];
$orden2 = $_POST['Orden2'];


//datos del arhivo
$nombre_archivo = $_FILES['userfile']['name'];
$tipo_archivo = $_FILES['userfile']['type'];
$tamano_archivo = $_FILES['userfile']['size'];
  

// Realizar la consulta SQL para insertar los datos en la tabla
$sql = "INSERT INTO tb_solicitud_excep_cal 
(orden, mot_gen_tecn,motivo,tecnico,orden2,rev_super) 
VALUES ('$orden', '$categoria', '$Obse','$usuario','$orden2','PENDIENTE')";



//compruebo si las características del archivo son las que deseo
if (!((strpos($tipo_archivo, "gif") || strpos($tipo_archivo, "jpeg") || strpos($tipo_archivo, "pdf")) && ($tamano_archivo < 10000000))) 
{
    echo "La extensión o el tamaño de los archivos no es correcta. <br><br><table><tr><td><li>Se permiten archivos .gif o .jpg<br><li>se permiten archivos de 100 Kb máximo.</td></tr></table>";
    echo "<script>alert('La extensión o el tamaño de los archivos no es correcta');window.history.back();</script>";


}else{
    if (move_uploaded_file($_FILES['userfile']['tmp_name'], 'OTDIGITAL/'.$_POST["Orden"].'.pdf')){
    {                    // Ejecutar la consulta
                        if (mysqli_query($conex, $sql)) {
                            echo "<script>alert('Datos guardados correctamente');</script>";
                            ?>

                                <script type="text/javascript">
                                window.location = 'Tecnico_SolicitudExcep.php'
                                </script>

                                <?php    

                        } else {
                            echo "Error al guardar los datos: " . mysqli_error($conex);
                        }
                    }
    }else{
        echo "<script>alert('Error al cargar los datos');window.history.back();</script>";
    }
}





// Cerrar la conexión
mysqli_close($conex);
?>