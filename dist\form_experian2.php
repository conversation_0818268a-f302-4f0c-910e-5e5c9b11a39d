<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Inicializar variables importantes
$es_admin = false; // Variable que indica si el usuario es administrador

// Aplicar headers anti-caché
no_cache_headers();

// Verificación mejorada de autenticación
function checkAuth() {
    if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
        // Registrar el intento fallido
        error_log("[" . date('Y-m-d H:i:s') . "] Error de autenticación - Usuario no identificado - IP: " . $_SERVER['REMOTE_ADDR']);

        // Redireccionar a login con mensaje de error
        $errorMsg = urlencode("Usuario no autenticado. Inicie sesión para continuar.");
        header('Location: ' . version_url('login.php?error=' . $errorMsg));
        exit;
    }

    // Renovar la sesión para evitar su caducidad prematura
    $_SESSION['last_activity'] = time();
    return true;
}

// Verificar si el usuario está autenticado
checkAuth();

// Incluir el archivo de conexión
require_once 'con_db.php';

// Usar la función createPDOConnection() del archivo con_db.php
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulario de Cliente</title>
    <?php echo no_cache_meta(); ?>
    <link rel="stylesheet" href="<?php echo version_url('css/form_experian.css'); ?>">
    <!-- Estilos para tablas ordenables -->
    <link rel="stylesheet" href="<?php echo version_url('css/table-sort.css'); ?>">
    <!-- Estilos para notificaciones -->
    <link rel="stylesheet" href="<?php echo version_url('css/notifications.css'); ?>">
    <!-- Agregar Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Cargar jQuery primero -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Agregar SheetJS (versión completa y estable) -->
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- Sistema de notificaciones -->
    <script src="<?php echo version_url('js/notifications.js'); ?>"></script>
    <script>
        // Verificar que jQuery se cargó correctamente
        window.onload = function() {
            if (typeof jQuery === "undefined") {
                alert("jQuery no se cargó correctamente");
            } else {
                console.log("jQuery cargado correctamente: " + jQuery.fn.jquery);
            }
            // Verificar la carga de SheetJS
            if (typeof XLSX === "undefined") {
                console.error("Error: SheetJS no se cargó correctamente");
            } else {
                console.log("SheetJS cargado correctamente");
            }
        };

        // Definir la función cargarEjecutivos si no existe
        function cargarEjecutivos(forzarRecarga = false) {
          // Mostrar indicador de carga
          $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="loading-data">Cargando datos...</td></tr>');

          // Agregar timestamp para evitar caché
          var timestamp = new Date().getTime();

          console.log('Cargando ejecutivos con forzarRecarga =', forzarRecarga);
          console.log('Usuario es admin:', window.userIsAdmin);

          $.ajax({
            url: 'endpoints/obtener_prospectos.php?t=' + timestamp, // Agregar timestamp para evitar caché
            type: 'GET',
            dataType: 'json',
            cache: false, // Siempre evitar caché
            beforeSend: function() {
              console.log('Enviando solicitud a endpoints/obtener_prospectos.php');
            },
            success: function(response) {
              console.log('Respuesta recibida:', response);
              if (response.success) {
                console.log('Datos recibidos correctamente. Registros:', response.data.length);
                actualizarTablaProspectos(response.data);
              } else {
                console.error('Error en la respuesta:', response.message);
                $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="error-data">Error: ' + response.message + '</td></tr>');
              }
            },
            error: function(xhr, status, error) {
              console.error('Error AJAX:', {
                status: status,
                error: error,
                response: xhr.responseText
              });
              $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="error-data">Error de conexión</td></tr>');
            }
          });
        }

        // Función para actualizar la tabla de prospectos
        function actualizarTablaProspectos(data) {
          if (!data || data.length === 0) {
            $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="no-data">No hay datos disponibles</td></tr>');
            return;
          }

          var html = '';
          $.each(data, function(index, prospecto) {
            // Usar los datos de la última bitácora si están disponibles
            var estado = prospecto.ultimo_estado || prospecto.estado;
            var observaciones = prospecto.ultima_observacion || prospecto.observaciones || "";
            var fechaRegistro = prospecto.ultima_fecha_gestion || prospecto.fecha_registro;

            html += '<tr>';
            html += '<td><button class="btn-bitacora" data-rut="' + prospecto.rut_ejecutivo + '" data-nombre="' + prospecto.nombre_ejecutivo + '" data-razon="' + prospecto.razon_social + '"><i class="fa fa-book"></i> Bitácora</button></td>';
            html += '<td>' + prospecto.nombre_ejecutivo + '</td>';
            html += '<td>' + prospecto.rut_ejecutivo + '</td>';
            html += '<td>' + prospecto.razon_social + '</td>';
            html += '<td>' + prospecto.rubro + '</td>';
            html += '<td>' + prospecto.contacto + '</td>';
            html += '<td>' + prospecto.telefono + '</td>';
            html += '<td>' + prospecto.fecha + '</td>';
            html += '<td>' + estado + '</td>';
            html += '<td>' + observaciones + '</td>';
            html += '<td>' + fechaRegistro + '</td>';
            html += '</tr>';
          });

          $('#ejecutivos-table tbody').html(html);
        }

        // Función para cargar la bitácora de un prospecto
        function cargarBitacora(rut) {
          if (!rut) return;

          // Mostrar indicador de carga
          $('#bitacora-timeline').html('<div class="loading-data">Cargando actividades...</div>');

          // Definir el flujo de estados en orden
          var flujoEstados = [
            "Envio información",
            "Negociación",
            "Cerrado",
            "B.O. Experian",
            "Proceso de Firma",
            "Firmado",
            "Habilitado"
          ];

          $.ajax({
            url: 'endpoints/obtener_bitacora.php?rut=' + encodeURIComponent(rut),
            type: 'GET',
            dataType: 'json',
            cache: false,
            success: function(response) {
              if (response && response.success) {
                // Filtrar las opciones del select según el último estado
                if (response.ultimo_estado) {
                  var selectEstado = document.getElementById('bitacora_estado');
                  if (selectEstado) {
                    var indiceUltimo = flujoEstados.indexOf(response.ultimo_estado);

                    if (indiceUltimo !== -1) {
                      // Guardar todas las opciones originales si no lo hemos hecho antes
                      if (!selectEstado.dataset.opcionesOriginales) {
                        var opcionesOriginales = [];
                        for (var i = 0; i < selectEstado.options.length; i++) {
                          opcionesOriginales.push({
                            value: selectEstado.options[i].value,
                            text: selectEstado.options[i].text
                          });
                        }
                        selectEstado.dataset.opcionesOriginales = JSON.stringify(opcionesOriginales);
                      }

                      // Limpiar el select
                      selectEstado.innerHTML = '<option value="">Seleccione...</option>';

                      // Agregar solo las opciones válidas (igual o posterior al último estado)
                      for (var j = indiceUltimo; j < flujoEstados.length; j++) {
                        var option = document.createElement('option');
                        option.value = flujoEstados[j];
                        option.text = flujoEstados[j];
                        selectEstado.appendChild(option);
                      }
                    }
                  }
                }

                // Mostrar los registros en el timeline
                if (!response.data || response.data.length === 0) {
                  $('#bitacora-timeline').html('<div class="no-data">No hay registros disponibles</div>');
                } else {
                  let html = '';
                  $.each(response.data, function(index, registro) {
                    // Determinar la clase de estado para el color del punto
                    let estadoClass = 'estado-default';
                    if (registro.estado.toLowerCase().includes('pendiente')) {
                      estadoClass = 'estado-pendiente';
                    } else if (registro.estado.toLowerCase().includes('proceso') || registro.estado.toLowerCase().includes('en curso')) {
                      estadoClass = 'estado-en-proceso';
                    } else if (registro.estado.toLowerCase().includes('completado') || registro.estado.toLowerCase().includes('finalizado')) {
                      estadoClass = 'estado-completado';
                    } else if (registro.estado.toLowerCase().includes('cancelado') || registro.estado.toLowerCase().includes('rechazado')) {
                      estadoClass = 'estado-cancelado';
                    }

                    // Formatear la fecha para mostrar en formato corto
                    let fechaRegistro = new Date(registro.fecha_registro);
                    let fechaFormateada = fechaRegistro.toLocaleDateString('es-CL') + ' ' +
                                        fechaRegistro.toLocaleTimeString('es-CL', {hour: '2-digit', minute:'2-digit'});

                    html += '<div class="timeline-item ' + estadoClass + '">';
                    html += '  <div class="timeline-content">';
                    html += '    <div class="timeline-header">';
                    html += '      <div class="timeline-text"><span class="highlight">' + registro.estado + '</span></div>';
                    html += '      <div class="timeline-time">' + fechaFormateada + '</div>';
                    html += '    </div>';
                    html += '    <div class="timeline-subtext">' + registro.observaciones + '</div>';
                    html += '    <div class="timeline-footer">';
                    html += '      <div>Registrado por: <span class="highlight">' + (registro.nombre_usuario || 'N/A') + '</span></div>';
                    html += '    </div>';
                    html += '  </div>';
                    html += '</div>';
                  });
                  $('#bitacora-timeline').html(html);
                }
              } else {
                $('#bitacora-timeline').html('<div class="error-data">Error: ' + (response ? response.message : 'Respuesta inválida') + '</div>');
              }
            },
            error: function() {
              $('#bitacora-timeline').html('<div class="error-data">Error de conexión al cargar bitácora</div>');
            }
          });
        }
    </script>
    <!-- Se eliminaron las referencias a DataTables -->
</head>
<body>
<?php
// Obtener el nombre del usuario desde la base de datos
$nombre_usuario = '';
try {
    // Verificar que la función existe antes de usarla
    if (function_exists('createPDOConnection')) {
        $connection = createPDOConnection();
    } else {
        // Alternativa si la función no está disponible
        $connection = new PDO(
            "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
            'gestarse_ncornejo7_experian',
            'N1c0l7as17',
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
    }
    if ($connection) {
        $query = "SELECT correo, COALESCE(nombre_usuario, SUBSTRING_INDEX(correo, '@', 1)) AS nombre_usuario FROM tb_experian_usuarios WHERE id = ?";
        $stmt = $connection->prepare($query);
        $stmt->execute([$_SESSION['usuario_id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && isset($result['nombre_usuario']) && !empty($result['nombre_usuario'])) {
            $nombre_usuario = $result['nombre_usuario'];
            // Usar el nombre completo sin dividirlo
            $primer_nombre = $nombre_usuario;
        } else {
            // Si no hay nombre_usuario, usar el correo como alternativa
            $nombre_usuario = $_SESSION['usuario'];
            $primer_nombre = $nombre_usuario;
        }
    }
} catch (Exception $e) {
    error_log("Error al obtener nombre de usuario: " . $e->getMessage());
    // En caso de error, usar el correo como alternativa
    $nombre_usuario = $_SESSION['usuario'];
    $primer_nombre = $nombre_usuario;
}
?>
<div class="site-header">
    <div class="header-container">
        <div class="logo-container">
            <img src="<?php echo version_url('img/img_experian/logo.jpg'); ?>" alt="Logo Experian" class="header-logo">
        </div>
        <div class="user-info">
            <span class="user-name"><?php echo htmlspecialchars($primer_nombre); ?></span>
        </div>
        <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Salir</a>
    </div>
</div>

<!-- Loader global para indicar carga -->
<div id="global-loader" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.8); z-index:9999; text-align:center; padding-top:20%;">
    <div style="display:inline-block; width:50px; height:50px; border:5px solid #f3f3f3; border-top:5px solid #0046ad; border-radius:50%; animation:spin 1s linear infinite;"></div>
    <p style="margin-top:10px; color:#0046ad; font-weight:bold;">Cargando...</p>
</div>

<style>
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Estilos para campos con errores */
.error-field {
    border: 2px solid #e74c3c !important;
    background-color: #fdf2f2 !important;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.3) !important;
}

.error-message {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 2px;
    font-weight: 500;
}

/* Estilos para el resumen de errores */
#error-summary {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
    position: relative;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Estilos para indicador de carga */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Mejorar visibilidad de campos requeridos */
input[required], select[required], textarea[required] {
    border-left: 3px solid #3498db;
}

input[required]:focus, select[required]:focus, textarea[required]:focus {
    border-left: 3px solid #2980b9;
}

/* Estilos para overlay de carga */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #0046ad;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 10px;
    color: #0046ad;
    font-weight: bold;
    font-size: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<!-- Error Monitor: Se mostrará solo si hay errores -->
<div id="error-monitor" style="display: none; background-color: #ffebee; border-left: 4px solid #f44336; margin: 10px 0; padding: 10px 15px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h3 style="margin: 0; color: #d32f2f; font-size: 16px;">Error Detectado</h3>
        <button onclick="document.getElementById('error-details').style.display = document.getElementById('error-details').style.display === 'none' ? 'block' : 'none';" 
                style="background: none; border: none; color: #d32f2f; cursor: pointer; font-weight: bold;">
            <i class="fas fa-chevron-down"></i> Detalles
        </button>
    </div>
    <p id="error-message" style="margin: 5px 0; font-weight: 500;"></p>
    <div id="error-details" style="display: none; margin-top: 10px; padding: 10px; background-color: #fff; border: 1px solid #ffcdd2; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 200px; overflow-y: auto;"></div>
</div>

<script>
// Monitor de errores para mostrar detalles en el panel especial
window.addEventListener('error', function(event) {
    var errorMonitor = document.getElementById('error-monitor');
    var errorMessage = document.getElementById('error-message');
    var errorDetails = document.getElementById('error-details');
    
    if (errorMonitor && errorMessage && errorDetails) {
        errorMonitor.style.display = 'block';
        errorMessage.textContent = event.message || 'Error desconocido';
        
        // Crear detalles del error
        var details = [];
        details.push('Archivo: ' + (event.filename || 'desconocido'));
        details.push('Línea: ' + (event.lineno || '?') + ', Columna: ' + (event.colno || '?'));
        if (event.error && event.error.stack) {
            details.push('\nStack Trace:\n' + event.error.stack);
        }
        
        errorDetails.textContent = details.join('\n');
    }
});

// Mostrar loader durante peticiones AJAX
$(document).ajaxStart(function() {
    $('#global-loader').fadeIn(300);
});

$(document).ajaxStop(function() {
    $('#global-loader').fadeOut(300);
});

// Capturar errores AJAX también
$(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
    var errorMonitor = document.getElementById('error-monitor');
    var errorMessage = document.getElementById('error-message');
    var errorDetails = document.getElementById('error-details');
    
    if (errorMonitor && errorMessage && errorDetails) {
        errorMonitor.style.display = 'block';
        errorMessage.textContent = 'Error en solicitud AJAX: ' + (thrownError || jqXHR.statusText || 'Error desconocido');
        
        // Crear detalles del error
        var details = [];
        details.push('URL: ' + ajaxSettings.url);
        details.push('Tipo: ' + ajaxSettings.type);
        details.push('Código de estado: ' + (jqXHR.status || 'desconocido'));
        
        if (jqXHR.responseText) {
            try {
                // Intentar formatear como JSON
                var jsonResponse = JSON.parse(jqXHR.responseText);
                details.push('\nRespuesta del servidor (JSON):\n' + JSON.stringify(jsonResponse, null, 2));
            } catch (e) {
                // Si no es JSON, mostrar como texto
                details.push('\nRespuesta del servidor:\n' + jqXHR.responseText.substring(0, 1000));
                if (jqXHR.responseText.length > 1000) details.push('... (respuesta truncada)');
            }
        }
        
        errorDetails.textContent = details.join('\n');
    }
});
</script>

<div class="tabs-container">
    <!-- Contenido principal -->
    <div class="main-content" style="margin-bottom: 90px;">

    <!-- Tab Contenido: Formulario -->
    <div id="form-tab" class="tab-content" style="overflow-y: auto; margin-bottom: 65px;">
        <form id="formExperian" method="POST" action="guardar_formulario.php" enctype="multipart/form-data">            <div class="container">
                <header style="display: flex; justify-content: space-between; align-items: center;">
                    <h1>Formulario de Registro de Cliente</h1>
                    <button type="button" onclick="llenarTodoElFormulario();" style="background-color: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                        <i class="fa fa-magic"></i> Llenar Todo el Formulario
                    </button>
                </header>

                <!-- Indicador de pasos -->
                <div class="steps-container">
                    <div class="progress-line">
                        <div class="fill"></div>
                    </div>
                    <div class="step-indicator active" data-step="1">1. Identificación del Cliente</div>
                    <div class="step-indicator" data-step="2">2. Datos de Contactos</div>
                    <div class="step-indicator" data-step="3">3. Servicios y Transacciones</div>
                </div>

                <!-- Sección 1: Identificación del cliente -->
                <div class="section-container active" id="section1">
                    <div class="section-header">1. IDENTIFICACIÓN DEL CLIENTE</div>

                    <table>
                        <tr>
                            <td class="label">Tipo de Cliente</td>
                            <td class="input-cell">
                                <select name="tipo_cliente" title="Tipo de Cliente" required>
                                    <option value="">Seleccione...</option>
                                    <option>Cliente Vigente</option>
                                    <option>Cliente No vigente</option>
                                </select>
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12"
                                    required>
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Razón Social</td>
                            <td class="input-cell">
                                <input type="text" name="razon_social" title="Razón Social" required>
                            </td>
                            <td class="label">Nombre Representante Legal 1</td>
                            <td class="input-cell">
                                <input type="text" name="nombre_representante1" title="Nombre Representante Legal 1" required>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 1</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante1"
                                    title="Rut Representante 1"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Nombre Representante Legal 2</td>
                            <td class="input-cell"><input type="text" name="nombre_representante2" title="Nombre Representante Legal 2"></td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 2</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante2"
                                    title="Rut Representante 2"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Nombre Representante Legal 3</td>
                            <td class="input-cell"><input type="text" name="nombre_representante3" title="Nombre Representante Legal 3"></td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 3</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante3"
                                    title="Rut Representante 3"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Sistema Creación de Empresa</td>
                            <td class="input-cell">
                                <select name="sistema_creacion" title="Sistema Creación de Empresa">
                                    <option>Tradicional</option>
                                    <option>Empresa por un día</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Fecha de creación</td>
                            <td class="input-cell">
                                <input type="date" name="fecha_creacion" title="Fecha de creación" required>
                            </td>
                            <td class="label">Notaría</td>
                            <td class="input-cell"><input type="text" name="notaria" title="Notaría"></td>
                        </tr>
                        <tr>
                            <td class="label">Actividad Económica SII</td>
                            <td class="input-cell"><input type="number" name="actividad_economica" title="Actividad Económica SII"></td>
                            <td class="label">Fecha de Constitución</td>
                            <td class="input-cell">
                                <input type="date" name="fecha_constitucion" title="Fecha de Constitución" required>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Dirección</td>
                            <td class="input-cell"><input type="text" name="direccion" title="Dirección"></td>
                            <td class="label">Comuna</td>
                            <td class="input-cell"><input type="text" name="comuna" title="Comuna"></td>
                        </tr>
                        <tr>
                            <td class="label">Página Web</td>
                            <td class="input-cell"><input type="text" name="pagina_web" title="Página Web"></td>
                            <td class="label">Correo Electrónico contacto</td>
                            <td class="input-cell">
                                <input type="email" name="email" title="Correo Electrónico contacto" required>
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="tel" name="telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678"
                                    required>
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Clasificación de Cliente SII</td>
                            <td class="input-cell">
                                <select name="clasificacion_sii" title="Clasificación de Cliente SII">
                                    <option>Nuevo</option>
                                    <option>Antiguo</option>
                                </select>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-test" onclick="llenarSeccion1();" style="background-color: #ff9800; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            <i class="fa fa-magic"></i> Llenar Automáticamente
                        </button>
                        <button type="button" class="btn-next" data-next="2">Siguiente</button>
                    </div>
                    
                    <script>
                    // Función para navegación manual - usa la función principal
                    function showManualFormSection(sectionNumber) {
                        console.log('Función manual para cambiar a sección:', sectionNumber);

                        // Usar la función principal de navegación
                        if (typeof window.showFormSection === 'function') {
                            window.showFormSection(sectionNumber);
                        } else {
                            // Fallback si la función principal no está disponible
                            const seccionNum = parseInt(sectionNumber);

                            if (isNaN(seccionNum) || seccionNum < 1 || seccionNum > 3) {
                                console.error('Número de sección inválido:', sectionNumber);
                                return;
                            }

                            // Ocultar todas las secciones
                            document.querySelectorAll('#form-tab .section-container').forEach(function(section) {
                                section.classList.remove('active');
                            });

                            // Mostrar la sección seleccionada
                            var targetSection = document.querySelector('#form-tab #section' + seccionNum);
                            if (targetSection) {
                                targetSection.classList.add('active');
                            }

                            // Actualizar indicadores
                            document.querySelectorAll('#form-tab .step-indicator').forEach(function(indicator) {
                                indicator.classList.remove('active');
                                if (indicator.getAttribute('data-step') === seccionNum.toString()) {
                                    indicator.classList.add('active');
                                }
                            });

                            // Actualizar barra de progreso
                            var progressFill = document.querySelector('#form-tab .progress-line .fill');
                            if (progressFill) {
                                var percentage = ((seccionNum - 1) / 2) * 100;
                                progressFill.style.width = percentage + '%';
                            }

                            // Manejar validación de secciones
                            if (typeof manejarValidacionSecciones === 'function') {
                                manejarValidacionSecciones();
                            }
                        }
                    }

                    // Funciones para llenar automáticamente los formularios con datos de prueba
                    function generarRUT() {
                        const rut = Math.floor(Math.random() * 99999999) + 1000000;
                        const dv = calcularDV(rut);
                        return formatearRUT(rut + '-' + dv);
                    }

                    function calcularDV(rut) {
                        let suma = 0;
                        let multiplicador = 2;

                        while (rut > 0) {
                            suma += (rut % 10) * multiplicador;
                            rut = Math.floor(rut / 10);
                            multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
                        }

                        const resto = suma % 11;
                        const dv = 11 - resto;

                        if (dv === 11) return '0';
                        if (dv === 10) return 'K';
                        return dv.toString();
                    }

                    function formatearRUT(rut) {
                        const rutLimpio = rut.replace(/[^0-9kK]/g, '');
                        const cuerpo = rutLimpio.slice(0, -1);
                        const dv = rutLimpio.slice(-1);

                        return cuerpo.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.') + '-' + dv;
                    }

                    function generarTelefono() {
                        return '9' + Math.floor(Math.random() * 90000000 + 10000000);
                    }

                    function generarEmail(nombre) {
                        const dominios = ['gmail.com', 'hotmail.com', 'yahoo.com', 'empresa.cl', 'test.com'];
                        const dominio = dominios[Math.floor(Math.random() * dominios.length)];
                        return nombre.toLowerCase().replace(/\s+/g, '.') + '@' + dominio;
                    }

                    function generarFecha(aniosAtras = 5) {
                        const fecha = new Date();
                        fecha.setFullYear(fecha.getFullYear() - Math.floor(Math.random() * aniosAtras));
                        fecha.setMonth(Math.floor(Math.random() * 12));
                        fecha.setDate(Math.floor(Math.random() * 28) + 1);
                        return fecha.toISOString().split('T')[0];
                    }

                    function llenarSeccion1() {
                        console.log('Llenando Sección 1 automáticamente...');

                        // Datos de empresas de ejemplo
                        const empresas = [
                            'Comercial Los Andes S.A.',
                            'Distribuidora Central Ltda.',
                            'Servicios Tecnológicos del Sur S.A.',
                            'Constructora Norte Grande Ltda.',
                            'Importadora y Exportadora Chile S.A.',
                            'Consultora Empresarial Moderna Ltda.',
                            'Industrias Metalúrgicas del Pacífico S.A.',
                            'Transportes y Logística Nacional Ltda.'
                        ];

                        const nombres = [
                            'Juan Carlos Pérez González',
                            'María Elena Rodríguez Silva',
                            'Carlos Alberto Muñoz López',
                            'Ana Patricia Fernández Torres',
                            'Roberto José Hernández Castro',
                            'Carmen Gloria Sánchez Morales',
                            'Luis Fernando García Rojas',
                            'Patricia Isabel Vargas Mendoza'
                        ];

                        const comunas = [
                            'Santiago', 'Las Condes', 'Providencia', 'Ñuñoa', 'La Florida',
                            'Maipú', 'Puente Alto', 'San Bernardo', 'Valparaíso', 'Viña del Mar'
                        ];

                        const notarias = [
                            'Notaría Primera de Santiago',
                            'Notaría Segunda de Las Condes',
                            'Notaría de Providencia',
                            'Notaría Central de Valparaíso'
                        ];

                        // Llenar campos
                        document.querySelector('select[name="tipo_cliente"]').value = Math.random() > 0.5 ? 'Cliente Vigente' : 'Cliente No vigente';
                        document.querySelector('input[name="rut"]').value = generarRUT();

                        const empresaSeleccionada = empresas[Math.floor(Math.random() * empresas.length)];
                        document.querySelector('input[name="razon_social"]').value = empresaSeleccionada;

                        const nombreRep1 = nombres[Math.floor(Math.random() * nombres.length)];
                        document.querySelector('input[name="nombre_representante1"]').value = nombreRep1;
                        document.querySelector('input[name="rut_representante1"]').value = generarRUT();

                        if (Math.random() > 0.5) {
                            const nombreRep2 = nombres[Math.floor(Math.random() * nombres.length)];
                            document.querySelector('input[name="nombre_representante2"]').value = nombreRep2;
                            document.querySelector('input[name="rut_representante2"]').value = generarRUT();
                        }

                        if (Math.random() > 0.7) {
                            const nombreRep3 = nombres[Math.floor(Math.random() * nombres.length)];
                            document.querySelector('input[name="nombre_representante3"]').value = nombreRep3;
                            document.querySelector('input[name="rut_representante3"]').value = generarRUT();
                        }

                        document.querySelector('select[name="sistema_creacion"]').value = Math.random() > 0.5 ? 'Tradicional' : 'Empresa por un día';
                        document.querySelector('input[name="fecha_creacion"]').value = generarFecha(10);
                        document.querySelector('input[name="notaria"]').value = notarias[Math.floor(Math.random() * notarias.length)];
                        document.querySelector('input[name="actividad_economica"]').value = Math.floor(Math.random() * 900000 + 100000);
                        document.querySelector('input[name="fecha_constitucion"]').value = generarFecha(15);

                        const comunaSeleccionada = comunas[Math.floor(Math.random() * comunas.length)];
                        document.querySelector('input[name="direccion"]').value = `Av. Principal ${Math.floor(Math.random() * 9999 + 100)}, ${comunaSeleccionada}`;
                        document.querySelector('input[name="comuna"]').value = comunaSeleccionada;

                        document.querySelector('input[name="pagina_web"]').value = `www.${empresaSeleccionada.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '')}.cl`;
                        document.querySelector('input[name="email"]').value = generarEmail(empresaSeleccionada.split(' ')[0]);
                        document.querySelector('input[name="telefono"]').value = generarTelefono();
                        document.querySelector('select[name="clasificacion_sii"]').value = Math.random() > 0.5 ? 'Nuevo' : 'Antiguo';

                        alert('Sección 1 llenada automáticamente con datos de prueba');
                    }

                    function llenarSeccion2() {
                        console.log('Llenando Sección 2 automáticamente...');

                        const nombres = [
                            'Juan Carlos Pérez González',
                            'María Elena Rodríguez Silva',
                            'Carlos Alberto Muñoz López',
                            'Ana Patricia Fernández Torres',
                            'Roberto José Hernández Castro',
                            'Carmen Gloria Sánchez Morales',
                            'Luis Fernando García Rojas',
                            'Patricia Isabel Vargas Mendoza'
                        ];

                        const cargos = [
                            'Gerente General',
                            'Director Ejecutivo',
                            'Administrador',
                            'Representante Legal',
                            'Presidente',
                            'Vicepresidente',
                            'Secretario',
                            'Tesorero'
                        ];

                        const comunas = [
                            'Santiago', 'Las Condes', 'Providencia', 'Ñuñoa', 'La Florida',
                            'Maipú', 'Puente Alto', 'San Bernardo', 'Valparaíso', 'Viña del Mar'
                        ];

                        // Llenar campos de la sección 2 - Contacto principal
                        const nombreContacto = nombres[Math.floor(Math.random() * nombres.length)];
                        document.querySelector('input[name="contacto_nombre"]').value = nombreContacto;
                        document.querySelector('input[name="contacto_rut"]').value = generarRUT();
                        document.querySelector('input[name="contacto_telefono"]').value = generarTelefono();
                        document.querySelector('input[name="contacto_email"]').value = generarEmail(nombreContacto);

                        // Llenar campos de backup
                        if (Math.random() > 0.3) { // 70% de probabilidad de llenar datos de backup
                            const nombreBackup = nombres[Math.floor(Math.random() * nombres.length)];
                            document.querySelector('input[name="contacto_backup_nombre"]').value = nombreBackup;
                            document.querySelector('input[name="contacto_backup_rut"]').value = generarRUT();
                            document.querySelector('input[name="contacto_backup_telefono"]').value = generarTelefono();
                            document.querySelector('input[name="contacto_backup_email"]').value = generarEmail(nombreBackup);
                        }

                        alert('Sección 2 llenada automáticamente con datos de prueba');
                    }

                    function llenarSeccion3() {
                        console.log('Llenando Sección 3 automáticamente...');

                        const nombres = [
                            'Juan Carlos Pérez González',
                            'María Elena Rodríguez Silva',
                            'Carlos Alberto Muñoz López',
                            'Ana Patricia Fernández Torres',
                            'Roberto José Hernández Castro',
                            'Carmen Gloria Sánchez Morales',
                            'Luis Fernando García Rojas',
                            'Patricia Isabel Vargas Mendoza'
                        ];

                        // Llenar Publicación de Morosos
                        const planesMorosos = ['XS', 'S', 'M', 'L', 'XL'];
                        const planMoroso = planesMorosos[Math.floor(Math.random() * planesMorosos.length)];
                        document.querySelector('select[name="morosos_plan"]').value = planMoroso;

                        // Llenar descuento morosos
                        const descuentosMorosos = ['0', '10', '20', '30'];
                        document.querySelector('select[name="morosos_descuento"]').value = descuentosMorosos[Math.floor(Math.random() * descuentosMorosos.length)];

                        // Llenar Advanced SME
                        const planesAdvanced = ['25', '50', '100', '200', '300', '400', '500', '1000', '2000', '3000', '4000', '5000'];
                        const planAdvanced = planesAdvanced[Math.floor(Math.random() * planesAdvanced.length)];
                        document.querySelector('select[name="advanced_plan"]').value = planAdvanced;

                        // Llenar descuento advanced
                        const descuentosAdvanced = ['0', '5', '10', '15', '20'];
                        document.querySelector('select[name="advanced_descuento"]').value = descuentosAdvanced[Math.floor(Math.random() * descuentosAdvanced.length)];

                        // Llenar datos para uso de claves
                        const nombreClave = nombres[Math.floor(Math.random() * nombres.length)];
                        document.querySelector('input[name="clave_nombre"]').value = nombreClave;
                        document.querySelector('input[name="clave_rut"]').value = generarRUT();
                        document.querySelector('input[name="clave_email"]').value = generarEmail(nombreClave);
                        document.querySelector('input[name="clave_telefono"]').value = generarTelefono();

                        // Llenar datos de backup de claves
                        if (Math.random() > 0.4) { // 60% de probabilidad
                            const nombreBackupClave = nombres[Math.floor(Math.random() * nombres.length)];
                            document.querySelector('input[name="backup_clave_nombre"]').value = nombreBackupClave;
                            document.querySelector('input[name="backup_clave_rut"]').value = generarRUT();
                            document.querySelector('input[name="backup_clave_email"]').value = generarEmail(nombreBackupClave);
                            document.querySelector('input[name="backup_clave_telefono"]').value = generarTelefono();
                        }

                        alert('Sección 3 llenada automáticamente con datos de prueba');
                    }

                    function llenarTodoElFormulario() {
                        console.log('Llenando todo el formulario automáticamente...');

                        // Llenar todas las secciones
                        llenarSeccion1();

                        // Esperar un poco antes de llenar la siguiente sección
                        setTimeout(() => {
                            window.showFormSection(2);
                            setTimeout(() => {
                                llenarSeccion2();
                                setTimeout(() => {
                                    window.showFormSection(3);
                                    setTimeout(() => {
                                        llenarSeccion3();
                                        alert('¡Todo el formulario ha sido llenado automáticamente!');
                                    }, 500);
                                }, 500);
                            }, 500);
                        }, 500);
                    }

                    // ===== SISTEMA DE VALIDACIÓN COMPLETO =====

                    // Función para validar RUT chileno
                    function validarRUT(rut) {
                        if (!rut || rut.trim() === '') return false;

                        // Limpiar el RUT
                        const rutLimpio = rut.replace(/[^0-9kK]/g, '');

                        if (rutLimpio.length < 8 || rutLimpio.length > 9) return false;

                        const cuerpo = rutLimpio.slice(0, -1);
                        const dv = rutLimpio.slice(-1).toUpperCase();

                        // Calcular dígito verificador
                        let suma = 0;
                        let multiplicador = 2;

                        for (let i = cuerpo.length - 1; i >= 0; i--) {
                            suma += parseInt(cuerpo[i]) * multiplicador;
                            multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
                        }

                        const resto = suma % 11;
                        const dvCalculado = resto === 0 ? '0' : resto === 1 ? 'K' : (11 - resto).toString();

                        return dv === dvCalculado;
                    }

                    // Función para validar email
                    function validarEmail(email) {
                        if (!email || email.trim() === '') return false;
                        const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        return regex.test(email.trim());
                    }

                    // Función para validar teléfono (9 dígitos)
                    function validarTelefono(telefono) {
                        if (!telefono || telefono.trim() === '') return false;
                        const telefonoLimpio = telefono.replace(/[^0-9]/g, '');
                        return telefonoLimpio.length === 9;
                    }

                    // Función para validar fecha (no debe ser futura para fechas de constitución)
                    function validarFecha(fecha, permitirFutura = false) {
                        if (!fecha || fecha.trim() === '') return false;

                        const fechaObj = new Date(fecha);
                        const hoy = new Date();
                        hoy.setHours(23, 59, 59, 999); // Permitir hasta el final del día actual

                        if (isNaN(fechaObj.getTime())) return false;

                        if (!permitirFutura && fechaObj > hoy) return false;

                        return true;
                    }

                    // Función para mostrar error en un campo
                    function mostrarErrorCampo(campo, mensaje) {
                        // Remover errores previos
                        campo.classList.remove('error-field');
                        const errorPrevio = campo.parentNode.querySelector('.error-message');
                        if (errorPrevio) {
                            errorPrevio.remove();
                        }

                        // Agregar clase de error
                        campo.classList.add('error-field');

                        // Crear mensaje de error
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'error-message';
                        errorDiv.textContent = mensaje;
                        errorDiv.style.color = '#e74c3c';
                        errorDiv.style.fontSize = '12px';
                        errorDiv.style.marginTop = '2px';

                        // Insertar después del campo
                        campo.parentNode.appendChild(errorDiv);
                    }

                    // Función para limpiar errores de un campo
                    function limpiarErrorCampo(campo) {
                        campo.classList.remove('error-field');
                        const errorMessage = campo.parentNode.querySelector('.error-message');
                        if (errorMessage) {
                            errorMessage.remove();
                        }
                    }

                    // Función para limpiar todos los errores
                    function limpiarTodosLosErrores() {
                        document.querySelectorAll('.error-field').forEach(campo => {
                            limpiarErrorCampo(campo);
                        });

                        // Remover resumen de errores
                        const resumenErrores = document.getElementById('error-summary');
                        if (resumenErrores) {
                            resumenErrores.remove();
                        }
                    }

                    // Función para mostrar resumen de errores
                    function mostrarResumenErrores(errores) {
                        // Remover resumen previo
                        const resumenPrevio = document.getElementById('error-summary');
                        if (resumenPrevio) {
                            resumenPrevio.remove();
                        }

                        // Crear contenedor de resumen
                        const resumen = document.createElement('div');
                        resumen.id = 'error-summary';
                        resumen.style.cssText = `
                            background-color: #f8d7da;
                            border: 1px solid #f5c6cb;
                            color: #721c24;
                            padding: 15px;
                            margin: 15px 0;
                            border-radius: 4px;
                            position: relative;
                        `;

                        // Título del resumen
                        const titulo = document.createElement('h4');
                        titulo.textContent = 'Por favor, corrija los siguientes errores:';
                        titulo.style.margin = '0 0 10px 0';
                        resumen.appendChild(titulo);

                        // Lista de errores
                        const lista = document.createElement('ul');
                        lista.style.margin = '0';
                        lista.style.paddingLeft = '20px';

                        errores.forEach(error => {
                            const item = document.createElement('li');
                            item.textContent = error;
                            item.style.marginBottom = '5px';
                            lista.appendChild(item);
                        });

                        resumen.appendChild(lista);

                        // Insertar al inicio del formulario
                        const formulario = document.getElementById('formExperian');
                        const container = formulario.querySelector('.container');
                        container.insertBefore(resumen, container.firstChild.nextSibling);

                        // Scroll al resumen
                        resumen.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }

                    // Función principal de validación del formulario
                    function validarFormularioCompleto() {
                        console.log('Iniciando validación completa del formulario...');

                        // Limpiar errores previos
                        limpiarTodosLosErrores();

                        const errores = [];
                        let primerCampoConError = null;

                        // ===== VALIDACIÓN SECCIÓN 1 =====

                        // Tipo de Cliente (requerido)
                        const tipoCliente = document.querySelector('select[name="tipo_cliente"]');
                        if (!tipoCliente.value.trim()) {
                            errores.push('Debe seleccionar el tipo de cliente');
                            mostrarErrorCampo(tipoCliente, 'Campo requerido');
                            if (!primerCampoConError) primerCampoConError = tipoCliente;
                        }

                        // RUT (requerido y válido)
                        const rut = document.querySelector('input[name="rut"]');
                        if (!rut.value.trim()) {
                            errores.push('El RUT es requerido');
                            mostrarErrorCampo(rut, 'Campo requerido');
                            if (!primerCampoConError) primerCampoConError = rut;
                        } else if (!validarRUT(rut.value)) {
                            errores.push('El RUT ingresado no es válido');
                            mostrarErrorCampo(rut, 'RUT inválido');
                            if (!primerCampoConError) primerCampoConError = rut;
                        }

                        // Razón Social (requerido)
                        const razonSocial = document.querySelector('input[name="razon_social"]');
                        if (!razonSocial.value.trim()) {
                            errores.push('La razón social es requerida');
                            mostrarErrorCampo(razonSocial, 'Campo requerido');
                            if (!primerCampoConError) primerCampoConError = razonSocial;
                        }

                        // Representante Legal 1 (requerido)
                        const nombreRep1 = document.querySelector('input[name="nombre_representante1"]');
                        if (!nombreRep1.value.trim()) {
                            errores.push('El nombre del representante legal 1 es requerido');
                            mostrarErrorCampo(nombreRep1, 'Campo requerido');
                            if (!primerCampoConError) primerCampoConError = nombreRep1;
                        }

                        // RUT Representante 1 (validar si está lleno)
                        const rutRep1 = document.querySelector('input[name="rut_representante1"]');
                        if (rutRep1.value.trim() && !validarRUT(rutRep1.value)) {
                            errores.push('El RUT del representante legal 1 no es válido');
                            mostrarErrorCampo(rutRep1, 'RUT inválido');
                            if (!primerCampoConError) primerCampoConError = rutRep1;
                        }

                        // RUT Representante 2 (validar si está lleno)
                        const rutRep2 = document.querySelector('input[name="rut_representante2"]');
                        if (rutRep2.value.trim() && !validarRUT(rutRep2.value)) {
                            errores.push('El RUT del representante legal 2 no es válido');
                            mostrarErrorCampo(rutRep2, 'RUT inválido');
                            if (!primerCampoConError) primerCampoConError = rutRep2;
                        }

                        // RUT Representante 3 (validar si está lleno)
                        const rutRep3 = document.querySelector('input[name="rut_representante3"]');
                        if (rutRep3.value.trim() && !validarRUT(rutRep3.value)) {
                            errores.push('El RUT del representante legal 3 no es válido');
                            mostrarErrorCampo(rutRep3, 'RUT inválido');
                            if (!primerCampoConError) primerCampoConError = rutRep3;
                        }

                        // Fecha de creación (requerido)
                        const fechaCreacion = document.querySelector('input[name="fecha_creacion"]');
                        if (!fechaCreacion.value.trim()) {
                            errores.push('La fecha de creación es requerida');
                            mostrarErrorCampo(fechaCreacion, 'Campo requerido');
                            if (!primerCampoConError) primerCampoConError = fechaCreacion;
                        } else if (!validarFecha(fechaCreacion.value, false)) {
                            errores.push('La fecha de creación no puede ser futura');
                            mostrarErrorCampo(fechaCreacion, 'Fecha inválida');
                            if (!primerCampoConError) primerCampoConError = fechaCreacion;
                        }

                        // Fecha de constitución (requerido)
                        const fechaConstitucion = document.querySelector('input[name="fecha_constitucion"]');
                        if (!fechaConstitucion.value.trim()) {
                            errores.push('La fecha de constitución es requerida');
                            mostrarErrorCampo(fechaConstitucion, 'Campo requerido');
                            if (!primerCampoConError) primerCampoConError = fechaConstitucion;
                        } else if (!validarFecha(fechaConstitucion.value, false)) {
                            errores.push('La fecha de constitución no puede ser futura');
                            mostrarErrorCampo(fechaConstitucion, 'Fecha inválida');
                            if (!primerCampoConError) primerCampoConError = fechaConstitucion;
                        }

                        // Email (requerido y válido)
                        const email = document.querySelector('input[name="email"]');
                        if (!email.value.trim()) {
                            errores.push('El correo electrónico es requerido');
                            mostrarErrorCampo(email, 'Campo requerido');
                            if (!primerCampoConError) primerCampoConError = email;
                        } else if (!validarEmail(email.value)) {
                            errores.push('El correo electrónico no tiene un formato válido');
                            mostrarErrorCampo(email, 'Email inválido');
                            if (!primerCampoConError) primerCampoConError = email;
                        }

                        // Teléfono (requerido y válido)
                        const telefono = document.querySelector('input[name="telefono"]');
                        if (!telefono.value.trim()) {
                            errores.push('El teléfono es requerido');
                            mostrarErrorCampo(telefono, 'Campo requerido');
                            if (!primerCampoConError) primerCampoConError = telefono;
                        } else if (!validarTelefono(telefono.value)) {
                            errores.push('El teléfono debe tener 9 dígitos');
                            mostrarErrorCampo(telefono, 'Teléfono inválido');
                            if (!primerCampoConError) primerCampoConError = telefono;
                        }

                        // ===== VALIDACIÓN SECCIÓN 2 =====

                        // Validar RUTs de contacto si están llenos
                        const contactoRut = document.querySelector('input[name="contacto_rut"]');
                        if (contactoRut.value.trim() && !validarRUT(contactoRut.value)) {
                            errores.push('El RUT del contacto no es válido');
                            mostrarErrorCampo(contactoRut, 'RUT inválido');
                            if (!primerCampoConError) primerCampoConError = contactoRut;
                        }

                        // Validar teléfono de contacto si está lleno
                        const contactoTelefono = document.querySelector('input[name="contacto_telefono"]');
                        if (contactoTelefono.value.trim() && !validarTelefono(contactoTelefono.value)) {
                            errores.push('El teléfono del contacto debe tener 9 dígitos');
                            mostrarErrorCampo(contactoTelefono, 'Teléfono inválido');
                            if (!primerCampoConError) primerCampoConError = contactoTelefono;
                        }

                        // Validar email de contacto si está lleno
                        const contactoEmail = document.querySelector('input[name="contacto_email"]');
                        if (contactoEmail.value.trim() && !validarEmail(contactoEmail.value)) {
                            errores.push('El email del contacto no tiene un formato válido');
                            mostrarErrorCampo(contactoEmail, 'Email inválido');
                            if (!primerCampoConError) primerCampoConError = contactoEmail;
                        }

                        // Validar RUT de contacto backup si está lleno
                        const contactoBackupRut = document.querySelector('input[name="contacto_backup_rut"]');
                        if (contactoBackupRut.value.trim() && !validarRUT(contactoBackupRut.value)) {
                            errores.push('El RUT del contacto backup no es válido');
                            mostrarErrorCampo(contactoBackupRut, 'RUT inválido');
                            if (!primerCampoConError) primerCampoConError = contactoBackupRut;
                        }

                        // Validar teléfono de contacto backup si está lleno
                        const contactoBackupTelefono = document.querySelector('input[name="contacto_backup_telefono"]');
                        if (contactoBackupTelefono.value.trim() && !validarTelefono(contactoBackupTelefono.value)) {
                            errores.push('El teléfono del contacto backup debe tener 9 dígitos');
                            mostrarErrorCampo(contactoBackupTelefono, 'Teléfono inválido');
                            if (!primerCampoConError) primerCampoConError = contactoBackupTelefono;
                        }

                        // Validar email de contacto backup si está lleno
                        const contactoBackupEmail = document.querySelector('input[name="contacto_backup_email"]');
                        if (contactoBackupEmail.value.trim() && !validarEmail(contactoBackupEmail.value)) {
                            errores.push('El email del contacto backup no tiene un formato válido');
                            mostrarErrorCampo(contactoBackupEmail, 'Email inválido');
                            if (!primerCampoConError) primerCampoConError = contactoBackupEmail;
                        }

                        // ===== VALIDACIÓN SECCIÓN 3 =====

                        // Validar RUT para uso de claves si está lleno
                        const claveRut = document.querySelector('input[name="clave_rut"]');
                        if (claveRut.value.trim() && !validarRUT(claveRut.value)) {
                            errores.push('El RUT para uso de claves no es válido');
                            mostrarErrorCampo(claveRut, 'RUT inválido');
                            if (!primerCampoConError) primerCampoConError = claveRut;
                        }

                        // Validar email para uso de claves si está lleno
                        const claveEmail = document.querySelector('input[name="clave_email"]');
                        if (claveEmail.value.trim() && !validarEmail(claveEmail.value)) {
                            errores.push('El email para uso de claves no tiene un formato válido');
                            mostrarErrorCampo(claveEmail, 'Email inválido');
                            if (!primerCampoConError) primerCampoConError = claveEmail;
                        }

                        // Validar teléfono para uso de claves si está lleno
                        const claveTelefono = document.querySelector('input[name="clave_telefono"]');
                        if (claveTelefono.value.trim() && !validarTelefono(claveTelefono.value)) {
                            errores.push('El teléfono para uso de claves debe tener 9 dígitos');
                            mostrarErrorCampo(claveTelefono, 'Teléfono inválido');
                            if (!primerCampoConError) primerCampoConError = claveTelefono;
                        }

                        // Validar RUT backup de claves si está lleno
                        const backupClaveRut = document.querySelector('input[name="backup_clave_rut"]');
                        if (backupClaveRut.value.trim() && !validarRUT(backupClaveRut.value)) {
                            errores.push('El RUT backup de claves no es válido');
                            mostrarErrorCampo(backupClaveRut, 'RUT inválido');
                            if (!primerCampoConError) primerCampoConError = backupClaveRut;
                        }

                        // Validar email backup de claves si está lleno
                        const backupClaveEmail = document.querySelector('input[name="backup_clave_email"]');
                        if (backupClaveEmail.value.trim() && !validarEmail(backupClaveEmail.value)) {
                            errores.push('El email backup de claves no tiene un formato válido');
                            mostrarErrorCampo(backupClaveEmail, 'Email inválido');
                            if (!primerCampoConError) primerCampoConError = backupClaveEmail;
                        }

                        // Validar teléfono backup de claves si está lleno
                        const backupClaveTelefono = document.querySelector('input[name="backup_clave_telefono"]');
                        if (backupClaveTelefono.value.trim() && !validarTelefono(backupClaveTelefono.value)) {
                            errores.push('El teléfono backup de claves debe tener 9 dígitos');
                            mostrarErrorCampo(backupClaveTelefono, 'Teléfono inválido');
                            if (!primerCampoConError) primerCampoConError = backupClaveTelefono;
                        }

                        // Mostrar resumen de errores si hay alguno
                        if (errores.length > 0) {
                            mostrarResumenErrores(errores);

                            // Scroll al primer campo con error
                            if (primerCampoConError) {
                                setTimeout(() => {
                                    primerCampoConError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    primerCampoConError.focus();
                                }, 500);
                            }
                        }

                        console.log(`Validación completada. Errores encontrados: ${errores.length}`);
                        return { esValido: errores.length === 0, errores, primerCampoConError };
                    }

                    // Función para validar archivos
                    function validarArchivos() {
                        const errores = [];
                        const archivosPermitidos = ['pdf', 'jpg', 'jpeg', 'png'];
                        const tamañoMaximo = 32 * 1024 * 1024; // 32MB

                        const inputsArchivo = document.querySelectorAll('input[type="file"]');

                        inputsArchivo.forEach(input => {
                            if (input.files && input.files.length > 0) {
                                const archivo = input.files[0];
                                const extension = archivo.name.split('.').pop().toLowerCase();

                                if (!archivosPermitidos.includes(extension)) {
                                    errores.push(`El archivo ${archivo.name} no tiene un formato permitido. Use: PDF, JPG, PNG`);
                                    mostrarErrorCampo(input, 'Formato no permitido');
                                }

                                if (archivo.size > tamañoMaximo) {
                                    errores.push(`El archivo ${archivo.name} es demasiado grande. Máximo: 32MB`);
                                    mostrarErrorCampo(input, 'Archivo muy grande');
                                }
                            }
                        });

                        return errores;
                    }

                    // Función para configurar validación en tiempo real
                    function configurarValidacionTiempoReal() {
                        // Validación de RUTs en tiempo real
                        document.querySelectorAll('.rut-input').forEach(input => {
                            input.addEventListener('blur', function() {
                                if (this.value.trim() && !validarRUT(this.value)) {
                                    mostrarErrorCampo(this, 'RUT inválido');
                                } else {
                                    limpiarErrorCampo(this);
                                }
                            });

                            input.addEventListener('input', function() {
                                // Limpiar error mientras el usuario escribe
                                limpiarErrorCampo(this);
                            });
                        });

                        // Validación de emails en tiempo real
                        document.querySelectorAll('input[type="email"]').forEach(input => {
                            input.addEventListener('blur', function() {
                                if (this.value.trim() && !validarEmail(this.value)) {
                                    mostrarErrorCampo(this, 'Email inválido');
                                } else {
                                    limpiarErrorCampo(this);
                                }
                            });

                            input.addEventListener('input', function() {
                                limpiarErrorCampo(this);
                            });
                        });

                        // Validación de teléfonos en tiempo real
                        document.querySelectorAll('input[name*="telefono"]').forEach(input => {
                            input.addEventListener('blur', function() {
                                if (this.value.trim() && !validarTelefono(this.value)) {
                                    mostrarErrorCampo(this, 'Debe tener 9 dígitos');
                                } else {
                                    limpiarErrorCampo(this);
                                }
                            });

                            input.addEventListener('input', function() {
                                limpiarErrorCampo(this);
                            });
                        });

                        // Validación de fechas en tiempo real
                        document.querySelectorAll('input[type="date"]').forEach(input => {
                            input.addEventListener('blur', function() {
                                if (this.value.trim() && !validarFecha(this.value, false)) {
                                    mostrarErrorCampo(this, 'Fecha no puede ser futura');
                                } else {
                                    limpiarErrorCampo(this);
                                }
                            });

                            input.addEventListener('input', function() {
                                limpiarErrorCampo(this);
                            });
                        });

                        // Validación de campos requeridos
                        document.querySelectorAll('input[required], select[required]').forEach(input => {
                            input.addEventListener('blur', function() {
                                if (!this.value.trim()) {
                                    mostrarErrorCampo(this, 'Campo requerido');
                                } else {
                                    limpiarErrorCampo(this);
                                }
                            });

                            input.addEventListener('input', function() {
                                if (this.value.trim()) {
                                    limpiarErrorCampo(this);
                                }
                            });
                        });
                    }

                    // Función para manejar validación HTML5 en secciones ocultas
                    function manejarValidacionSecciones() {
                        // Deshabilitar required en campos de secciones no activas
                        document.querySelectorAll('.section-container:not(.active) input[required], .section-container:not(.active) select[required]').forEach(campo => {
                            campo.setAttribute('data-was-required', 'true');
                            campo.removeAttribute('required');
                        });

                        // Habilitar required en campos de sección activa
                        document.querySelectorAll('.section-container.active input[data-was-required], .section-container.active select[data-was-required]').forEach(campo => {
                            campo.setAttribute('required', 'required');
                            campo.removeAttribute('data-was-required');
                        });
                    }

                    // Función para restaurar todos los required
                    function restaurarValidacionHTML5() {
                        document.querySelectorAll('input[data-was-required], select[data-was-required]').forEach(campo => {
                            campo.setAttribute('required', 'required');
                            campo.removeAttribute('data-was-required');
                        });
                    }

                    // Configurar validación cuando el DOM esté listo
                    document.addEventListener('DOMContentLoaded', function() {
                        configurarValidacionTiempoReal();
                        manejarValidacionSecciones();
                        console.log('Validación en tiempo real configurada');

                        // Manejar validación cuando cambian las secciones
                        const observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(mutation) {
                                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                                    manejarValidacionSecciones();
                                }
                            });
                        });

                        // Observar cambios en las secciones
                        document.querySelectorAll('.section-container').forEach(section => {
                            observer.observe(section, { attributes: true });
                        });
                    });
                    </script>
                </div>

                <!-- Sección 2: Datos de Contactos -->
                <div class="section-container" id="section2">
                    <div class="section-header">2. DATOS DE CONTACTOS</div>

                    <table>
                        <tr>
                            <td class="label">Contacto Nombre</td>
                            <td class="input-cell"><input type="text" name="contacto_nombre" title="Contacto Nombre"></td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="contacto_rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="contacto_telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Correo Electrónico</td>
                            <td class="input-cell">
                                <input type="email" name="contacto_email" title="Correo Electrónico">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                        <td class="label">2.1 DATOS DE BACKUP</td>
                        <tr>
                            <td class="label">Contacto Backup Nombre</td>
                            <td class="input-cell"><input type="text" name="contacto_backup_nombre" title="Contacto Backup Nombre"></td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="contacto_backup_rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="contacto_backup_telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Correo Electrónico</td>
                            <td class="input-cell">
                                <input type="email" name="contacto_backup_email" title="Correo Electrónico">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-prev" data-prev="1">Anterior</button>
                        <button type="button" class="btn-test" onclick="llenarSeccion2();" style="background-color: #ff9800; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            <i class="fa fa-magic"></i> Llenar Automáticamente
                        </button>
                        <button type="button" class="btn-next" data-next="3">Siguiente</button>
                    </div>
                </div>

                <!-- Sección 3: Servicios y Nivel de Transacciones -->
                <div class="section-container" id="section3">
                    <div class="section-header">3. SERVICIOS Y NIVEL DE TRANSACCIONES / PUNTOS</div>

                    <!-- Subsección 3.1: Publicación de Morosos -->
                    <div class="subsection-header">3.1 PUBLICACIÓN DE MOROSOS</div>
                    <table>
                        <tr>
                            <td class="label">Plan</td>
                            <td class="input-cell">
                                <select name="morosos_plan" id="morosos_plan" title="Plan Publicación de Morosos">
                                    <option value="">Seleccione...</option>
                                    <option>XS</option>
                                    <option>S</option>
                                    <option>M</option>
                                    <option>L</option>
                                    <option>XL</option>
                                </select>
                            </td>
                            <td class="label">Número de Consultas</td>
                            <td class="input-cell">
                                <input type="text" name="morosos_consultas" id="morosos_consultas" title="Número de Consultas"  readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">UF Mensual</td>
                            <td class="input-cell">
                                <input type="number" name="morosos_uf" id="morosos_uf" step="0.01" title="UF Mensual" placeholder="0.00" readonly>
                            </td>
                            <td class="label">% de descuento</td>
                            <td class="input-cell">
                                <select name="morosos_descuento" title="% de descuento">
                                    <option value="0">0%</option>
                                    <option value="10">10%</option>
                                    <option value="20">20%</option>
                                    <option value="30">30%</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Nuevo Valor</td>
                            <td class="input-cell">
                                <input type="number" name="morosos_nuevo_valor" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    </table>

                    <!-- Subsección 3.2: Informe Advanced SME -->
                    <div class="subsection-header">3.2 INFORME ADVANCED SME</div>
                    <table>
                        <tr>
                            <td class="label">Plan</td>
                            <td class="input-cell">
                                <select name="advanced_plan" title="Plan Advanced SME">
                                    <option value="">Seleccione...</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                    <option value="300">300</option>
                                    <option value="400">400</option>
                                    <option value="500">500</option>
                                    <option value="1000">1.000</option>
                                    <option value="2000">2.000</option>
                                    <option value="3000">3.000</option>
                                    <option value="4000">4.000</option>
                                    <option value="5000">5.000</option>
                                </select>
                            </td>
                            <td class="label"> UF / transacción</td>
                            <td class="input-cell">
                                <input type="text" name="advanced_consultas" title="Número de Consultas"  readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">UF Mensual</td>
                            <td class="input-cell">
                                <input type="number" name="advanced_uf" step="0.01" title="UF Mensual" placeholder="0.00" readonly>
                            </td>
                            <td class="label">% de descuento</td>
                            <td class="input-cell">
                                <select name="advanced_descuento" title="% de descuento">
                                    <option value="0">0%</option>
                                    <option value="5">5%</option>
                                    <option value="10">10%</option>
                                    <option value="15%">15%</option>
                                    <option value="20">20%</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Nuevo Valor</td>
                            <td class="input-cell">
                                <input type="number" name="advanced_nuevo_valor" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    </table>

                    <!-- Subsección 3.3: Uso de Claves -->
                    <div class="subsection-header">3.3 PARA USO DE CLAVES</div>
                    <table>
                        <tr>
                            <td class="label">Nombre</td>
                            <td class="input-cell">
                                <input type="text" name="clave_nombre" title="Nombre para uso de claves">
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="clave_rut"
                                    title="Rut para uso de claves"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Correo</td>
                            <td class="input-cell">
                                <input type="email" name="clave_email" title="Correo para uso de claves">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="clave_telefono" title="Teléfono para uso de claves"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                        </tr>
                    </table>

                    <!-- Subsección 3.4: Backup de Claves -->
                    <div class="subsection-header">3.4 BACKUP DE CLAVES</div>
                    <table>
                        <tr>
                            <td class="label">Nombre</td>
                            <td class="input-cell">
                                <input type="text" name="backup_clave_nombre" title="Nombre backup de claves">
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="backup_clave_rut"
                                    title="Rut backup de claves"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Correo</td>
                            <td class="input-cell">
                                <input type="email" name="backup_clave_email" title="Correo backup de claves">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="backup_clave_telefono" title="Teléfono backup de claves"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                        </tr>
                    </table>

                    <!-- Nueva Subsección 3.5: Documentos -->
                    <div class="subsection-header">3.5 DOCUMENTOS</div>
                    <table>
                        <tr>
                            <td class="label">Carnet de Identidad (CI)</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci" id="archivo_ci" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Erut</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_erut" id="archivo_erut" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Extracto</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_extracto" id="archivo_extracto" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">CI Frente</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci_frente" id="archivo_ci_frente" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">CI Detrás</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci_detras" id="archivo_ci_detras" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Carpeta Tributaria</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_carpeta_tributaria" id="archivo_carpeta_tributaria" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Consulta de Terceros</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_consulta_terceros" id="archivo_consulta_terceros" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-prev" data-prev="2">Anterior</button>
                        <button type="button" class="btn-test" onclick="llenarSeccion3();" style="background-color: #ff9800; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            <i class="fa fa-magic"></i> Llenar Automáticamente
                        </button>
                        <button type="submit" class="btn-submit">Guardar Formulario</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Tab Contenido: Tabla -->
    <?php if ($_SESSION['usuario_id'] == 4): ?>
    <div id="table-tab" class="tab-content">
        <div id="table-container">
            <h2>Registros de Clientes</h2>
            <button id="exportClients" class="export-button">
                <i class="fa fa-download"></i> Descargar Registros
            </button>
            <div class="table-controls">
                <input type="text" class="table-search" id="tableSearch" placeholder="Buscar...">
            </div>
            <div class="table-wrapper" style="overflow-x: auto; width: 100%; display: block;">
                <table id="user-table" style="min-width: 100%; width: auto; border-collapse: separate; border-spacing: 0;">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tipo Cliente</th>
                            <th>RUT</th>
                            <th>Razón Social</th>
                            <th>Nombre Representante 1</th>
                            <th>RUT Representante 1</th>
                            <th>Nombre Representante 2</th>
                            <th>RUT Representante 2</th>
                            <th>Nombre Representante 3</th>
                            <th>RUT Representante 3</th>
                            <th>Sistema Creación</th>
                            <th>Fecha Creación</th>
                            <th>Notaría</th>
                            <th>Actividad Económica</th>
                            <th>Fecha Constitución</th>
                            <th>Dirección</th>
                            <th>Comuna</th>
                            <th>Página Web</th>
                            <th>Email</th>
                            <th>Teléfono</th>
                            <th>Clasificación SII</th>
                            <th>Contacto Nombre</th>
                            <th>Contacto RUT</th>
                            <th>Contacto Teléfono</th>
                            <th>Contacto Email</th>
                            <th>Contacto Backup Nombre</th>
                            <th>Contacto Backup RUT</th>
                            <th>Contacto Backup Teléfono</th>
                            <th>Contacto Backup Email</th>
                            <th>Morosos Plan</th>
                            <th>Morosos Consultas</th>
                            <th>Morosos UF</th>
                            <th>Morosos Descuento</th>
                            <th>Morosos Nuevo Valor</th>
                            <th>Advanced Plan</th>
                            <th>Advanced Consultas</th>
                            <th>Advanced UF</th>
                            <th>Advanced Descuento</th>
                            <th>Advanced Nuevo Valor</th>
                            <!-- Columnas para claves de usuario -->
                            <th>Nombre Usuario Clave</th>
                            <th>RUT Usuario Clave</th>
                            <th>Email Usuario Clave</th>
                            <th>Teléfono Usuario Clave</th>
                            <th>Nombre Backup Clave</th>
                            <th>RUT Backup Clave</th>
                            <th>Email Backup Clave</th>
                            <th>Teléfono Backup Clave</th>
                            <!-- Nuevas columnas para documentos -->
                            <th>CI</th>
                            <th>ERUT</th>
                            <th>Extracto</th>
                            <th>CI Frente</th>
                            <th>CI Detrás</th>
                            <th>Carpeta Tributaria</th>
                            <th>Consulta Terceros</th>
                            <th>Fecha Creación Registro</th>
                            <th>ID Usuario</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        try {
                            // Verificar que la función existe antes de usarla
                            if (function_exists('createPDOConnection')) {
                                $connection = createPDOConnection();
                            } else {
                                // Alternativa si la función no está disponible
                                $connection = new PDO(
                                    "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                                    'gestarse_ncornejo7_experian',
                                    'N1c0l7as17',
                                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                                );
                            }
                            if ($connection) {
                                $query = "SELECT * FROM form_experian ORDER BY id DESC";
                                $stmt = $connection->prepare($query);
                                $stmt->execute();
                                $clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                if (empty($clientes)) {
                                    echo '<tr><td colspan="54" class="no-data">No hay registros disponibles</td></tr>';
                                } else {
                                    foreach ($clientes as $cliente) {
                                        echo '<tr>';
                                        foreach ($cliente as $valor) {
                                            echo '<td>' . htmlspecialchars($valor ?? '') . '</td>';
                                        }
                                        echo '</tr>';
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            echo '<tr><td colspan="54" class="error-data">Error al cargar los datos: ' . htmlspecialchars($e->getMessage()) . '</td></tr>';
                            error_log("Error en la consulta de clientes: " . $e->getMessage());
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Tab Contenido: Nueva Opción -->
<div id="new-tab" class="tab-content active" style="margin-bottom: 70px; overflow-y: visible;">
    <div class="container">

        <!-- Modal para el formulario de prospecto -->
        <div id="prospectoModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h2>Registro de prospecto</h2>



                <?php
                // Obtener el nombre del usuario desde la base de datos
                $nombre_usuario = '';
                try {
                    // Verificar que la función existe antes de usarla
                    if (function_exists('createPDOConnection')) {
                        $connection = createPDOConnection();
                    } else {
                        // Alternativa si la función no está disponible
                        $connection = new PDO(
                            "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                            'gestarse_ncornejo7_experian',
                            'N1c0l7as17',
                            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                        );
                    }
                    if ($connection) {
                        $query = "SELECT correo, COALESCE(nombre_usuario, SUBSTRING_INDEX(correo, '@', 1)) AS nombre_usuario FROM tb_experian_usuarios WHERE id = ?";
                        $stmt = $connection->prepare($query);
                        $stmt->execute([$_SESSION['usuario_id']]);
                        $result = $stmt->fetch(PDO::FETCH_ASSOC);
                        if ($result && isset($result['nombre_usuario'])) {
                            $nombre_usuario = $result['nombre_usuario'];
                        } else {
                            // Si no hay nombre_usuario, usar el correo como alternativa
                            $nombre_usuario = $_SESSION['usuario'];
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error al obtener nombre de usuario: " . $e->getMessage());
                    // En caso de error, usar el correo como alternativa
                    $nombre_usuario = $_SESSION['usuario'];
                }
                ?>

                <form id="formEjecutivos" method="POST" action="guardar_prospecto.php">
                <div class="section-header">DATOS DEL PROSPECTO</div>

                <table>
                    <tr>
                        <td class="label">Nombre Ejecutivo</td>
                        <td class="input-cell">
                            <input type="text" name="nombre_prospecto" title="Nombre Prospecto" value="<?php echo htmlspecialchars($nombre_usuario); ?>" readonly required>
                        </td>
                        <td class="label">Rut</td>
                        <td class="input-cell">
                            <input type="text"
                                name="rut_ejecutivo"
                                title="Rut"
                                class="rut-input"
                                pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                placeholder="12.345.678-9"
                                maxlength="12"
                                required>
                            <div class="rut-message">Formato: 12.345.678-9</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Razón Social</td>
                        <td class="input-cell">
                            <input type="text" name="razon_social" title="Razón Social" required>
                        </td>
                        <td class="label">Rubro</td>
                        <td class="input-cell">
                            <input type="text" name="rubro" title="Rubro" required>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Contacto</td>
                        <td class="input-cell">
                            <input type="text" name="contacto" title="Contacto" required>
                        </td>
                        <td class="label">Teléfono</td>
                        <td class="input-cell">
                            <input type="tel" name="telefono" title="Teléfono"
                                pattern="[0-9]{9}"
                                maxlength="9"
                                placeholder="912345678"
                                required>
                            <div class="info-message">Debe contener 9 dígitos</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Fecha</td>
                        <td class="input-cell">
                            <input type="date" name="fecha" title="Fecha" value="<?php echo date('Y-m-d'); ?>" required>
                        </td>
                        <td class="label">Estado</td>
                        <td class="input-cell">
                            <select name="estado" title="Estado" required>
                                <option value="">Seleccione...</option>
                                <!-- <option value="Interesado">Interesado</option>
                                <option value="No interesado">No interesado</option> -->
                                <option value="Envio información">Envio información</option>
                                <option value="Negociación">Negociación</option>
                                <option value="Cerrado">Cerrado</option>
                                <option value="B.O. Experian">B.O. Experian</option>
                                <option value="Proceso de Firma">Proceso de Firma</option>
                                <option value="Firmado">Firmado</option>
                                <option value="Habilitado">Habilitado</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Observaciones</td>
                        <td class="input-cell">
                            <textarea name="observaciones" title="Observaciones" rows="3" style="width: 100%; resize: vertical;"></textarea>
                        </td>
                        <td class="label"></td>
                        <td class="input-cell"></td>
                    </tr>
                </table>

                <div class="form-actions">
                    <button type="submit" class="btn-submit" style="background-color: green;">
                        <i class="fa fa-save"></i> Guardar prospecto
                    </button>
                    <button type="reset" class="btn-reset">
                        <i class="fa fa-eraser"></i> Limpiar Formulario
                    </button>
                </div>
            </form>
            </div>
        </div>

        <div class="ejecutivos-table-container" style="margin-top: 30px;">
            <div class="section-header">Registros de prospectos</div>

            <!-- Botón para abrir el modal -->
            <div class="button-container" style="margin-bottom: 15px;">
                <button id="openProspectoModal" class="btn-modal-open" style="background-color: green;">
                    <i class="fa fa-plus-circle"></i> Nuevo Prospecto
                </button>
                <button id="exportEjecutivos" class="export-button">
                    <i class="fa fa-download"></i> Descargar Prospectos
                </button>
            </div>
            <div class="table-controls" style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
                <input type="text" id="ejecutivos-search" class="table-search" placeholder="Buscar...">
                <?php if (isset($es_admin) && !$es_admin): ?>
                <div class="filter-indicator" style="background-color: #e7f3ff; padding: 5px 10px; border-radius: 4px; font-size: 14px; color: #0056b3;">
                    <i class="fa fa-filter"></i> Mostrando solo sus prospectos
                </div>
                <?php endif; ?>
            </div>
            <div class="table-wrapper">
                <table id="ejecutivos-table">
                    <thead>
                        <tr>
                            <th>Acciones</th>
                            <th>Nombre Ejecutivo</th>
                            <th>RUT</th>
                            <th>Razón Social</th>
                            <th>Rubro</th>
                            <th>Contacto</th>
                            <th>Teléfono</th>
                            <th>Fecha</th>
                            <th>Estado</th>
                            <th>Observaciones</th>
                            <th>Fecha Registro</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- La tabla se llenará dinámicamente con JavaScript -->
                        <tr><td colspan="11" class="loading-data">Cargando datos...</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Bitácora -->
<div id="bitacoraModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <span class="close-modal">&times;</span>
            <h2>Bitácora de Actividades</h2>
            <div id="bitacora-info" class="modal-subtitle"></div>
        </div>
        <div class="modal-body">
            <form id="formBitacora" method="post" action="endpoints/guardar_bitacora.php">
                <input type="hidden" name="rut_ejecutivo" id="bitacora_rut">

                <div class="form-group">
                    <label for="bitacora_estado">Estado:</label>
                    <select name="estado" id="bitacora_estado" required>
                        <option value="">Seleccione...</option>
                        <option value="Envio información">Envio información</option>
                        <option value="Negociación">Negociación</option>
                        <option value="Cerrado">Cerrado</option>
                        <option value="B.O. Experian">B.O. Experian</option>
                        <option value="Proceso de Firma">Proceso de Firma</option>
                        <option value="Firmado">Firmado</option>
                        <option value="Habilitado">Habilitado</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="bitacora_observaciones">Observaciones:</label>
                    <textarea name="observaciones" id="bitacora_observaciones" rows="4" required></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-submit">
                        <i class="fa fa-save"></i> Guardar Registro
                    </button>
                </div>
            </form>

            <div class="bitacora-historial">
                <div class="bitacora-header">
                    <h3>Historial de Actividades</h3>
                    <a href="#" class="view-all">Ver todo <i class="fa fa-angle-right"></i></a>
                </div>
                <div class="timeline-container">
                    <div class="timeline" id="bitacora-timeline">
                        <!-- Aquí se cargarán los registros de la bitácora en formato timeline -->
                        <div class="loading-data">Cargando actividades...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer Navigation -->
<div class="app-footer">
    <button class="footer-tab active" data-tab="new-tab">
        <i class="fa fa-user-plus"></i>
        <span>Prospecto</span>
    </button>
    <button class="footer-tab" data-tab="form-tab">
        <i class="fa fa-file-text"></i>
        <span>Venta</span>
    </button>
    <?php if ($_SESSION['usuario_id'] == 4): ?>
    <button class="footer-tab" data-tab="table-tab">
        <i class="fa fa-table"></i>
        <span>Registros</span>
    </button>
    <?php endif; ?>
</div>

<!-- Scripts -->
<script>
// Define user permissions for JavaScript (will be overridden by the calculated value below)
window.userIsAdmin = <?php echo $_SESSION['usuario_id'] == 4 ? 'true' : 'false'; ?>;

// Arreglo para botones de navegación entre secciones del formulario
document.addEventListener('DOMContentLoaded', function() {
    // Verificar que los botones existan
    var nextButtons = document.querySelectorAll('#form-tab .btn-next');
    var prevButtons = document.querySelectorAll('#form-tab .btn-prev');
    var stepIndicators = document.querySelectorAll('#form-tab .step-indicator');
    
    console.log('Inicializando navegación de formulario - Botones siguiente:', nextButtons.length);
    console.log('Botones anterior:', prevButtons.length);

    // Debug: mostrar información de los botones
    nextButtons.forEach(function(button, index) {
        console.log(`Botón siguiente ${index + 1}:`, {
            'data-next': button.getAttribute('data-next'),
            'class': button.className,
            'text': button.textContent.trim()
        });
    });

    prevButtons.forEach(function(button, index) {
        console.log(`Botón anterior ${index + 1}:`, {
            'data-prev': button.getAttribute('data-prev'),
            'class': button.className,
            'text': button.textContent.trim()
        });
    });
    
    // Función para mostrar una sección específica (global para evitar conflictos)
    window.showFormSection = function(sectionNumber) {
        // Convertir a número para evitar NaN
        const seccionNum = parseInt(sectionNumber);

        if (isNaN(seccionNum) || seccionNum < 1 || seccionNum > 3) {
            console.error('Número de sección inválido:', sectionNumber);
            return;
        }

        console.log('Cambiando a sección:', seccionNum);

        // Ocultar todas las secciones
        document.querySelectorAll('#form-tab .section-container').forEach(function(section) {
            section.classList.remove('active');
        });

        // Mostrar la sección seleccionada
        var targetSection = document.querySelector('#form-tab #section' + seccionNum);
        if (targetSection) {
            targetSection.classList.add('active');
        } else {
            console.error('No se encontró la sección:', seccionNum);
            return;
        }

        // Actualizar indicadores
        document.querySelectorAll('#form-tab .step-indicator').forEach(function(indicator) {
            indicator.classList.remove('active');
            if (indicator.getAttribute('data-step') === seccionNum.toString()) {
                indicator.classList.add('active');
            }
        });

        // Actualizar barra de progreso
        var progressFill = document.querySelector('#form-tab .progress-line .fill');
        if (progressFill) {
            var percentage = ((seccionNum - 1) / 2) * 100;
            progressFill.style.width = percentage + '%';
        }

        // Manejar validación de secciones después del cambio
        if (typeof manejarValidacionSecciones === 'function') {
            manejarValidacionSecciones();
        }
    };

    // Log para confirmar que la función global se definió
    console.log('Función showFormSection definida globalmente:', typeof window.showFormSection);
    
    // Configurar botones "Siguiente"
    nextButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var nextSection = this.getAttribute('data-next');
            console.log('Botón siguiente clickeado, ir a sección:', nextSection);

            if (nextSection && nextSection !== '') {
                window.showFormSection(parseInt(nextSection));
            } else {
                console.error('No se encontró data-next en el botón');
            }
        });
    });

    // Configurar botones "Anterior"
    prevButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var prevSection = this.getAttribute('data-prev');
            console.log('Botón anterior clickeado, ir a sección:', prevSection);

            if (prevSection && prevSection !== '') {
                window.showFormSection(parseInt(prevSection));
            } else {
                console.error('No se encontró data-prev en el botón');
            }
        });
    });
    
    // Configurar indicadores de paso
    stepIndicators.forEach(function(indicator) {
        indicator.addEventListener('click', function(e) {
            var stepNumber = this.getAttribute('data-step');
            console.log('Indicador clickeado, ir a sección:', stepNumber);
            window.showFormSection(stepNumber);
        });
    });

    // Inicializar mostrando la sección 1
    console.log('Inicializando formulario en sección 1');
    window.showFormSection(1);
});
</script>

<?php
// Pre-load table data to avoid additional requests
// Cargar datos para todos los usuarios

    // Get client records data
    try {
        // Inicializar con un array vacío para evitar errores
        echo "<script>window.preloadedClientData = " . json_encode(['success' => true, 'data' => []]) . ";</script>\n";
    } catch (Exception $e) {
        error_log("Error loading client data: " . $e->getMessage());
        echo "<script>window.preloadedClientData = {success: false, message: 'Error al cargar los datos iniciales: " . addslashes($e->getMessage()) . "'};</script>\n";
    }

    // Get prospects data
    try {
        // Verificar que la función existe antes de usarla
        if (function_exists('createPDOConnection')) {
            $connection = createPDOConnection();
        } else {
            // Alternativa si la función no está disponible
            $connection = new PDO(
                "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                'gestarse_ncornejo7_experian',
                'N1c0l7as17',
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
        }
        if ($connection) {
            // Verificar si el usuario tiene rol de administrador
            $usuario_id = $_SESSION['usuario_id'];
            $es_admin = false;
            $query_rol = "SELECT id, correo, nombre_usuario, rol FROM tb_experian_usuarios WHERE id = ?";
            $stmt_rol = $connection->prepare($query_rol);
            $stmt_rol->execute([$usuario_id]);
            $usuario = $stmt_rol->fetch(PDO::FETCH_ASSOC);

            // Registrar información del usuario para depuración
            error_log("form_experian2.php - Usuario ID: " . $usuario_id . " - Datos: " . json_encode($usuario));

            // Forzar a que todos los usuarios que no sean explícitamente administradores vean solo sus prospectos
            if ($usuario && isset($usuario['rol']) && ($usuario['rol'] == 'admin' || $usuario['rol'] == 'administrador')) {
                $es_admin = true;
                error_log("form_experian2.php - Usuario " . $usuario['nombre_usuario'] . " (ID: " . $usuario_id . ") es administrador");
            } else {
                $es_admin = false;
                error_log("form_experian2.php - Usuario " . ($usuario ? $usuario['nombre_usuario'] : 'desconocido') . " (ID: " . $usuario_id . ") NO es administrador");
            }

            // Definir la variable para usar en JavaScript - se hará más adelante en el script principal

            // Fetch prospects with last bitacora data
            if (isset($es_admin) && $es_admin) {
                // Si es admin, mostrar todos los prospectos
                $prospectsQuery = "SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC";
                $prospectsStmt = $connection->prepare($prospectsQuery);
                error_log("form_experian2.php - Consulta SQL para administrador: " . $prospectsQuery);
                $prospectsStmt->execute();
            } else {
                // Si no es admin, filtrar por usuario_id
                $prospectsQuery = "SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC";
                $prospectsStmt = $connection->prepare($prospectsQuery);
                $prospectsStmt->bindParam(':usuario_id', $usuario_id, PDO::PARAM_INT);
                error_log("form_experian2.php - Consulta SQL para usuario regular: " . $prospectsQuery . " (usuario_id = " . $usuario_id . ")");
                $prospectsStmt->execute();
            }

            // Registrar cuántos registros se encontraron
            $prospectsData = $prospectsStmt->fetchAll(PDO::FETCH_ASSOC);
            error_log("form_experian2.php - Se encontraron " . count($prospectsData) . " prospectos para el usuario ID: " . $usuario_id);

            // Convert to JSON for JavaScript
            echo "<script>window.preloadedProspectsData = " . json_encode(['success' => true, 'data' => $prospectsData]) . ";</script>\n";

            // Definir la variable userIsAdmin basada en el rol calculado
            echo "<script>window.userIsAdmin = " . ($es_admin ? "true" : "false") . ";</script>\n";
        } else {
            // Si no hay conexión, definir valores por defecto
            echo "<script>window.preloadedProspectsData = {success: false, message: 'No se pudo establecer conexión con la base de datos'};</script>\n";
            echo "<script>window.userIsAdmin = false;</script>\n";
        }
    } catch (Exception $e) {
        error_log("Error loading prospects data: " . $e->getMessage());
        echo "<script>window.preloadedProspectsData = {success: false, message: 'Error al cargar los datos iniciales: " . addslashes($e->getMessage()) . "'};</script>\n";
        // Definir userIsAdmin como false en caso de error
        echo "<script>window.userIsAdmin = false;</script>\n";
    }
?>

<script src="<?php echo version_url('js/form_experian.js'); ?>?v=<?php echo filemtime(__DIR__ . '/js/form_experian.js'); ?>"></script>


<script>
// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado - Configurando eventos del footer directamente');

    // Event listener para el botón de exportar registros
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'exportClients') {
            e.preventDefault();
            exportarClientesDirecto();
        }
    });

    // Cargar la tabla de prospectos automáticamente para todos los usuarios
    if (typeof cargarEjecutivos === 'function') {
        console.log('Cargando tabla de prospectos automáticamente...');
        setTimeout(function() {
            cargarEjecutivos();
        }, 100);
    } else {
        console.warn('Función cargarEjecutivos no disponible en DOMContentLoaded');
    }

    // Obtener todos los botones del footer
    var footerTabs = document.querySelectorAll('.footer-tab');
    console.log('Tabs del footer encontrados:', footerTabs.length);

    // Configurar eventos de clic para cada botón
    footerTabs.forEach(function(tab) {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            var tabId = this.getAttribute('data-tab');
            console.log('Tab clickeado (script inline):', tabId);

            // Desactivar todos los tabs
            footerTabs.forEach(function(t) {
                t.classList.remove('active');
            });

            // Activar el tab actual
            this.classList.add('active');

            // Ocultar todos los contenidos
            var tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(function(content) {
                content.classList.remove('active');
            });

            // Mostrar el contenido correspondiente
            var activeContent = document.getElementById(tabId);
            if (activeContent) {
                activeContent.classList.add('active');
            }

            // Cargar datos si es necesario
            if (tabId === 'new-tab') {
                setTimeout(function() {
                    if (typeof cargarEjecutivos === 'function') {
                        cargarEjecutivos();
                    }
                }, 100);
            } else if (tabId === 'table-tab' && window.userIsAdmin) {
                setTimeout(function() {
                    if (typeof loadTableData === 'function') {
                        loadTableData();
                    } else {
                        console.log('Función loadTableData no disponible, los datos ya están cargados en el HTML');
                    }
                }, 100);
            }
        });
    });

    // Configurar búsqueda en tabla de prospectos directamente
    var ejecutivosSearch = document.getElementById('ejecutivos-search');
    if (ejecutivosSearch) {
        console.log('Configurando búsqueda para tabla de prospectos (script inline)');

        ejecutivosSearch.addEventListener('input', function() {
            var searchText = this.value.toLowerCase();
            console.log('Búsqueda en tabla de prospectos:', searchText);

            var rows = document.querySelectorAll('#ejecutivos-table tbody tr');
            rows.forEach(function(row) {
                var text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }

    // Configurar búsqueda en tabla de registros directamente
    var tableSearch = document.getElementById('tableSearch');
    if (tableSearch) {
        console.log('Configurando búsqueda para tabla de registros (script inline)');

        tableSearch.addEventListener('input', function() {
            var searchText = this.value.toLowerCase();
            console.log('Búsqueda en tabla de registros:', searchText);

            var rows = document.querySelectorAll('#user-table tbody tr');
            rows.forEach(function(row) {
                var text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }

    // Configurar botón de Nuevo Prospecto
    var openProspectoModal = document.getElementById('openProspectoModal');
    var prospectoModal = document.getElementById('prospectoModal');
    if (openProspectoModal && prospectoModal) {
        console.log('Configurando botón de Nuevo Prospecto (script inline)');

        openProspectoModal.addEventListener('click', function() {
            console.log('Botón Nuevo Prospecto clickeado (script inline)');
            prospectoModal.style.display = 'block';
        });

        // Cerrar el modal con el botón X
        var closeModalBtn = prospectoModal.querySelector('.close-modal');
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', function() {
                console.log('Cerrando modal (script inline)');
                prospectoModal.style.display = 'none';
            });
        }

        // Cerrar el modal al hacer clic fuera de él
        window.addEventListener('click', function(event) {
            if (event.target === prospectoModal) {
                console.log('Cerrando modal (clic fuera) (script inline)');
                prospectoModal.style.display = 'none';
            }
        });

        // Configurar formulario de prospectos
        var formEjecutivos = document.getElementById('formEjecutivos');
        if (formEjecutivos) {
            console.log('Configurando formulario de prospectos (script inline)');

            // Eliminar cualquier manejador de eventos previo para evitar duplicación
            formEjecutivos.removeEventListener('submit', formEjecutivosSubmitHandler);

            // Definir el manejador de eventos como una función con nombre para poder eliminarlo
            function formEjecutivosSubmitHandler(e) {
                e.preventDefault();
                console.log('Formulario de prospectos enviado (script inline)');

                if (!confirm('¿Está seguro de guardar este prospecto?')) {
                    return false;
                }

                // Mostrar indicador de carga
                $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');

                // Obtener datos del formulario
                var formData = new FormData(this);

                // Convertir FormData a objeto para depuración
                var formDataObj = {};
                formData.forEach(function(value, key) {
                    formDataObj[key] = value;
                });
                console.log('Datos del formulario:', formDataObj);

                // Enviar mediante fetch para evitar problemas con jQuery
                fetch(formEjecutivos.getAttribute('action'), {
                    method: 'POST',
                    body: new URLSearchParams(new FormData(formEjecutivos))
                })
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    console.log('Respuesta del servidor:', data);

                    // Eliminar indicador de carga
                    $('.loading-overlay').remove();

                    if (data.success) {
                        // Mostrar mensaje de éxito
                        alert(data.message);

                        // Cerrar el modal
                        prospectoModal.style.display = 'none';

                        // Limpiar el formulario
                        formEjecutivos.reset();

                        // Si el usuario tiene permisos, actualizar la tabla
                        if (window.userIsAdmin) {
                            setTimeout(function() {
                                if (typeof cargarEjecutivos === 'function') {
                                    cargarEjecutivos();
                                } else {
                                    // Recargar la página como alternativa
                                    window.location.reload();
                                }
                            }, 500);
                        }
                    } else {
                        // Mostrar mensaje de error
                        alert(data.message || 'Error al guardar el prospecto');
                    }
                })
                .catch(function(error) {
                    console.error('Error al enviar formulario:', error);
                    $('.loading-overlay').remove();
                    alert('Error al enviar el formulario. Revise la consola para más detalles.');
                });
            }

            // Agregar el manejador de eventos
            formEjecutivos.addEventListener('submit', formEjecutivosSubmitHandler);
        }
    }

    // Configurar botón de Descargar Prospectos
    var exportEjecutivos = document.getElementById('exportEjecutivos');
    if (exportEjecutivos) {
        console.log('Configurando botón de Descargar Prospectos (script inline)');

        $("#exportEjecutivos").off('click').on("click", function(e) {
            e.preventDefault();
            console.log('Botón Descargar Prospectos clickeado (script inline)');

            // Mostrar indicador de carga
            $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');

            // Usar ruta relativa en lugar de absoluta
            $.ajax({
                url: 'endpoints/exportar_prospectos.php', // Asegúrate que esta ruta sea correcta
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Exportar a Excel
                        var wb = XLSX.utils.book_new();
                        var ws = XLSX.utils.json_to_sheet(response.data);
                        XLSX.utils.book_append_sheet(wb, ws, "Prospectos");
                        XLSX.writeFile(wb, "Prospectos_" + new Date().toISOString().slice(0,10) + ".xlsx");
                        alert("Archivo descargado correctamente");
                    } else {
                        alert("Error: " + response.message);
                    }
                    $('.loading-overlay').remove();
                },
                error: function(xhr, status, error) {
                    console.error("Error AJAX en exportación: ", xhr.responseText);
                    alert("Error al exportar: verifique la ruta del endpoint");
                    $('.loading-overlay').remove();
                }
            });
        });
    }



    // Configurar botones de bitácora - usar un ID único para este manejador de eventos
    if (!window.bitacoraButtonHandlerAdded) {
        window.bitacoraButtonHandlerAdded = true;
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-bitacora') || e.target.closest('.btn-bitacora')) {
            var button = e.target.classList.contains('btn-bitacora') ? e.target : e.target.closest('.btn-bitacora');
            var rut = button.getAttribute('data-rut');
            var nombre = button.getAttribute('data-nombre');
            var razon = button.getAttribute('data-razon');

            console.log('Botón de bitácora clickeado para RUT:', rut, '(script inline)');

            var bitacoraModal = document.getElementById('bitacoraModal');
            var bitacoraRut = document.getElementById('bitacora_rut');
            var bitacoraInfo = document.getElementById('bitacora-info');

            if (bitacoraModal && bitacoraRut && bitacoraInfo) {
                // Establecer información en el modal
                bitacoraRut.value = rut;
                bitacoraInfo.innerHTML = '<strong>Cliente:</strong> ' + razon + ' | <strong>Ejecutivo:</strong> ' + nombre + ' | <strong>RUT:</strong> ' + rut;

                // Cargar registros de bitácora
                if (typeof cargarBitacora === 'function') {
                    cargarBitacora(rut);
                } else {
                    // Implementación alternativa si la función no está disponible
                    console.log('Función cargarBitacora no disponible, implementando alternativa');

                    // Mostrar indicador de carga
                    $('#bitacora-timeline').html('<div class="loading-data">Cargando actividades...</div>');

                    // Cargar registros mediante AJAX con más información de depuración
                    console.log('Intentando cargar bitácora para RUT:', rut);

                    // Mostrar indicador de carga
                    $('#bitacora-timeline').html('<div class="loading-data">Cargando actividades...</div>');

                    // Construir la URL completa para depuración
                    var bitacoraUrl = 'endpoints/obtener_bitacora.php?rut=' + encodeURIComponent(rut);
                    console.log('URL de la bitácora:', bitacoraUrl);

                    // Definir el flujo de estados en orden
                    var flujoEstados = [
                        "Envio información",
                        "Negociación",
                        "Cerrado",
                        "B.O. Experian",
                        "Proceso de Firma",
                        "Firmado",
                        "Habilitado"
                    ];

                    $.ajax({
                        url: bitacoraUrl,
                        type: 'GET',
                        dataType: 'json',
                        cache: false,
                        beforeSend: function(xhr) {
                            console.log('Enviando solicitud AJAX...');
                        },
                        success: function(response) {
                            console.log('Respuesta recibida:', response);
                            if (response && response.success) {
                                // Filtrar las opciones del select según el último estado
                                if (response.ultimo_estado) {
                                  var selectEstado = document.getElementById('bitacora_estado');
                                  var indiceUltimo = flujoEstados.indexOf(response.ultimo_estado);

                                  if (indiceUltimo !== -1) {
                                    // Guardar todas las opciones originales si no lo hemos hecho antes
                                    if (!selectEstado.dataset.opcionesOriginales) {
                                      var opcionesOriginales = [];
                                      for (var i = 0; i < selectEstado.options.length; i++) {
                                        opcionesOriginales.push({
                                          value: selectEstado.options[i].value,
                                          text: selectEstado.options[i].text
                                        });
                                      }
                                      selectEstado.dataset.opcionesOriginales = JSON.stringify(opcionesOriginales);
                                    }

                                    // Limpiar el select
                                    selectEstado.innerHTML = '<option value="">Seleccione...</option>';

                                    // Agregar solo las opciones válidas (igual o posterior al último estado)
                                    for (var j = indiceUltimo; j < flujoEstados.length; j++) {
                                      var option = document.createElement('option');
                                      option.value = flujoEstados[j];
                                      option.text = flujoEstados[j];
                                      selectEstado.appendChild(option);
                                    }

                                    console.log('Select de estados filtrado según último estado:', response.ultimo_estado);
                                  }
                                } else {
                                  // Si no hay último estado, restaurar todas las opciones originales
                                  var selectEstado = document.getElementById('bitacora_estado');
                                  if (selectEstado.dataset.opcionesOriginales) {
                                    var opcionesOriginales = JSON.parse(selectEstado.dataset.opcionesOriginales);
                                    selectEstado.innerHTML = '<option value="">Seleccione...</option>';
                                    opcionesOriginales.forEach(function(opcion) {
                                      if (opcion.value) { // Ignorar la opción vacía
                                        var option = document.createElement('option');
                                        option.value = opcion.value;
                                        option.text = opcion.text;
                                        selectEstado.appendChild(option);
                                      }
                                    });
                                  }
                                }

                                if (response.data.length === 0) {
                                    $('#bitacora-timeline').html('<div class="no-data">No hay registros disponibles</div>');
                                    console.log('No hay registros disponibles');
                                } else {
                                    let html = '';
                                    $.each(response.data, function(index, registro) {
                                        // Determinar la clase de estado para el color del punto
                                        let estadoClass = 'estado-default';
                                        if (registro.estado.toLowerCase().includes('pendiente')) {
                                          estadoClass = 'estado-pendiente';
                                        } else if (registro.estado.toLowerCase().includes('proceso') || registro.estado.toLowerCase().includes('en curso')) {
                                          estadoClass = 'estado-en-proceso';
                                        } else if (registro.estado.toLowerCase().includes('completado') || registro.estado.toLowerCase().includes('finalizado')) {
                                          estadoClass = 'estado-completado';
                                        } else if (registro.estado.toLowerCase().includes('cancelado') || registro.estado.toLowerCase().includes('rechazado')) {
                                          estadoClass = 'estado-cancelado';
                                        }

                                        // Formatear la fecha para mostrar en formato corto
                                        let fechaRegistro = new Date(registro.fecha_registro);
                                        let fechaFormateada = fechaRegistro.toLocaleDateString('es-CL') + ' ' +
                                                            fechaRegistro.toLocaleTimeString('es-CL', {hour: '2-digit', minute:'2-digit'});

                                        html += '<div class="timeline-item ' + estadoClass + '">';
                                        html += '  <div class="timeline-content">';
                                        html += '    <div class="timeline-header">';
                                        html += '      <div class="timeline-text"><span class="highlight">' + registro.estado + '</span></div>';
                                        html += '      <div class="timeline-time">' + fechaFormateada + '</div>';
                                        html += '    </div>';
                                        html += '    <div class="timeline-subtext">' + registro.observaciones + '</div>';
                                        html += '    <div class="timeline-footer">';
                                        html += '      <div>Registrado por: <span class="highlight">' + (registro.nombre_usuario || 'N/A') + '</span></div>';
                                        html += '    </div>';
                                        html += '  </div>';
                                        html += '</div>';
                                    });
                                    $('#bitacora-timeline').html(html);
                                    console.log('Registros cargados correctamente:', response.data.length);
                                }
                            } else {
                                $('#bitacora-timeline').html('<div class="error-data">Error: ' + (response ? response.message : 'Respuesta inválida') + '</div>');
                                console.error('Error al cargar bitácora:', response ? response.message : 'Respuesta inválida');
                            }
                        },
                        error: function(xhr, status, error) {
                            $('#bitacora-timeline').html('<div class="error-data">Error de conexión: ' + status + '</div>');
                            console.error('Error AJAX al cargar bitácora:', {
                                status: status,
                                error: error,
                                response: xhr.responseText,
                                url: bitacoraUrl
                            });

                            // Intentar analizar la respuesta para obtener más información
                            try {
                                if (xhr.responseText) {
                                    var errorResponse = JSON.parse(xhr.responseText);
                                    console.error('Detalles del error:', errorResponse);
                                }
                            } catch (e) {
                                console.error('No se pudo analizar la respuesta de error:', xhr.responseText);
                            }
                        }
                    });
                }

                // Mostrar el modal
                bitacoraModal.style.display = 'block';

                // Configurar cierre del modal
                var closeBitacoraBtn = bitacoraModal.querySelector('.close-modal');
                if (closeBitacoraBtn) {
                    closeBitacoraBtn.addEventListener('click', function() {
                        console.log('Cerrando modal de bitácora (script inline)');
                        bitacoraModal.style.display = 'none';
                    });
                }

                // Cerrar el modal al hacer clic fuera de él
                window.addEventListener('click', function(event) {
                    if (event.target === bitacoraModal) {
                        console.log('Cerrando modal de bitácora (clic fuera) (script inline)');
                        bitacoraModal.style.display = 'none';
                    }
                });
            }
        }
    });
    }

    // Configurar formulario de bitácora
    var formBitacora = document.getElementById('formBitacora');
    if (formBitacora) {
        console.log('Configurando formulario de bitácora (script inline)');

        // Eliminar todos los manejadores de eventos previos para evitar duplicación
        var formClone = formBitacora.cloneNode(true);
        formBitacora.parentNode.replaceChild(formClone, formBitacora);
        formBitacora = formClone;
        formBitacora.id = 'formBitacora';

        // Agregar un nuevo manejador de eventos
        $("#formBitacora").off('submit').on("submit", function(e) {
            e.preventDefault();
            var formData = $(this).serialize();
            var rutEjecutivo = $("#bitacora_rut").val();

            // Mostrar overlay
            $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        $("#formBitacora")[0].reset();
                        $("#bitacoraModal").hide();
                        
                        // Llamar directamente a obtener_prospectos.php sin intermediarios
                        $.ajax({
                            url: 'endpoints/obtener_prospectos.php', 
                            type: 'GET',
                            dataType: 'json',
                            data: { t: new Date().getTime() }, // Evitar caché
                            success: function(tableData) {
                                console.log('ACTUALIZAR TABLA: Datos recibidos', tableData);
                                if (tableData.success) {
                                    actualizarTablaProspectos(tableData.data);
                                }
                            }
                        });
                    } else {
                        alert("Error: " + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error:", xhr.responseText);
                    // Usar el sistema de notificaciones en lugar de alert
                    mostrarMensaje("Error al guardar el registro", "error", 6000);
                },
                complete: function() {
                    $('.loading-overlay').remove();
                }
            });
        });
    }

    // Función para cargar datos de la tabla de clientes (opcional, ya que los datos se cargan en el HTML)
    window.loadTableData = function() {
        console.log('Función loadTableData llamada - Los datos ya están cargados en el HTML');
        // Esta función está disponible por si se necesita recargar la tabla dinámicamente en el futuro
        // Por ahora, los datos se cargan directamente en el HTML desde PHP
    };

    // Función global para exportar clientes
    window.exportarClientesDirecto = function() {
        // Mostrar indicador de carga
        var loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'loading-overlay';
        loadingOverlay.innerHTML = '<div class="spinner"></div><div class="loading-text">Exportando registros...</div>';
        document.body.appendChild(loadingOverlay);

        // Realizar petición al endpoint
        fetch('endpoints/exportar_clientes.php')
            .then(response => response.json())
            .then(function(data) {
                if (data.success) {
                    // Verificar que XLSX esté disponible
                    if (typeof XLSX === 'undefined') {
                        alert('Error: Librería XLSX no está cargada');
                        return;
                    }

                    // Exportar a Excel
                    var wb = XLSX.utils.book_new();
                    var ws = XLSX.utils.json_to_sheet(data.data);
                    XLSX.utils.book_append_sheet(wb, ws, "Registros_Clientes");
                    XLSX.writeFile(wb, "Registros_Clientes_" + new Date().toISOString().slice(0,10) + ".xlsx");
                    alert("Archivo descargado correctamente (" + data.total + " registros)");
                } else {
                    alert("Error: " + data.message);
                }
                document.body.removeChild(loadingOverlay);
            })
            .catch(function(error) {
                console.error("Error en exportación de clientes: ", error);
                alert("Error al exportar: " + error.message);
                if (loadingOverlay.parentNode) {
                    document.body.removeChild(loadingOverlay);
                }
            });
    };
});
</script>

<!-- Script para reemplazar alerts con notificaciones estilizadas -->
<script>
// Reemplazar el alert para mostrar notificaciones estilizadas en formularios
$(document).ready(function() {
    // Reemplazar la función de manejo de formulario para venta (formulario principal)
    var originalFormExperian = $('#formExperian').off('submit');
    $('#formExperian').on('submit', function(e) {
        e.preventDefault();

        console.log('Formulario enviado - iniciando validación...');

        // Restaurar todos los campos required antes de la validación
        restaurarValidacionHTML5();

        // Deshabilitar temporalmente la validación HTML5 nativa para evitar errores en campos ocultos
        this.setAttribute('novalidate', 'novalidate');

        // Validación completa del formulario
        const resultadoValidacion = validarFormularioCompleto();

        if (!resultadoValidacion.esValido) {
            console.log('Validación falló:', resultadoValidacion.errores);
            // Rehabilitar validación HTML5 y manejar secciones
            this.removeAttribute('novalidate');
            manejarValidacionSecciones();
            return false;
        }

        // Validar archivos
        const erroresArchivos = validarArchivos();
        if (erroresArchivos.length > 0) {
            mostrarResumenErrores(erroresArchivos);
            // Rehabilitar validación HTML5 y manejar secciones
            this.removeAttribute('novalidate');
            manejarValidacionSecciones();
            return false;
        }

        // Confirmar antes de enviar
        if (!confirm('¿Está seguro de guardar este formulario?')) {
            // Rehabilitar validación HTML5 y manejar secciones
            this.removeAttribute('novalidate');
            manejarValidacionSecciones();
            return false;
        }
        
        // Deshabilitar botón de envío
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        
        // Mostrar indicador de carga
        $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');
        
        // Usar FormData para incluir archivos
        var formData = new FormData(this);
        
        // Para debugging
        console.log('Enviando datos del formulario...');
        
        $.ajax({
            url: 'guardar_formulario.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                console.log('Respuesta recibida:', response);
                if (response.success) {
                    // Limpiar todos los errores de validación
                    limpiarTodosLosErrores();

                    // Usar el nuevo sistema de notificaciones en lugar de alert
                    mostrarMensaje(response.message, 'success', 6000);

                    // Limpiar el formulario
                    $('#formExperian')[0].reset();

                    // Volver a la primera sección
                    if (typeof showManualFormSection === 'function') {
                        showManualFormSection(1);
                    }

                    // Scroll al inicio del formulario
                    document.querySelector('#formExperian').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                } else {
                    // Notificación de error
                    mostrarMensaje('Error: ' + response.message, 'error', 8000);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error al enviar formulario:', {
                    status: status,
                    error: error,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText
                });
                
                // Verificar si hay respuesta del servidor
                let mensaje = 'Error al guardar los datos';
                
                try {
                    if (xhr.responseText) {
                        const jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse && jsonResponse.message) {
                            mensaje = jsonResponse.message;
                        }
                    }
                } catch (e) {
                    console.log('Error al parsear respuesta:', e);
                }
                
                // Mostrar notificación de error
                mostrarMensaje(mensaje, 'error', 8000);
            },
            complete: function() {
                // Habilitar botón de envío
                submitBtn.prop('disabled', false);
                // Quitar indicador de carga
                $('.loading-overlay').remove();
                // Restaurar validación HTML5 y manejar secciones
                $('#formExperian')[0].removeAttribute('novalidate');
                manejarValidacionSecciones();
            }
        });
    });
});
</script>

</body>
</html>



