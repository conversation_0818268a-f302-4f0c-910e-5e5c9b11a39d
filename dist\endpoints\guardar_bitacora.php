<?php
// Configurar encabezados para permitir CORS y especificar el tipo de contenido
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Max-Age: 3600');

// Incluir el archivo de conexión a la base de datos
// Usar dirname para obtener la ruta absoluta al directorio padre
require_once dirname(__FILE__) . '/../con_db.php';

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar si el usuario está autenticado
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'Usuario no autenticado']);
    exit;
}

// Verificar si es una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Obtener datos del formulario
$rut_ejecutivo = isset($_POST['rut_ejecutivo']) ? trim($_POST['rut_ejecutivo']) : '';
$estado = isset($_POST['estado']) ? trim($_POST['estado']) : '';
$observaciones = isset($_POST['observaciones']) ? trim($_POST['observaciones']) : '';
$usuario_id = $_SESSION['usuario_id'];

// Validar datos
if (empty($rut_ejecutivo) || empty($estado) || empty($observaciones)) {
    echo json_encode(['success' => false, 'message' => 'Todos los campos son obligatorios']);
    exit;
}

// Definir el flujo de estados en orden
$flujo_estados = [
    "Envio información",
    "Negociación",
    "Cerrado",
    "B.O. Experian",
    "Proceso de Firma",
    "Firmado",
    "Habilitado"
];

try {
    // Registrar información de depuración
    error_log("Intentando guardar bitácora para RUT: " . $rut_ejecutivo);

    // Establecer conexión a la base de datos usando PDO
    try {
        // Configuración de la conexión a la base de datos
        $host = 'localhost';
        $port = 3306;
        $dbname = 'gestarse_experian';
        $username = 'gestarse_ncornejo7_experian';
        $password = 'N1c0l7as17';

        $connection = new PDO(
            "mysql:host=$host;dbname=$dbname;charset=utf8",
            $username,
            $password,
            array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
        );
        error_log("Conexión a la base de datos establecida correctamente");
    } catch (PDOException $e) {
        error_log("Error al conectar a la base de datos: " . $e->getMessage());
        throw new Exception("No se pudo establecer conexión con la base de datos: " . $e->getMessage());
    }

    // Obtener el último estado registrado para este RUT
    $query_ultimo_estado = "SELECT estado FROM tb_experian_prospecto_bitacora
                           WHERE rut_ejecutivo = :rut_ejecutivo
                           ORDER BY fecha_registro DESC LIMIT 1";
    $stmt_ultimo_estado = $connection->prepare($query_ultimo_estado);
    $stmt_ultimo_estado->bindParam(':rut_ejecutivo', $rut_ejecutivo);
    $stmt_ultimo_estado->execute();
    $ultimo_registro = $stmt_ultimo_estado->fetch(PDO::FETCH_ASSOC);

    // Si hay un registro previo, validar que el nuevo estado sea válido según el flujo
    if ($ultimo_registro) {
        $ultimo_estado = $ultimo_registro['estado'];
        $indice_ultimo = array_search($ultimo_estado, $flujo_estados);
        $indice_nuevo = array_search($estado, $flujo_estados);

        // Si el último estado existe en el flujo y el nuevo estado es anterior, rechazar
        if ($indice_ultimo !== false && $indice_nuevo !== false && $indice_nuevo < $indice_ultimo) {
            echo json_encode([
                'success' => false,
                'message' => 'No se puede retroceder en el flujo de estados. El último estado registrado es "' . $ultimo_estado . '"'
            ]);
            exit;
        }
    }

    // Preparar la consulta SQL
    $query = "INSERT INTO tb_experian_prospecto_bitacora (rut_ejecutivo, estado, observaciones, usuario_id)
              VALUES (:rut_ejecutivo, :estado, :observaciones, :usuario_id)";

    $stmt = $connection->prepare($query);

    // Vincular parámetros
    $stmt->bindParam(':rut_ejecutivo', $rut_ejecutivo);
    $stmt->bindParam(':estado', $estado);
    $stmt->bindParam(':observaciones', $observaciones);
    $stmt->bindParam(':usuario_id', $usuario_id);

    // Ejecutar la consulta
    if ($stmt->execute()) {
        // Actualizar el estado en la tabla de prospectos
        $updateQuery = "UPDATE tb_experian_prospecto
                        SET estado = :estado
                        WHERE rut_ejecutivo = :rut_ejecutivo";

        $updateStmt = $connection->prepare($updateQuery);
        $updateStmt->bindParam(':estado', $estado);
        $updateStmt->bindParam(':rut_ejecutivo', $rut_ejecutivo);

        // Ejecutar la actualización (no es crítico si falla)
        try {
            $updateStmt->execute();
        } catch (Exception $updateError) {
            error_log("Error al actualizar el estado del prospecto: " . $updateError->getMessage());
        }

        echo json_encode([
            'success' => true,
            'message' => 'Registro de bitácora guardado correctamente',
            'data' => [
                'rut_ejecutivo' => $rut_ejecutivo,
                'estado' => $estado,
                'observaciones' => $observaciones,
                'fecha_registro' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        throw new Exception("Error al guardar el registro de bitácora");
    }

} catch (Exception $e) {
    // Registrar el error
    error_log("Error al guardar bitácora: " . $e->getMessage());

    // Devolver respuesta de error
    echo json_encode([
        'success' => false,
        'message' => 'Error al guardar el registro: ' . $e->getMessage()
    ]);
}
?>
