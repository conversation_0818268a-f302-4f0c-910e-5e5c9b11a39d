<?php 

header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.


$inc = include("con_db.php");
session_start();
if (isset($_GET['valor'])) {

  $orden = $_GET['valor'];
  $usuario = $_GET['usuario'];

    $resultado = $conex->query(
    "   
    SELECT  
    t.correo_super  , t.email 
    , cali.mes_contable  ,  x.*
    FROM 
        tb_solicitud_excep_cal x
    LEFT JOIN 
        tb_user_tqw  t
    ON  
        x.tecnico = t.nombre 
    LEFT JOIN  
        (select IDEN_ORDEN 
            , mes_contable 
            from
                bd_Calidad30_report
        ) cali
    ON 
        x.orden = cali.IDEN_ORDEN
    WHERE 
        mes_contable = '2023-03-01'                      
    AND 
        IDEN_ORDEN = '$orden';"        
      );
  
  $fila = mysqli_fetch_assoc($resultado);

  }





?>



<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>APP TQW</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/logo_con.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="Suoer_Excep_Calid30.php">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">FORMULARIO REVISION DE CALIDAD REACTIVA</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <form action="Action_Super_ExcepCal.php" method="POST">
            
              <div class="form-group" style="display: none;">
              <input type="hidden" name="Usuario" value="<?php echo $usuario ?>">
              </div>
          
              <div class="form-group">
              <label class="form-label" for="exampleInputText">ORDEN</label>
              <input class="form-control" id="orden" type="text" name="orden" value=<?php echo $fila["orden"] ?>>
            </div>


            <div class="form-group">
              <label class="form-label" for="exampleInputText">RUT CLIENTE</label>
              <input class="form-control" id="Rut" name="Rut" type="text" value=<?php echo $fila["rut"] ?>>
            </div>

            <div class="form-group">
              <label class="form-label" for="exampleInputText">NOMBRE TECNICO</label>
              <input class="form-control" id="Nombre" type="text" name = "Nombre" value="<?php echo $fila["tecnico"]; ?>">
            </div>



            <div class="form-group">
              <label class="form-label" for="exampleTextarea1">Motivo Excepcion ingresada por el tecnico</label>
              <textarea class="form-control" id="DescFin" name="DescFin" cols="3" rows="5"
              ><?php echo $fila["motivo"]; ?></textarea>
            </div>

            <!-- <div class="form-group">
              <label class="form-label" for="exampleInputText">IDEN ORDEN 2</label>
              <input class="form-control" id="Orden2" type="text" name = "Orden2" value="<?php echo $fila["IDEN_ORDEN_2"] ?>">
            </div> -->


            <div class="form-group">
              <label class="form-label" for="defaultSelectLg">¿Corresponde excepcionar?</label>
              <select class="form-select form-select-lg" id="Aplica" name="Aplica"
                aria-label="Default select example">
                                                                                              
                <option value="APLICA">Si</option>
                <option value="NO APLICA">No</option>                                                

              </select>
            </div>



            <div class="form-group">
              <label class="form-label" for="defaultSelectLg">De las siguientes categorias. ¿A cual corresponde la revisión realizada?</label>
              <select class="form-select form-select-lg" id="Categoria" name="Categoria"
                aria-label="Default select example">
                                                                
                
                
                <option value="1">Tecnico / ICE</option>
                <option value="2">Tecnico / ICI</option>                                
                <option value="3">Redes / FEN</option>
                <option value="4">Redes / Falla masiva</option>
                <option value="5">Redes / Saturacion</option>
                <option value="6">Tercero / Acometida</option>
                <option value="7">VTR - Falla en equipo</option>
                <option value="8">VTR - Falla Provisionamiento</option>
                <option value="9">VTR - Plataforma</option>                
                <option value="10">Cliente / Capacitacion</option>
                <option value="11">Cliente/ Manipulacion</option>
                <option value="12">Cliente / Equipos</option>

              </select>
            </div>




            

            <div class="form-group">
              <label class="form-label" for="exampleTextarea1">Ingrese una observación en caso de complementar este caso</label>
              <textarea class="form-control" id="motivo" name="motivo" cols="3" rows="5"
              ></textarea>
            </div>



            <input type="submit" class="btn btn-primary w-100 d-flex align-items-center justify-content-center">
              
            </input>

            <a href="OTDIGITAL/<?php echo $fila["orden"] ?>.pdf" target="_blank">Ver PDF</a>



          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          
          <li class="active">
            <a href="SoporteCalidad.php">
              <i class="bi bi-collection"></i>
              <span>Rev Calidad React</span>
            </a>
          </li>

        </ul>
      </div>
    </div>
  </div>

  <script>
  $(document).ready(function() {
  $('a[href$=".pdf"]').click(function(e) {
  e.preventDefault();
  var href = $(this).attr('href');
  window.open(href, 'popup', 'width=600,height=800');
  });
  });
  </script>

  <!-- All JavaScript Files -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>