<?php
require_once("dist/con_db.php");

try {
    echo "<h2>Creando nuevos usuarios en tb_experian_usuarios</h2>\n";
    
    // Lista de usuarios a crear basada en la tabla proporcionada
    $usuarios = [
        [
            'nombre_usuario' => 'CATHERINE DEL CARMEN PINCHEIRA BRITO',
            'correo' => '<EMAIL>',
            'cargo' => 'SUPERVISORA',
            'rut_ejecutivo' => '13305541-K'
        ],
        [
            'nombre_usuario' => 'VICTOR ALFONSO PACHECO PACHECO',
            'correo' => 'victor.pache<PERSON>@inteletgroup.cl',
            'cargo' => 'EJECUTIVO DE VENTAS',
            'rut_ejecutivo' => '18192758-5'
        ],
        [
            'nombre_usuario' => 'CARLOS ANDRÉS MURILLO RESTREPO',
            'correo' => '<EMAIL>',
            'cargo' => 'EJECUTIVO DE VENTAS',
            'rut_ejecutivo' => '25926276-3'
        ],
        [
            'nombre_usuario' => 'JOHANNA LISSETE RIGO ESPINOZA',
            'correo' => '<EMAIL>',
            'cargo' => 'EJECUTIVO DE VENTAS',
            'rut_ejecutivo' => '12685256-8'
        ],
        [
            'nombre_usuario' => 'TANIA ELIZABETH MUÑOZ PIZARRO',
            'correo' => '<EMAIL>',
            'cargo' => 'EJECUTIVO DE VENTAS',
            'rut_ejecutivo' => '16520144-2'
        ]
    ];
    
    // Mapeo de cargo a rol
    $cargo_to_rol = [
        'SUPERVISORA' => 'admin',
        'EJECUTIVO DE VENTAS' => 'ejecutivos'
    ];
    
    // Preparar la consulta de inserción
    $stmt = $mysqli->prepare("INSERT INTO tb_experian_usuarios (nombre_usuario, correo, clave, rol, proyecto, rut_ejecutivo) VALUES (?, ?, ?, ?, ?, ?)");
    
    if (!$stmt) {
        throw new Exception("Error preparando la consulta: " . $mysqli->error);
    }
    
    $usuarios_creados = 0;
    $usuarios_existentes = 0;
    
    foreach ($usuarios as $usuario) {
        // Verificar si el usuario ya existe (por correo o RUT)
        $check_stmt = $mysqli->prepare("SELECT id FROM tb_experian_usuarios WHERE correo = ? OR rut_ejecutivo = ?");
        $check_stmt->bind_param("ss", $usuario['correo'], $usuario['rut_ejecutivo']);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            echo "⚠️ Usuario ya existe: " . htmlspecialchars($usuario['nombre_usuario']) . " (" . htmlspecialchars($usuario['correo']) . ")<br>\n";
            $usuarios_existentes++;
            $check_stmt->close();
            continue;
        }
        $check_stmt->close();
        
        // Generar clave temporal (se puede cambiar después)
        $clave_temporal = 'Temp2024!' . substr($usuario['rut_ejecutivo'], 0, 4);
        
        // Mapear cargo a rol
        $rol = $cargo_to_rol[$usuario['cargo']] ?? 'ejecutivos';
        
        // Proyecto fijo
        $proyecto = 'inteletGroup';
        
        // Insertar usuario
        $stmt->bind_param("ssssss", 
            $usuario['nombre_usuario'],
            $usuario['correo'],
            $clave_temporal,
            $rol,
            $proyecto,
            $usuario['rut_ejecutivo']
        );
        
        if ($stmt->execute()) {
            echo "✓ Usuario creado: " . htmlspecialchars($usuario['nombre_usuario']) . " - Rol: " . htmlspecialchars($rol) . " - Clave temporal: " . htmlspecialchars($clave_temporal) . "<br>\n";
            $usuarios_creados++;
        } else {
            echo "❌ Error creando usuario " . htmlspecialchars($usuario['nombre_usuario']) . ": " . $stmt->error . "<br>\n";
        }
    }
    
    $stmt->close();
    
    echo "<h3>Resumen:</h3>\n";
    echo "✅ Usuarios creados: $usuarios_creados<br>\n";
    echo "⚠️ Usuarios que ya existían: $usuarios_existentes<br>\n";
    echo "📋 Total procesados: " . count($usuarios) . "<br>\n";
    
    // Mostrar todos los usuarios actuales
    echo "<h3>Usuarios actuales en la tabla:</h3>\n";
    $result = $mysqli->query("SELECT id, nombre_usuario, correo, rol, proyecto, rut_ejecutivo FROM tb_experian_usuarios ORDER BY id");
    
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>ID</th><th>Nombre Usuario</th><th>Correo</th><th>Rol</th><th>Proyecto</th><th>RUT Ejecutivo</th></tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['nombre_usuario'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['correo']) . "</td>";
            echo "<td>" . htmlspecialchars($row['rol']) . "</td>";
            echo "<td>" . htmlspecialchars($row['proyecto'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['rut_ejecutivo'] ?? 'N/A') . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>📝 Nota importante:</h3>\n";
    echo "<p>Las claves temporales generadas deben ser cambiadas por los usuarios en su primer acceso.</p>\n";
    echo "<p>Formato de clave temporal: Temp2024! + primeros 4 dígitos del RUT</p>\n";
    
} catch (Exception $e) {
    echo "<h3>❌ Error: " . htmlspecialchars($e->getMessage()) . "</h3>\n";
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>
