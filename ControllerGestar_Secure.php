<?php
// Secure Authentication Controller - Improved Version
// Iniciar output buffering para capturar cualquier salida no deseada
ob_start();

// Enhanced security headers
header('Content-Type: application/json');
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT");
header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");

// Enhanced session security
ini_set('session.cookie_secure', 1);     // HTTPS only
ini_set('session.cookie_httponly', 1);   // No JavaScript access
ini_set('session.cookie_samesite', 'Strict'); // CSRF protection
ini_set('session.use_strict_mode', 1);   // Strict session handling

// Configuración para debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // No mostrar errores directamente al usuario

// Rate limiting configuration
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_TIME', 900); // 15 minutes

// Función para registrar errores en archivo de log específico
function log_auth_error($message, $level = 'ERROR') {
    $log_file = dirname(__FILE__) . '/auth_errors.log';
    $date = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $log_message = "[$date] [$level] [$ip] [UA: $user_agent] $message" . PHP_EOL;
    error_log($log_message, 3, $log_file);
}

// Function to validate password strength
function validatePassword($password) {
    return strlen($password) >= 8 &&
           preg_match('/[A-Z]/', $password) &&
           preg_match('/[a-z]/', $password) &&
           preg_match('/[0-9]/', $password) &&
           preg_match('/[^A-Za-z0-9]/', $password);
}

// Function to check rate limiting
function checkRateLimit($mysqli, $email, $ip) {
    $stmt = $mysqli->prepare("SELECT COUNT(*) as attempts FROM tb_login_attempts 
                             WHERE (email = ? OR ip_address = ?) 
                             AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)");
    $stmt->bind_param("ssi", $email, $ip, LOCKOUT_TIME);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    return $row['attempts'] < MAX_LOGIN_ATTEMPTS;
}

// Function to log login attempt
function logLoginAttempt($mysqli, $email, $ip, $success) {
    $stmt = $mysqli->prepare("INSERT INTO tb_login_attempts (email, ip_address, success, attempt_time) 
                             VALUES (?, ?, ?, NOW())");
    $stmt->bind_param("ssi", $email, $ip, $success);
    $stmt->execute();
    $stmt->close();
}

log_auth_error("Inicio de proceso de autenticación segura", 'INFO');

try {
    require_once("con_db.php");
    log_auth_error("Archivo con_db.php incluido correctamente", 'INFO');
    
    // Asegurar que la sesión está limpia antes de iniciar
    if (session_status() !== PHP_SESSION_NONE) {
        log_auth_error("Destruyendo sesión existente", 'INFO');
        session_destroy();
    }
    
    // Iniciar una nueva sesión
    session_start();
    session_regenerate_id(true);
    log_auth_error("Sesión iniciada. ID: " . session_id(), 'INFO');
    
    // Verificar que la solicitud sea POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Método no permitido");
    }
    
    // Enhanced input validation and sanitization
    if (!isset($_POST['rut']) || !isset($_POST['clave'])) {
        throw new Exception("Credenciales incompletas");
    }
    
    $rut = filter_var(trim($_POST['rut']), FILTER_SANITIZE_EMAIL);
    $clave = trim($_POST['clave']);
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    
    // Validate input
    if (empty($rut) || empty($clave)) {
        throw new Exception("Credenciales vacías");
    }
    
    log_auth_error("Credenciales recibidas para: " . $rut, 'INFO');
    
    // Check rate limiting
    if (!checkRateLimit($mysqli, $rut, $ip)) {
        log_auth_error("Rate limit exceeded for: " . $rut . " from IP: " . $ip, 'SECURITY');
        logLoginAttempt($mysqli, $rut, $ip, 0);
        
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => 'Demasiados intentos fallidos. Intente nuevamente en 15 minutos.',
            'timestamp' => time(),
            'lockout' => true
        ]);
        exit;
    }
    
    // Verificar la conexión
    if ($mysqli->connect_error) {
        throw new Exception("Error de conexión a la base de datos");
    }
    
    // Consultar la tabla tb_experian_usuarios - INCLUIR PROYECTO para redirección condicional
    $sql = "SELECT id, correo, clave, rol, nombre_usuario, proyecto FROM tb_experian_usuarios WHERE correo = ?";
    $stmt = $mysqli->prepare($sql);
    
    if ($stmt === false) {
        throw new Exception("Error en la preparación de la consulta");
    }
    
    $stmt->bind_param("s", $rut);
    
    if (!$stmt->execute()) {
        throw new Exception("Error al ejecutar la consulta");
    }
    
    $stmt->store_result();
    
    if ($stmt->num_rows === 0) {
        log_auth_error("Usuario no encontrado: $rut", 'WARNING');
        logLoginAttempt($mysqli, $rut, $ip, 0);
        
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => 'Credenciales incorrectas',
            'timestamp' => time()
        ]);
        exit;
    }
    
    $stmt->bind_result($db_id, $db_correo, $db_clave, $db_rol, $db_nombre_usuario, $db_proyecto);
    $stmt->fetch();
    
    // Enhanced password verification
    $password_valid = false;
    
    // Check if password is hashed (starts with $2y$ for bcrypt or $argon2 for argon2)
    if (strpos($db_clave, '$2y$') === 0 || strpos($db_clave, '$argon2') === 0) {
        // Use password_verify for hashed passwords
        $password_valid = password_verify($clave, $db_clave);
    } else {
        // Legacy plain text comparison (should be migrated)
        $password_valid = ($clave === $db_clave);
        
        // If valid, hash the password for future use
        if ($password_valid) {
            $hashed_password = password_hash($clave, PASSWORD_ARGON2ID);
            $update_stmt = $mysqli->prepare("UPDATE tb_experian_usuarios SET clave = ? WHERE id = ?");
            $update_stmt->bind_param("si", $hashed_password, $db_id);
            $update_stmt->execute();
            $update_stmt->close();
            log_auth_error("Password migrated to hash for user: $rut", 'INFO');
        }
    }
    
    if ($password_valid) {
        // Credenciales correctas
        log_auth_error("Autenticación exitosa para: $rut", 'INFO');
        logLoginAttempt($mysqli, $rut, $ip, 1);
        
        // Guardar datos en sesión con tokens de seguridad
        $_SESSION['usuario'] = $db_correo;
        $_SESSION['usuario_id'] = $db_id;
        $_SESSION['rol'] = $db_rol;
        $_SESSION['nombre_usuario'] = $db_nombre_usuario;
        $_SESSION['proyecto'] = $db_proyecto; // Agregar proyecto a la sesión
        $_SESSION['auth_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['session_time'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['ip_address'] = $ip;

        // Determinar URL de redirección basada en el proyecto
        $redirectUrl = '';
        if ($db_proyecto === 'experian') {
            // Usuarios legacy de Experian van al formulario actual
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/form_experian2.php';
        } elseif ($db_proyecto === 'inteletGroup') {
            // Usuarios nuevos de InteletGroup van a un formulario específico
            // TODO: Crear o especificar el formulario para InteletGroup
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/form_inteletgroup.php';
        } else {
            // Fallback por defecto (para casos donde proyecto sea NULL o tenga otro valor)
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/form_experian2.php';
        }

        log_auth_error("Usuario $rut con proyecto '$db_proyecto' será redirigido a: $redirectUrl", 'INFO');
        
        ob_clean();
        echo json_encode([
            'success' => true,
            'rol' => $db_rol,
            'usuario_id' => $db_id,
            'nombre_usuario' => $db_nombre_usuario,
            'proyecto' => $db_proyecto,
            'csrf_token' => $_SESSION['csrf_token'],
            'message' => 'Login exitoso',
            'timestamp' => time(),
            'redirectUrl' => $redirectUrl // Usar la URL determinada por el proyecto
        ]);
    } else {
        // Contraseña incorrecta
        log_auth_error("Contraseña incorrecta para: $rut", 'WARNING');
        logLoginAttempt($mysqli, $rut, $ip, 0);
        
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => 'Credenciales incorrectas',
            'timestamp' => time()
        ]);
    }
    
} catch (Exception $e) {
    log_auth_error("ERROR CRÍTICO: " . $e->getMessage(), 'ERROR');
    
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => 'Error al procesar la solicitud',
        'timestamp' => time()
    ]);
} finally {
    // Cerrar recursos
    if (isset($stmt) && $stmt !== false) {
        $stmt->close();
    }
    if (isset($mysqli)) {
        $mysqli->close();
    }
    
    // Finalizar output buffering
    if (ob_get_level()) {
        ob_end_flush();
    }
}
?>
