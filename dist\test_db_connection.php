<?php
// Archivo de prueba para verificar la conexión a la base de datos
session_start();

// Incluir archivo de conexión
require_once 'con_db.php';

// Verificar si hay sesión activa
if (!isset($_SESSION['usuario_id']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    echo json_encode([
        'success' => false,
        'message' => 'Usuario no autenticado o sin permisos para InteletGroup',
        'session_data' => $_SESSION
    ]);
    exit;
}

try {
    // Probar conexión a la base de datos
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // Probar una consulta simple
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM tb_experian_usuarios WHERE proyecto = 'inteletGroup'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => 'Conexión exitosa',
        'user_count' => $result['count'],
        'session_user_id' => $_SESSION['usuario_id'],
        'session_proyecto' => $_SESSION['proyecto']
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error de conexión a la base de datos: ' . $e->getMessage(),
        'error_code' => $e->getCode()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error general: ' . $e->getMessage()
    ]);
}
?>
