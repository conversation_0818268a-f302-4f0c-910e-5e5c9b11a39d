<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Header Area-->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content-->
      <div class="header-content header-style-four position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button-->
        <div class="back-button">
          <a href="home.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title-->
        <div class="page-heading">
          <h6 class="mb-0">Settings</h6>
        </div>

        <!-- User Profile-->
        <div class="user-profile-wrapper">
          <a class="user-profile-trigger-btn" href="#">
            <img src="img/bg-img/20.jpg" alt="">
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <div class="container">
      <!-- Setting Card-->
      <div class="card mb-3 shadow-sm">
        <div class="card-body direction-rtl">
          <p class="mb-2">Settings</p>

          <div class="single-setting-panel">
            <div class="form-check form-switch mb-2">
              <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault" checked>
              <label class="form-check-label" for="flexSwitchCheckDefault">Availability Status</label>
            </div>
          </div>

          <div class="single-setting-panel">
            <div class="form-check form-switch mb-2">
              <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault2" checked>
              <label class="form-check-label" for="flexSwitchCheckDefault2">Send Me Notifications</label>
            </div>
          </div>

          <div class="single-setting-panel">
            <div class="form-check form-switch mb-2">
              <input class="form-check-input" type="checkbox" id="darkSwitch">
              <label class="form-check-label" for="darkSwitch">Dark Mode</label>
            </div>
          </div>

          <div class="single-setting-panel">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="rtlSwitch">
              <label class="form-check-label" for="rtlSwitch">RTL Mode</label>
            </div>
          </div>
        </div>
      </div>

      <!-- Setting Card-->
      <div class="card mb-3 shadow-sm">
        <div class="card-body direction-rtl">
          <p class="mb-2">Account Setup</p>

          <div class="single-setting-panel">
            <a href="user-profile.html">
              <div class="icon-wrapper">
                <i class="bi bi-person"></i>
              </div>
              Update Profile
            </a>
          </div>

          <div class="single-setting-panel">
            <a href="user-profile.html">
              <div class="icon-wrapper bg-warning">
                <i class="bi bi-pencil"></i>
              </div>
              Update Bio
            </a>
          </div>

          <div class="single-setting-panel">
            <a href="change-password.html">
              <div class="icon-wrapper bg-info">
                <i class="bi bi-lock"></i>
              </div>
              Change Password
            </a>
          </div>

          <div class="single-setting-panel">
            <a href="language.html">
              <div class="icon-wrapper bg-success">
                <i class="bi bi-globe2"></i>
              </div>
              Language
            </a>
          </div>

          <div class="single-setting-panel">
            <a href="privacy-policy.html">
              <div class="icon-wrapper bg-danger">
                <i class="bi bi-shield-lock"></i>
              </div>
              Privacy Policy
            </a>
          </div>
        </div>
      </div>

      <!-- Setting Card-->
      <div class="card shadow-sm">
        <div class="card-body direction-rtl">
          <p class="mb-2">Register &amp; Logout</p>

          <div class="single-setting-panel">
            <a href="register.html">
              <div class="icon-wrapper bg-primary">
                <i class="bi bi-person"></i>
              </div>
              Create New Account
            </a>
          </div>

          <div class="single-setting-panel">
            <a href="login.html">
              <div class="icon-wrapper bg-danger">
                <i class="bi bi-box-arrow-right"></i>
              </div>
              Logout
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li>
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li class="active">
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>