<?php
// Debug script para InteletGroup
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Headers para JSON
header('Content-Type: application/json');

// Log de debug
error_log("=== DEBUG INTELETGROUP PROSPECTO ===");
error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
error_log("POST data: " . print_r($_POST, true));

session_start();

// Verificar sesión
error_log("SESSION data: " . print_r($_SESSION, true));

try {
    // Verificar autenticación
    if (!isset($_SESSION['usuario_id'])) {
        throw new Exception('No hay usuario_id en sesión');
    }
    
    if (!isset($_SESSION['proyecto'])) {
        throw new Exception('No hay proyecto en sesión');
    }
    
    if ($_SESSION['proyecto'] !== 'inteletGroup') {
        throw new Exception('Proyecto no es inteletGroup: ' . $_SESSION['proyecto']);
    }

    // Verificar método POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no es POST: ' . $_SERVER['REQUEST_METHOD']);
    }

    // Verificar conexión a base de datos
    require_once 'con_db.php';
    
    if (!$conexion) {
        throw new Exception('No hay conexión a base de datos');
    }
    
    // Verificar que las tablas existen
    $tables_to_check = [
        'tb_inteletgroup_prospectos',
        'tb_inteletgroup_prospecto_bitacora',
        'tb_inteletgroup_documentos'
    ];
    
    $missing_tables = [];
    foreach ($tables_to_check as $table) {
        $result = $conexion->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows === 0) {
            $missing_tables[] = $table;
        }
    }
    
    if (!empty($missing_tables)) {
        throw new Exception('Faltan tablas: ' . implode(', ', $missing_tables));
    }

    // Obtener datos del formulario
    $usuario_id = $_SESSION['usuario_id'];
    $nombre_ejecutivo = trim($_POST['nombre_ejecutivo'] ?? '');
    $rut_cliente = trim($_POST['rut_cliente'] ?? '');
    
    if (empty($rut_cliente)) {
        throw new Exception('RUT cliente está vacío');
    }
    
    if (empty($nombre_ejecutivo)) {
        throw new Exception('Nombre ejecutivo está vacío');
    }

    // Verificar si el RUT ya existe
    $stmt = $conexion->prepare("SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ?");
    if (!$stmt) {
        throw new Exception('Error preparando consulta: ' . $conexion->error);
    }
    
    $stmt->bind_param("s", $rut_cliente);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        throw new Exception('Ya existe un prospecto con este RUT: ' . $rut_cliente);
    }

    echo json_encode([
        'success' => true,
        'message' => 'Debug exitoso - Todo está funcionando correctamente',
        'debug_info' => [
            'usuario_id' => $usuario_id,
            'proyecto' => $_SESSION['proyecto'],
            'rut_cliente' => $rut_cliente,
            'nombre_ejecutivo' => $nombre_ejecutivo,
            'tablas_verificadas' => $tables_to_check
        ]
    ]);

} catch (Exception $e) {
    error_log("ERROR en debug_inteletgroup_prospecto.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Error de debug: ' . $e->getMessage(),
        'debug_info' => [
            'session' => $_SESSION ?? 'No session',
            'post' => $_POST ?? 'No POST data',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'No method'
        ]
    ]);
}
?>
